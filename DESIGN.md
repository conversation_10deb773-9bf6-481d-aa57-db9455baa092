# JeopardyGPT Design Document

## Table of Contents
1. [Abstract Architecture](#abstract-architecture)
   - [System Overview](#system-overview)
   - [Core Components](#core-components)
   - [Data Model](#data-model)
   - [Communication Flow](#communication-flow)
2. [Detailed Implementation](#detailed-implementation)
   - [Frontend Architecture](#frontend-architecture)
   - [Backend Architecture](#backend-architecture)
   - [Database Design](#database-design)
   - [AI Integration](#ai-integration)
3. [Technology Stack](#technology-stack)
   - [Frontend Technologies](#frontend-technologies)
   - [Backend Technologies](#backend-technologies)
   - [Database](#database)
   - [AI Services](#ai-services)
   - [Deployment and Infrastructure](#deployment-and-infrastructure)
4. [Key Implementation Details](#key-implementation-details)
   - [Authentication Flow](#authentication-flow)
   - [Game State Management](#game-state-management)
   - [Real-time Updates](#real-time-updates)
   - [Question Generation](#question-generation)
   - [Answer Evaluation](#answer-evaluation)

## Abstract Architecture

### System Overview

JeopardyGPT is designed as a real-time, multi-user web application that follows a client-server architecture. The system is built around a central game state that is synchronized across all connected clients, with a focus on providing a responsive and engaging user experience.

The architecture follows these key principles:
- **Event-driven**: The system responds to user actions and game events
- **State-based**: All game logic is driven by transitions between well-defined states
- **Real-time**: Updates are propagated to all clients immediately
- **Scalable**: The system can handle multiple concurrent game rooms
- **Responsive**: The UI adapts to different device sizes and orientations

### Core Components

At an abstract level, the system consists of these core components:

1. **Client Application**
   - User Interface Layer: Renders the game state and captures user input
   - State Management Layer: Maintains local representation of game state
   - Communication Layer: Handles data exchange with the server

2. **Server Application**
   - API Layer: Exposes endpoints for client operations
   - Game Logic Layer: Implements game rules and state transitions
   - Data Access Layer: Manages persistence and retrieval of game data
   - AI Integration Layer: Interfaces with language models for question generation

3. **Database**
   - Stores user profiles, game rooms, and game states
   - Supports real-time updates and subscriptions

4. **AI Service**
   - Generates questions based on categories
   - Evaluates answers for correctness

### Data Model

The data model consists of these primary entities with detailed structure:

1. **User**
   ```typescript
   interface User {
     uid: string;              // Firebase Auth UID
     displayName: string;      // User's display name
     email?: string;          // User's email (optional)
     photoURL?: string;       // Profile picture URL
     isAnonymous: boolean;    // Whether user is anonymously authenticated
     createdAt: number;       // Timestamp of account creation
     lastLoginAt: number;     // Timestamp of last login
   }
   ```

2. **Room**
   ```typescript
   interface Room {
     roomId: string;          // Unique room identifier
     host: string;            // UID of the room creator/host
     mode: GameMode;          // TURN_BASED, BUZZER, or GAMEMASTER
     createdAt: number;       // Timestamp of room creation
     playerIds: string[];     // Array of player UIDs in the room
     playerInfos: PlayerInfo[]; // Array of player information objects
     categoryTitles?: string[]; // Array of category titles
     gameState: FullRoomState; // Current state of the game
     roomSettings?: RoomSettings; // Configuration options
   }

   interface PlayerInfo {
     score: number;           // Player's current score
     userName?: string;       // Player's display name
     photoUrl?: string;       // Player's profile picture URL
   }

   enum GameMode {
     TURN_BASED = 1,
     BUZZER = 2,
     GAMEMASTER = 3
   }

   interface RoomSettings {
     gameMode: GameMode;      // Current game mode
     buzzInTimerDurationMillis: number;     // Time allowed for buzzing in
     answerDurationMillis: number;          // Time allowed for answering
     showAnswerDurationMillis: number;      // Time to display answer before moving on
     initialThinkingDurationMillis: number;  // Extra time for first player in turn-based mode
     llmAgent: LlmAgent;      // AI model configuration
     numQuestions?: number;   // Number of questions per category
   }

   interface LlmAgent {
     provider: "OpenAI" | "Gemini" | "DeepSeekAI" | "Fake";
     modelName: string;       // Specific model identifier
   }
   ```

3. **Game State**
   ```typescript
   interface FullRoomState {
     gameProgress: GameProgress; // Current game progress state
   }

   type GameProgress =
     | WaitingRoom
     | GeneratingQuestions
     | RoomState
     | RoomSummary;

   interface WaitingRoom {
     type: "WaitingRoom";
   }

   interface GeneratingQuestions {
     type: "GeneratingQuestions";
     progress: number;        // Progress percentage (0-100)
     message: string;         // Current status message
     category?: string;       // Category being generated
     questionIndex?: number;  // Question index being generated
     startTime: number;       // Generation start timestamp
   }

   interface RoomState {
     type: "RoomState";
     roundIdx: number;        // Current round index
     roundStates: RoundState[]; // States for all rounds
     currentPlayerIdx: number; // Index of current player
   }

   interface RoomSummary {
     type: "RoomSummary";
   }

   interface RoundState {
     currentCategoryIdx?: number; // Currently selected category
     currentClueIdx?: number;    // Currently selected clue
     categoryStates: CategoryState[]; // States for all categories
   }

   interface CategoryState {
     clueStates: ClueState[];  // States for all clues in this category
   }
   ```

4. **Clue**
   ```typescript
   interface Clue {
     questionSentences: string[]; // Array of question sentences
     questionHTML: string;      // HTML-formatted question text
     hint: string;              // Hint text
     hintBlanks: string;        // Representation of answer with blanks
     answer: string;            // Correct answer
     value: number;             // Point value
     detailedFactsAboutAnswer: string[]; // Educational facts
     isGoodClue?: boolean;      // Quality flag
   }

   interface Category {
     categoryTitle: string;     // Category name
     categoryDescription?: string; // Category description
     clues: Clue[];            // Array of clues in this category
   }

   interface Round {
     categories: Category[];    // Array of categories in this round
   }
   ```

5. **Clue State**
   ```typescript
   interface ClueState {
     hintOpened: boolean;      // Whether hint is revealed
     showAnswer: boolean;      // Whether answer is revealed
     buzzedInPlayerQueue: string[]; // Ordered list of players who buzzed in
     passedPlayers: string[];  // Players who passed their turn
     queueAnswerTurnIdx: number; // Current position in buzz-in queue
     buzzInTimerStartTime?: number; // When buzz-in timer started
     answerStartTime?: number; // When answer timer started
     showAnswerStartTime?: number; // When show-answer timer started
     clueComplete?: boolean;   // Whether clue has been completed
     clueLog: ClueLog;         // History of actions
     answerExplanations: AnswerExplanation[]; // Feedback on answers
   }

   interface ClueLog {
     logs: string[];           // Array of log messages
   }

   interface AnswerExplanation {
     isCorrect: boolean;       // Whether answer was correct
     explanation: string;      // Explanation text
   }
   ```

6. **Chat Message**
   ```typescript
   interface ChatMessage {
     messageId: string;        // Unique message identifier
     roomId: string;           // Room this message belongs to
     senderId: string;         // UID of message sender
     senderName: string;       // Display name of sender
     senderPhotoUrl?: string;  // Profile picture of sender
     content: string;          // Message text content
     timestamp: number;        // When message was sent
     type: "user" | "system"; // Message type
     mentions?: string[];      // UIDs of mentioned users
     isRead: Record<string, boolean>; // Read status by user
   }
   ```

7. **User Settings**
   ```typescript
   interface AppSettings {
     theme: "light" | "dark"; // UI theme preference
     soundEnabled: boolean;    // Whether TTS is enabled
     voiceUri: string;         // Selected TTS voice
   }
   ```

### Communication Flow

The abstract communication flow follows these patterns:

1. **Room Creation and Setup**
   - Client requests room creation
   - Server generates room ID and initializes state
   - Client receives room details and transitions to waiting room
   - Other clients join using room ID
   - Host configures game settings
   - Settings updates are broadcast to all clients

2. **Game Initialization**
   - Host triggers game start
   - Server transitions to question generation state
   - AI service generates questions based on categories
   - Progress updates are sent to all clients
   - Server transitions to active gameplay when complete

3. **Gameplay Interaction**
   - Player selects clue (or waits for turn)
   - Server updates game state and broadcasts to all clients
   - Players buzz in or answer based on game mode
   - Server validates actions and updates state
   - Score and state changes are broadcast to all clients

4. **Game Conclusion**
   - Host ends game or all clues are answered
   - Server calculates final scores and statistics
   - Game transitions to summary state
   - Results are displayed to all players

## Detailed Implementation

### Frontend Architecture

The frontend is implemented as a single-page application (SPA) with a component-based architecture. The application is organized into the following layers:

1. **Presentation Layer**
   - Components: Reusable UI elements that render specific parts of the interface
   - Views: Screen-level components that combine multiple components
   - Styles: Design system implementation with responsive layouts

2. **State Management Layer**
   - Local State: Component-specific state for UI interactions
   - Application State: Shared state for cross-component communication
   - Remote State: Synchronized with the server via real-time updates

3. **Service Layer**
   - API Services: Handle communication with backend endpoints
   - Authentication Service: Manages user identity and session
   - Real-time Service: Maintains WebSocket or similar connection for live updates
   - Game Service: Coordinates game-specific operations

4. **Utility Layer**
   - Helpers: Common utility functions
   - Constants: Application-wide constants and enumerations
   - Types: Type definitions for TypeScript

### Backend Architecture

The backend follows a serverless, function-based architecture with these key components:

1. **API Functions**
   - Room Management: Functions for creating, joining, and configuring rooms
   - Game Flow: Functions for starting games, selecting clues, and ending games
   - Player Actions: Functions for buzzing in, submitting answers, and passing

2. **Game Logic**
   - State Machine: Manages transitions between game states
   - Rule Enforcement: Implements game rules and validates player actions
   - Scoring: Calculates and updates player scores
   - Timer Management: Handles time-based events and expirations

3. **AI Integration**
   - Question Generation: Creates categories and clues based on user input
   - Answer Validation: Evaluates player answers for correctness
   - Hint Generation: Creates helpful hints for players

4. **Data Access**
   - Database Operations: CRUD operations for game data
   - Real-time Updates: Publishes changes to subscribed clients
   - Transaction Management: Ensures data consistency during concurrent operations

### Database Design

The database is designed around a document-based model with the following collections:

1. **Users Collection**
   - User profiles and authentication information
   - Preferences and settings

2. **Rooms Collection**
   - Room documents containing:
     - Room metadata
     - Player information
     - Game settings
     - Current game state
   - Subcollections for rounds, categories, and clues

3. **Game History Collection**
   - Completed game records
   - Player performance statistics

### AI Integration

The AI integration is implemented through a service-oriented approach:

1. **Question Generation Service**
   - Accepts category titles and configuration
   - Generates appropriate questions with varying difficulty
   - Returns structured clue data with questions, hints, and answers

2. **Answer Evaluation Service**
   - Compares player answers to correct answers
   - Handles variations, synonyms, and minor errors
   - Provides explanations for correctness or incorrectness

## Technology Stack

### Frontend Technologies

1. **Framework and Core Libraries**
   - **Angular**: Frontend framework for building the single-page application
   - **TypeScript**: Programming language for type-safe development
   - **RxJS**: Library for reactive programming using Observables

2. **UI Components and Styling**
   - **Angular Material**: UI component library following Material Design guidelines
   - **SCSS**: CSS preprocessor for styling components
   - **Material Design 3**: Design system for consistent visual language

3. **State Management**
   - **Angular Services**: For managing application state
   - **RxJS Subjects and Observables**: For reactive state management

4. **Build and Development Tools**
   - **Angular CLI**: Command-line interface for Angular development
   - **Webpack**: Module bundler for application packaging
   - **ESLint**: Code quality and style checking

### Backend Technologies

1. **Serverless Platform**
   - **Firebase Functions**: Cloud functions for serverless backend logic
   - **Node.js**: JavaScript runtime for executing server-side code
   - **TypeScript**: Type-safe programming language for backend development

2. **API and Communication**
   - **Firebase Callable Functions**: Method for exposing backend functionality to clients
   - **Firebase Authentication**: User identity and access control

3. **Game Logic**
   - **Custom state machine implementation**: For managing game state transitions
   - **Server-side validation**: For enforcing game rules and preventing cheating

### Database

1. **Cloud Database**
   - **Firebase Firestore**: NoSQL document database for storing game data
   - **Real-time listeners**: For pushing updates to clients
   - **Security rules**: For controlling access to data

2. **Data Structure**
   - Document-based storage with collections and subcollections
   - Denormalized data for efficient access patterns
   - Optimized for real-time updates

### AI Services

1. **Language Model Integration**
   - **OpenAI API**: For GPT-4 and other OpenAI models
   - **Google Gemini API**: For Gemini models
   - **DeepSeekAI API**: For DeepSeek models

2. **Custom Prompting and Processing**
   - Specialized prompts for generating Jeopardy-style questions
   - Post-processing for formatting and quality control
   - Answer comparison algorithms for flexible matching

### Deployment and Infrastructure

1. **Hosting and Delivery**
   - **Firebase Hosting**: For serving the frontend application
   - **Cloud Functions**: For serverless backend execution
   - **Content Delivery Network (CDN)**: For global distribution

2. **Monitoring and Analytics**
   - **Firebase Analytics**: For tracking user behavior and app performance
   - **Error logging**: For capturing and addressing issues
   - **Performance monitoring**: For optimizing application speed

## Key Implementation Details

### Authentication Flow

1. **User Registration and Login**
   - Users can register with email/password or OAuth providers
   - Authentication state is maintained across sessions
   - Anonymous play is supported for quick access

2. **Room Access Control**
   - Room creators automatically become hosts with admin privileges
   - Players must authenticate to join rooms
   - Host can control who can join the room

### Game State Management

1. **State Machine Implementation**
   - Game progress is modeled as a state machine with defined transitions
   - Each state has specific allowed actions and validation rules
   - State transitions trigger appropriate UI updates and game logic

2. **Concurrency Handling**
   - Database transactions ensure consistency during concurrent operations
   - Optimistic UI updates provide responsive experience
   - Conflict resolution strategies handle edge cases

### Real-time Updates

1. **Firestore Real-time Listeners**
   - Clients subscribe to room documents for live updates
   - Changes are pushed to all connected clients immediately
   - Efficient delta updates minimize data transfer

2. **UI Synchronization**
   - Component hierarchy reflects data structure
   - Reactive programming patterns propagate changes through the application
   - Animations provide visual feedback for state changes

### Question Generation

1. **Prompt Engineering**
   - Carefully crafted prompts guide AI models to generate appropriate questions
   - Category-specific context improves relevance and quality
   - Difficulty scaling ensures appropriate challenge levels

2. **Quality Control**
   - Generated questions are validated for format and content
   - Filtering removes inappropriate or low-quality questions
   - Fallback mechanisms handle generation failures

### Answer Evaluation

1. **Intelligent Matching**
   - String normalization (case, punctuation, whitespace)
   - Edit distance algorithms for spelling variations
   - Synonym expansion for alternative phrasings
   - Special handling for numbers, dates, and proper nouns

2. **Feedback Generation**
   - Contextual explanations for correct and incorrect answers
   - Educational facts provide additional information
   - Hint generation helps players learn from mistakes
