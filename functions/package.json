{"name": "functions", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions,database,firestore,auth", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "mocha -r ts-node/register src/test/*.test.ts"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"@azure-rest/ai-inference": "^1.0.0-beta.5", "@azure/core-auth": "^1.9.0", "@azure/core-sse": "^2.1.3", "@google/generative-ai": "^0.21.0", "dotenv": "^16.4.7", "firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "js-levenshtein": "^1.1.6", "openai": "^4.77.0", "ts-node": "^10.9.2"}, "devDependencies": {"@types/js-levenshtein": "^1.1.3", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0", "typescript": "^4.9.0"}, "private": true}