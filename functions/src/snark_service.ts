import {LogEntry, LogType} from "./structured_log";

/**
 * Generates a snarky comment for a structured log entry
 *
 * @param logEntry The structured log entry
 * @return A snarky comment
 */
export function generateSnarkyComment(logEntry: LogEntry): string {
  // Arrays of snarky comments for different log types
  const correctAnswerComments = [
    "Correct! {Player}, even a broken clock is right twice a day.",
    "Right answer, {player}! Did you actually know that or was it a lucky guess?",
    "Correct! {Player} shows useless knowledge finally paid off.",
    "Right! {Player} must have been reading the encyclopedia for fun.",
    "Correct! {Player}'s brain cells are finally earning their keep.",
    "Right answer! {Player} must be fun at pub quizzes.",
    "Correct! Impressive for {player} who still uses a calculator for basic math.",
    "Right! Maybe {player}'s hours of random internet browsing weren't wasted after all.",
    "Correct! {Player}'s trivial knowledge has momentarily outweighed their trivial existence.",
    "Right answer! {Player}'s teachers would be shocked.",
    "Correct! {Player} has been saving that fact since 3rd grade, haven't they?",
    "Right! I bet {player} has been waiting their whole life to use that information.",
    "Correct! {Player}'s brain must be sore from that workout.",
    "Right answer! {Player} has been hiding their intelligence well until now.",
    "Correct! I'm as surprised as {player} is.",
    "Right! Who knew all {player}'s Wikipedia rabbit holes would pay off?",
    "{Player} is correct! They've temporarily raised the room's average IQ.",
    "Right answer, {player}! Don't let it go to your head.",
    "Correct! Even {player} can be right sometimes.",
    "Right! The law of averages finally worked in {player}'s favor.",
  ];

  const incorrectAnswerComments = [
    "Wrong, {player}! That answer was so off, it's in a different zip code.",
    "{Player}, incorrect. Maybe trivia isn't your thing. Have you considered knitting?",
    "Wrong answer, {player}. Did you even read the question?",
    "Incorrect, {player}. Your confidence was impressive, though!",
    "Wrong! At least {player} tried... sort of.",
    "Incorrect. The correct answer was obvious to everyone but {player}.",
    "{Player}, wrong answer. Don't worry, being average is perfectly acceptable.",
    "{Player}, incorrect. Your guess was creative, I'll give you that.",
    "Wrong! That's what happens when {player} confuses Google with their brain.",
    "Incorrect, {player}. Maybe stick to questions about your own name next time.",
    "Wrong answer from {player}. The silence is everyone trying not to laugh.",
    "Incorrect, {player}. Your answer belongs in a different trivia game. Or possibly a different universe.",
    "Wrong! {Player}'s answer was so bad, the question is considering retirement.",
    "Incorrect. I can hear {player}'s brain cells apologizing to each other.",
    "{Player}'s set a new standard for being confidently incorrect.",
    "Incorrect. {Player}'s knowledge has more gaps than Swiss cheese.",
    "Wrong! If ignorance is bliss, {player} must be ecstatic right now.",
    "Incorrect. {Player}'s answer just made historians cry.",
    "Wrong answer, {player}. I'd tell you to guess again, but I fear what you might say.",
    "Incorrect. {Player}'s answer was so wrong it actually made the right answer less correct.",
  ];

  const passComments = [
    "{Player} is passing? Wise choice. Better to remain silent and be thought a fool than to answer and remove all doubt.",
    "{Player} is skipping this one? I guess \"I don't know\" is technically less wrong than whatever they were going to say.",
    "{Player} is passing... the strategic choice when their knowledge passes out.",
    "{Player} is taking a pass? Probably for the best.",
    "{Player} is passing? That's one way to maintain their dignity.",
    "{Player} is skipping? Their silence speaks volumes about their knowledge on this topic.",
    "{Player} is passing this question? Smart move - why risk being wrong when they can just admit defeat?",
    "{Player} is taking a pass? I respect their decision to avoid embarrassment.",
    "{Player} is passing? Sometimes the wisest answer is no answer at all.",
    "{Player} is skipping? I guess we'll never know what creative wrong answer they had in mind.",
    "{Player} is passing on this one? Saving their energy for questions they might actually know?",
    "{Player} is taking a pass? The question thanks them for their mercy.",
    "{Player} is passing? That's the trivia equivalent of \"I'll call you back\" - we all know what it means.",
    "{Player} is skipping? Their strategic retreat has been noted.",
    "{Player} is passing? Sometimes the best contribution is no contribution at all.",
    "{Player} is taking a pass? Probably the most correct decision they've made all game.",
    "{Player} is passing? At least they know what they don't know.",
    "{Player} is skipping this question? The void of their knowledge has never been so apparent.",
    "{Player} is passing? Their restraint is admirable.",
    "{Player} is taking a pass? Wise. Very wise.",
  ];

  const noAnswerComments = [
    "No one got it? I guess everyone left their brains at home today.",
    "No one knew the answer? Shocking. Absolutely shocking.",
    "Collective ignorance is still ignorance, folks.",
    "Everyone failed that one. Group incompetence at its finest!",
    "No one got it right. The question wasn't THAT hard, was it?",
    "The sound of crickets is deafening right now.",
    "That awkward moment when everyone's equally clueless.",
    "The question stumped everyone. Maybe try \"Name That Color\" next time?",
    "No correct answers? This is why we can't have nice things.",
    "Everyone missed that one. Maybe try a category on \"Things Everyone Knows\"?",
    "No one got it right. The trivia gods are displeased.",
    "The question defeated everyone. Darwin would be disappointed.",
    "No correct answers. I'm starting to think this is a group of AI bots failing the Turing test.",
    "Everyone missed it! At least you're all consistently wrong together.",
    "No one knew this one. Shared ignorance is still ignorance, but at least you have company!",
  ];

  const hintComments = [
    "Opening a hint? I guess you need all the help you can get.",
    "Here's a hint since you're all struggling so much.",
    "Hint time! Because that last round was just embarrassing for everyone.",
    "Need a hint? Of course you do.",
    "Here's a hint. Try not to overthink it like last time.",
    "Hint activated! Now you only have to be half as smart to get it right.",
    "Opening hint... lowering the bar to accommodate current skill levels.",
    "Here's a hint since this question is clearly exceeding the room's collective IQ.",
    "Hint time! Now you only get half points, but that's better than the zero you were headed for.",
    "Deploying hint because watching you all struggle was getting painful.",
    "Here's a hint. It's like training wheels for your brain.",
    "Hint revealed! Now the question is only moderately challenging instead of impossible.",
    "Opening hint because apparently this question is quantum physics to you all.",
    "Here's a hint. Use it wisely, unlike your previous answers.",
    "Hint time! Let's see if this helps or if we need to make the questions even simpler.",
  ];

  const buzzInComments = [
    "{Player} buzzed in so fast their finger might need ice afterward.",
    "{Player} buzzed in! Either they know it or they're very optimistic about guessing.",
    "That was a suspiciously quick buzz from {player}. Let's see if the confidence is warranted.",
    "{Player} buzzed in like they actually know the answer. This should be interesting.",
    "{Player} buzzed in! The speed of wrongness is still impressive.",
    "{Player} buzzed so fast it was quicker than their thought process, probably.",
    "{Player} buzzed in with the confidence of someone who might actually be correct this time.",
    "{Player} buzzed so fast it broke the sound barrier. Let's see if their answer is as impressive.",
    "{Player} buzzed in! Now comes the hard part: actually knowing the answer.",
    "{Player} buzzed with either knowledge or delusion. Place your bets!",
    "{Player} buzzed in with shocking speed. Now let's see if there's any substance behind that reflex.",
    "{Player} buzzed impressively. Let's see if their answer matches their enthusiasm.",
    "{Player} buzzed in! Either a stroke of genius or a stroke of something else entirely.",
    "{Player} buzzed confidently. Confidence and correctness aren't always related though.",
    "{Player} buzzed in faster than they can probably think. Bold strategy.",
  ];

  const waitingForAnswerComments = [
    "Now waiting for {player} to answer... or make an educated guess... or produce random syllables...",
    "Waiting for {player} to answer. The suspense is... actually not that great.",
    "Now waiting while {player} desperately searches their memory banks.",
    "Waiting for {player} to answer. You can almost hear the gears grinding.",
    "Now waiting for what will surely be a fascinating attempt at an answer from {player}.",
    "Waiting for {player} to answer. The clock is ticking, and so is everyone's patience.",
    "Now waiting while {player} tries to remember if they actually know this or not.",
    "Waiting for {player} to answer that's probably forming very slowly right now.",
    "Now waiting for what might be brilliance or might be complete nonsense from {player}.",
    "Waiting for {player} to answer. The longer it takes, the less confident I feel about it.",
    "Now waiting while {player} debates whether to embarrass themselves or pass.",
    "{Player} is taking longer to formulate an answer than it should.",
    "Now waiting for what will likely be a creative interpretation of the correct answer from {player}.",
    "{Player} is pausing dramatically, but it isn't building confidence.",
    "Now waiting while {player} tries to remember something they probably never knew.",
  ];

  const clueSelectedComments = [
    "Clue selected! Let's see if anyone actually knows this one.",
    "Selected a clue that will probably stump everyone.",
    "Clue chosen! Prepare for collective confusion.",
    "Selected a clue. May the odds of someone knowing this be ever in your favor.",
    "Clue picked! Time to showcase how little everyone knows about this topic.",
    "Selected a clue that will separate the trivia masters from... well, everyone here.",
    "Clue chosen! Let's see who pretends to know this one.",
    "Selected a clue. Cue the sound of brains struggling.",
    "Clue picked! Watch as confidence turns to confusion in real time.",
    "Selected a clue that might as well be in another language for most of you.",
    "Clue chosen! Time to find out who actually paid attention in school.",
    "Selected a clue. Prepare for the awkward silence of collective ignorance.",
    "Clue picked! Let's see who buzzes in only to immediately regret it.",
    "Selected a clue that will likely result in some creative \"almost correct\" answers.",
    "Clue chosen! Time to separate those who know from those who think they know.",
  ];

  const timerExpiredComments = [
    "Time's up for {player}! Thinking isn't supposed to take that long.",
    "{Player} ran out of time. Turns out, staring blankly doesn't count as answering.",
    "Time's up for {player}! That's what happens when you buzz in before your brain is ready.",
    "{Player} ran out of time. Silence isn't actually a valid answer strategy.",
    "Time's up for {player}! Apparently the question was too difficult for human comprehension.",
    "{Player} ran out of time. Next time, try forming words with your mouth.",
    "Time's up for {player}! The awkward silence was answer enough.",
    "{Player} ran out of time. I guess the answer got lost somewhere between their brain and mouth.",
    "Time's up for {player}! Buzzing in is only step one. Step two is actually answering.",
    "{Player} ran out of time. That's one way to avoid being wrong - just don't answer at all!",
    "Time's up for {player}! Their blank stare has been noted as an incorrect answer.",
    "{Player} ran out of time. The \"deer in headlights\" approach rarely works in trivia.",
    "Time's up for {player}! Next time, try having the answer ready before you buzz in.",
    "{Player} ran out of time. Their silence speaks volumes about their knowledge on this topic.",
    "Time's up for {player}! Contemplating the meaning of life instead of answering, I see.",
  ];

  const pointsAssignedComments = [
    "{Player}, don't spend them all in one place.",
    "Congratulations on your momentary triumph in this trivial pursuit, {player}.",
    "Points added to {player}'s meaningless total.",
    "{Player} gets a score increase. Their life satisfaction remains the same.",
    "Points awarded to {player}! Frame this moment; it might be your peak.",
    "The scoreboard acknowledges {player} with mild interest.",
    "Points given to {player}. The universe remains indifferent.",
    "{Player} gets a score boost, much like their ego.",
    "Points added for {player}. Try not to let it go to your head.",
    "Congratulations, {player}! These points have no cash value.",
    "{Player} gets points. They're almost as valuable as Monopoly money.",
    "{Player}'s trivial knowledge has been duly recorded on the scoreboard.",
    "{Player} gets points. They can't be exchanged for actual knowledge.",
    "{Player}'s score updated. No refunds or exchanges.",
    "{Player} gets points. They're worth exactly what they paid for them.",
  ];

  const noPointsAssignedComments = [
    "No points awarded. I guess everyone was equally unimpressive.",
    "Zero points. That's consistent with expectations.",
    "No points this time. Keep trying, or don't. It's your life.",
    "Points awarded: zero. Just like the contribution to this round.",
    "No points. The scoreboard remains as unimpressed as everyone else.",
    "Zero points. At least there's consistency.",
    "No points. The question remains unanswered, much like potential.",
    "Points not awarded. The void acknowledges the effort.",
    "Zero points. Some questions are better left to those who know things.",
    "No points. Perhaps trivia isn't the right calling.",
  ];

  // Helper function to get a random comment from an array
  const getRandomComment = (comments: string[]): string => {
    return comments[Math.floor(Math.random() * comments.length)];
  };

  // Helper function to personalize comments with player name
  const personalizeComment = (comment: string, playerName: string): string => {
    // Replace placeholders with player name
    return comment
      .replace(/\{player\}/g, playerName)
      .replace(
        /\{Player\}/g,
        playerName.charAt(0).toUpperCase() + playerName.slice(1)
      );
  };

  // Generate a snarky comment based on the log type
  switch (logEntry.type) {
  case LogType.CLUE_SELECTED: {
    const comment = getRandomComment(clueSelectedComments);
    return personalizeComment(comment, logEntry.playerName);
  }

  case LogType.BUZZ_IN: {
    const comment = getRandomComment(buzzInComments);
    return personalizeComment(comment, logEntry.playerName);
  }

  case LogType.ANSWER_CORRECT: {
    const comment = getRandomComment(correctAnswerComments);
    return personalizeComment(comment, logEntry.playerName);
  }

  case LogType.ANSWER_INCORRECT: {
    const comment = getRandomComment(incorrectAnswerComments);
    return personalizeComment(comment, logEntry.playerName);
  }

  case LogType.PASS: {
    const comment = getRandomComment(passComments);
    return personalizeComment(comment, logEntry.playerName);
  }

  case LogType.TIMER_EXPIRED: {
    const comment = getRandomComment(timerExpiredComments);
    return personalizeComment(comment, logEntry.playerName);
  }

  case LogType.HINT_OPENED:
    return getRandomComment(hintComments);

  case LogType.NO_CORRECT_ANSWER:
    return getRandomComment(noAnswerComments);

  case LogType.WAITING_FOR_ANSWER: {
    const comment = getRandomComment(waitingForAnswerComments);
    return personalizeComment(comment, logEntry.playerName);
  }

  case LogType.SHOW_ANSWER:
    return `The answer was: ${logEntry.answer}. Better luck next time, geniuses.`;

  case LogType.POINTS_ASSIGNED: {
    const name = logEntry.playerName || logEntry.teamName || "No one";
    if (name === "No one" || logEntry.pointsAwarded <= 0) {
      const comment = getRandomComment(noPointsAssignedComments);
      return comment; // No personalization for no points
    } else {
      const comment = getRandomComment(pointsAssignedComments);
      return personalizeComment(comment, name);
    }
  }

  default:
    return `Unknown log entry type: ${(logEntry as any).type}`;
  }
}
