import {HttpsError} from "firebase-functions/https";
import {Room, Clue, Round} from "./resources";
import {RegenerateClueRequest} from "./services";
import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {
  createHintBlanksFromAnswer,
  extractLastJsonObject,
  runPrompt,
} from "./round_generator";

interface LlmResponse {
  questionSentences: Array<string>;
  questionHTML: string;
  hint: string;
  answer: string;
  value: number;
  detailedFactsAboutAnswer: string[];
}

export const regenerateClue = functions.https.onCall(
  async (data: any, _context: any) => {
    // TODO: Add validation.
    const req = data.data as RegenerateClueRequest;
    const roomId = req.roomId;
    const roomRef = admin
      .firestore()
      .collection("rooms")
      .doc(`${roomId}`);
    const roundRef = roomRef.collection("roomRoundData").doc(`${req.roundIdx}`);
    const roundDoc = await roundRef.get();
    if (!roundDoc.exists) {
      throw new HttpsError("not-found", "Room+round not found");
    }
    const roundData = roundDoc.data() as Round;

    const maxClueValue = Math.max(
      ...roundData.categories[req.categoryIdx].clues.map((clue) => clue.value)
    );
    const points =
      roundData.categories[req.categoryIdx].clues[req.clueIdx].value;
    const prompt = `Generate one trivia question for the category "${
      roundData.categories[req.categoryIdx].categoryTitle
    }" based on verifiable facts with the following strict requirements:
            - Difficulty level being ${points} out of ${maxClueValue}.
            - The question should be creative, engaging, and encourage reasoning.
            - The answer must be factually correct, specific, and easily verifiable.
            - Avoid overly niche or ambiguous topics; prioritize globally recognized facts.
            - Include a concise hint to guide deduction without giving the answer away.
            - Ensure logical consistency between the question, hint, and answer.
            - Use JSON format to output the response:
              [
                {
                  "detailedFactsAboutAnswer": ["5 interesting facts related to the category."]
                  "questionHTML": "...",
                  "hint": "Hint for the question",
                  "answer": "Correct answer",
                  "value": ${points},
                }
              ]`;
    const room = (await roomRef.get()).data() as Room;

    const rawResponse = await runPrompt(prompt, room.roomSettings!.llmAgent);
    const parsedResponse = JSON.parse(
      extractLastJsonObject(rawResponse)!
    ) as LlmResponse[];
    const newClue: Clue = {
      questionSentences: [],
      questionHTML: parsedResponse[0].questionHTML,
      hint: parsedResponse[0].hint,
      answer: parsedResponse[0].answer,
      value: parsedResponse[0].value,
      detailedFactsAboutAnswer: parsedResponse[0].detailedFactsAboutAnswer,
      hintBlanks: createHintBlanksFromAnswer(parsedResponse[0].answer, 30),
    };

    return admin.firestore().runTransaction(async (txn) => {
      const roundDoc = await txn.get(roundRef);
      if (!roundDoc.exists) {
        throw new HttpsError("not-found", "Room+round not found");
      }
      const round = roundDoc.data() as Round;
      round.categories[req.categoryIdx].clues[req.clueIdx] = newClue;
      await txn.set(roundRef, round);
    });
  }
);
