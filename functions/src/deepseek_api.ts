/*
Run this model in Javascript

> npm install @azure-rest/ai-inference @azure/core-auth @azure/core-sse
*/
import createClient, {ChatCompletionsOutput} from "@azure-rest/ai-inference";
import {AzureKeyCredential} from "@azure/core-auth";

// To authenticate with the model you will need to generate a personal access token (PAT) in your GitHub settings.
// Create your PAT token by following instructions here: https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens
const token = process.env["GITHUB_TOKEN"];
const client = createClient(
  "https://models.inference.ai.azure.com",
  new AzureKeyCredential(token!)
);

export async function queryDeepSeekAI(
  prompt: string,
  _llmAgent: string
): Promise<any> {
  try {
    console.log(prompt);
    const response = await client.path("/chat/completions").post({
      body: {
        messages: [
          {
            role: "system",
            content:
              "You are a helpful assistant built to generate jeopardy style questions and return response in JSON format.",
          },
          {role: "user", content: prompt},
        ],
        model: "DeepSeek-R1",
        max_tokens: 2048,
      },
    });
    console.log(response);
    const text = (response.body as ChatCompletionsOutput).choices[0]?.message
      ?.content;
    console.log("Raw response from OpenAI API:", text);
    return text;
  } catch (error) {
    console.error("Error querying OpenAI API:", error);
    throw new Error("Failed to fetch or parse response from OpenAI API");
  }
}
