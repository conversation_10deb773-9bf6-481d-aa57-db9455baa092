import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import {RegisterPlayerRequest} from "./services";
export const registerPlayer = functions.https.onCall(
  async (data: any, _context: any) => {
    const req = data.data as RegisterPlayerRequest;
    const db = admin.firestore();
    await db
      .collection("players")
      .doc(data.auth.uid!)
      .set({
        userName: req.userName,
      });
  }
);
