require('dotenv').config();
import * as admin from 'firebase-admin';
import { joinRoom } from './join_room';
import { createRoom } from './create_room';
import { startRoom } from './start_room';
import { selectClue } from './select_clue';
import { buzzIn } from './buzz_in';
import { submitGuess } from './submit_guess';
import { pingRoom } from './ping_room';
import { registerPlayer } from './register_player';
import { assignPoints } from './assign_points';
import { regenerateClue } from './regenerate_clue';
import { skipShowAnswer } from './skip_show_answer';
import { generateSubCategories } from './subcategory_generator';
import { gameMasterActions } from './gamemaster_actions';
import { updateRoomSettings } from './update_room_settings';
import { updateGenerationProgress } from './update_generation_progress';
import { startGenerationPhase } from './start_generation_phase';
import { revertToGameState } from './revert_to_game_state';
import { getRoundData } from './get_round_data';
import { updateQuestion } from './update_question';

admin.initializeApp();

exports.joinRoom = joinRoom;
exports.createRoom = createRoom;
exports.startRoom = startRoom;
exports.selectClue = selectClue;
exports.submitGuess = submitGuess;
exports.pingRoom = pingRoom;
exports.buzzIn = buzzIn;
exports.assignPoints = assignPoints;
exports.regenerateClue = regenerateClue;
exports.registerPlayer = registerPlayer;
exports.skipShowAnswer = skipShowAnswer;
exports.generateSubCategories = generateSubCategories;
exports.gameMasterActions = gameMasterActions;
exports.updateRoomSettings = updateRoomSettings;
exports.updateGenerationProgress = updateGenerationProgress;
exports.startGenerationPhase = startGenerationPhase;
exports.revertToGameState = revertToGameState;
exports.getRoundData = getRoundData;
exports.updateQuestion = updateQuestion;
