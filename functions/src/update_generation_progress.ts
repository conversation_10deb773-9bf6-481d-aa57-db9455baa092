import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {HttpsError} from "firebase-functions/https";
import {Room} from "./resources";

interface UpdateGenerationProgressRequest {
  roomId: string;
  progress: number;
  message: string;
  category?: string;
  questionIndex?: number;
}

function validate(
  roomData: Room,
  _data: any,
  _req: UpdateGenerationProgressRequest,
  _nowTs: number
) {
  // Only the host can update the generation progress
  if (_data.auth?.uid !== roomData.host) {
    throw new HttpsError(
      "permission-denied",
      "Only the host can update the generation progress"
    );
  }

  // Ensure the room is in the GeneratingQuestions state
  if (roomData.gameState.gameProgress.type !== "GeneratingQuestions") {
    throw new HttpsError(
      "failed-precondition",
      "Room is not in the GeneratingQuestions state"
    );
  }
}

function modify(
  roomData: Room,
  req: UpdateGenerationProgressRequest,
  _nowTs: number
): Room {
  // Update the generation progress in the room state
  const generatingState = roomData.gameState.gameProgress as any;
  generatingState.progress = req.progress;
  generatingState.message = req.message;
  generatingState.category = req.category;
  generatingState.questionIndex = req.questionIndex;

  return roomData;
}

export const updateGenerationProgress = functions.https.onCall(
  async (data: any, _context: any) => {
    const req = data.data as UpdateGenerationProgressRequest;
    const db = admin.firestore();

    return db.runTransaction(async (txn) => {
      const roomRef = db.collection("rooms").doc(req.roomId);
      const roomDoc = await txn.get(roomRef);

      if (!roomDoc.exists) {
        throw new HttpsError("not-found", "Room not found");
      }

      const roomData = roomDoc.data() as Room;
      const nowTs = Date.now();

      validate(roomData, data, req, nowTs);
      const updatedRoom = modify(roomData, req, nowTs);

      await txn.set(roomRef, updatedRoom);
      return updatedRoom;
    });
  }
);
