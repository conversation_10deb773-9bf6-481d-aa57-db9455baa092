import {StartRoomRequest} from "./services";
import {HttpsError} from "firebase-functions/https";
import {ClueState, GameMode, Room, Round, RoundState} from "./resources";
import {generateRoundSimple} from "./round_generator";
import * as admin from "firebase-admin";
import * as functions from "firebase-functions";

function validate(
  currentData: Room,
  data: any,
  req: StartRoomRequest,
  _nowTs: number
) {
  const roomData = currentData as Room;

  if (!req.roomId || !data.auth.uid!) {
    new HttpsError("invalid-argument", "Room ID and player ID are required");
  }

  // Check if the player starting the game is the host
  if (roomData.host !== data.auth.uid!) {
    throw new HttpsError(
      "failed-precondition",
      "Only the host can start the game"
    );
  }

  // Check if the game is in waiting or generating questions state
  if (
    roomData.gameState.gameProgress.type !== "WaitingRoom" &&
    roomData.gameState.gameProgress.type !== "GeneratingQuestions"
  ) {
    throw new HttpsError(
      "failed-precondition",
      "Game already started or ended"
    );
  }
}

function createInitialClueState(): ClueState {
  return {
    hintOpened: false,
    showAnswer: false,
    buzzedInPlayerQueue: [],
    passedPlayers: [],
    queueAnswerTurnIdx: 0,
    clueLog: {
      logs: [],
    },
    structuredClueLog: {
      entries: [],
    },
    answerExplanations: [],
  };
}

function modify(
  roomData: Room,
  rounds: Round[],
  req: StartRoomRequest,
  _nowTs: number
) {
  if (roomData.mode === GameMode.GAMEMASTER) {
    return startGamemasterRoom(roomData, rounds, req);
  }
  return startTurnbasedRoom(roomData, rounds, req);
}

export const startRoom = functions.https.onCall(
  {timeoutSeconds: 540},
  async (data: any, _context: any) => {
    const db = admin.firestore();
    const req = data.data as StartRoomRequest;
    if (req.categoryTitles.length === 0) {
      throw new Error("No category titles found. Cannot start the game.");
    }
    const rounds = await generateRoundSimple(
      req.categoryTitles,
      req.numQuestions,
      req.numCategories,
      req.llmAgent
    );
    return await db.runTransaction(async (txn) => {
      const roomRef = db.collection("rooms").doc(req.roomId);
      const roomDoc = await txn.get(roomRef);
      if (!roomDoc.exists) {
        throw new HttpsError("not-found", "Room not found");
      }
      const nowTs = Date.now();
      validate(roomDoc.data() as Room, data, req, nowTs);
      const updatedRoom = modify(roomDoc.data() as Room, rounds, req, nowTs);
      await txn.set(roomRef, updatedRoom);
      console.log(updatedRoom);
      await txn.set(roomRef.collection("roomRoundData").doc("0"), rounds[0]);
      return updatedRoom;
    });
  }
);

function startGamemasterRoom(
  roomData: Room,
  rounds: Round[],
  req: StartRoomRequest
) {
  roomData.categoryTitles = req.categoryTitles;
  // Set players to the team names
  roomData.playerIds = req.teamNames;
  roomData.playerInfos = req.teamNames.map((teamName) => ({
    userName: teamName,
    score: 0,
  }));
  // Use custom timer settings if provided, otherwise use defaults
  // In gamemaster mode, we only need showAnswerDurationMillis
  roomData.roomSettings = {
    answerDurationMillis: 0, // Not used in gamemaster mode
    buzzInTimerDurationMillis: 0, // Not used in gamemaster mode
    // Use the provided showAnswerDurationMillis or default to 10000ms (10 seconds)
    showAnswerDurationMillis:
      req.timerSettings?.showAnswerDurationMillis || 10000,
    gameMode: GameMode.GAMEMASTER,
    initialThinkingDurationMillis: 0, // Not used in gamemaster mode
    llmAgent: req.llmAgent,
  };

  const roundStates: Array<RoundState> = rounds.map((round) => ({
    categoryStates: round.categories.map((category) => ({
      clueStates: category.clues.map(() => createInitialClueState()),
    })),
  }));

  console.log("Generated Round States:", roundStates);

  roomData.gameState = {
    gameProgress: {
      type: "RoomState",
      roundIdx: 0,
      currentPlayerIdx: 0,
      roundStates: roundStates,
    },
  };

  return roomData;
}

function startTurnbasedRoom(
  roomData: Room,
  rounds: Round[],
  req: StartRoomRequest
) {
  roomData.categoryTitles = req.categoryTitles;
  if (req.mode == GameMode.GAMEMASTER) {
    throw new HttpsError(
      "invalid-argument",
      "Cannot Start turn based game with gamemaster mode"
    );
  }
  // Use custom timer settings if provided, otherwise use defaults
  roomData.roomSettings = {
    answerDurationMillis: req.timerSettings?.answerDurationMillis || 60000,
    // In TURN_BASED mode, buzzInTimer isn't used, but we'll set it anyway
    buzzInTimerDurationMillis:
      req.timerSettings?.buzzInTimerDurationMillis || 60000,
    showAnswerDurationMillis:
      req.timerSettings?.showAnswerDurationMillis || 10000,
    gameMode: req.mode,
    // Initial thinking time is used in both modes
    // In BUZZER mode: additional time for the first player who buzzes in
    // In TURN_BASED mode: additional time for the first player to answer
    initialThinkingDurationMillis:
      req.timerSettings?.initialThinkingDurationMillis || 20000,
    llmAgent: req.llmAgent,
  };
  roomData.mode = req.mode;

  const roundStates: Array<RoundState> = rounds.map((round) => ({
    currentPlayer: roomData.playerIds[0], // Set the first player as the current player
    categoryStates: round.categories.map((category) => ({
      clueStates: category.clues.map(() => createInitialClueState()),
    })),
  }));

  console.log("Generated Round States:", roundStates);

  roomData.gameState = {
    gameProgress: {
      type: "RoomState",
      roundIdx: 0,
      currentPlayerIdx: 0,
      roundStates: roundStates,
    },
  };

  return roomData;
}
