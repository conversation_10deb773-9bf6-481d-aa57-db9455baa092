import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {Room} from "./resources";
import {HttpsError} from "firebase-functions/https";

type ValidateFn<T> = (room: Room, request: T, nowTs: number) => void;
type ModifyFn<T> = (room: Room, request: T, nowTs: number) => Room;

export function modifyRoomFn<T>(
  validateFn: ValidateFn<T>,
  modifyFn: ModifyFn<T>
) {
  return functions.https.onCall(async (data: any, _context: any) => {
    const roomId = (data.data as { roomId: string }).roomId;
    const roomRef = admin.database().ref(`rooms/${roomId}`);
    try {
      const txnResult = await roomRef.transaction((currentData) => {
        if (currentData === null) {
          return {} as Room;
        }
        const roomData = currentData as Room;
        const nowTs = Date.now();
        try {
          validateFn(roomData, data.data as T, nowTs);
        } catch (e) {
          console.error(e);
          // Abort the txn.
          return;
        }
        return JSON.parse(
          JSON.stringify(modifyFn(currentData, data.data as T, nowTs))
        );
      });
      const updatedRoom = txnResult.snapshot.val();
      if (!txnResult.committed) {
        // room didn't get updated because validation failed.
        // Find the reason for validation failure again :(.
        validateFn(updatedRoom, data.data as T, Date.now());
      } else if (updatedRoom === null) {
        throw new HttpsError("not-found", "Room not found");
      }
      return updatedRoom;
    } catch (error) {
      console.error("Error selecting clue:", error);
      if (error instanceof HttpsError) throw error;
      throw new HttpsError("internal", "Internal Server Error");
    }
  });
}
