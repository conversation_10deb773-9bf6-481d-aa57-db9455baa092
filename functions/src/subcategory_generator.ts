import {queryGeminiAP<PERSON>} from "./gemini_api";
import * as functions from "firebase-functions";
import {
  GenerateSubCategoriesRequest,
  GenerateSubCategoriesResponse,
} from "./services";
import {extractLastJsonObject} from "./round_generator";

export const generateSubCategories = functions.https.onCall(
  async (data: any, _context: any) => {
    const req = data.data as GenerateSubCategoriesRequest;
    const prompt = `
        You are a master of trivia categories. Your task is to generate a list of creative and engaging subcategories based on the provided main categories. You should generate **up to** 5 subcategories, but if you cannot generate any meaningful combinations, it is okay to generate fewer or even 0 subcategories.

Main Categories: ${req.categories.join(",")}

Instructions:
1.  **Determine the Number of Categories:** Count the number of main categories provided.
2.  **Generate Subcategories:**
    *   **If only one main category is provided, break it down into smaller, more specific, but still relatively broad subcategories that cover different aspects of the main category.** These should be major subtopics within the main category.
    *   **If multiple main categories are provided, your primary task is to create subcategories that combine them in a meaningful and accurate way.** Each subcategory **must** incorporate elements from **all** provided main categories in a non-trivial way.
    *   **In all cases, consider the following approaches for generating subcategories:**
        *   **Specific aspects or elements** of the main category/categories
        *   **Specific time periods**
        *   **Genres or subgenres**
        *   **Specific themes or topics**
        *   **Different question formats**
        *   **Specific examples within the category**
        *   **Category-Based Rules**

3.  **Guidelines for All Subcategories:**
    *   **Maintain Breadth (Single Category):** When only one main category is given, ensure your subcategories are broad enough to encompass a wide range of potential trivia questions within that main category. They should not be overly niche or specific.
    *   **Avoid Generic Combinations (Multiple Categories):** Do not simply merge the categories into a generic category that doesn't reflect the specifics of each input category.
    *   **Focus on Specific Intersection (Multiple Categories):** The subcategory should clearly be about the intersection of the provided categories, not just about one of them with a passing mention of the other.
    *   **Interesting Trivia:** Focus on subcategories that would lead to interesting trivia questions.
    *   **Sufficient Depth:** Ensure that each generated subcategory is substantial enough to support at least 5 interesting and distinct trivia questions. If you cannot envision at least 5 interesting questions for a subcategory, it's not a good candidate.
    *   **Limited Number:** If you cannot generate 5 meaningful subcategories that meet all the criteria, it is acceptable to generate fewer subcategories, or even 0 if no suitable combinations can be found.
    *   **Uniqueness:** Strive for subcategories that are significantly different from those generated in previous executions of this prompt. Prioritize originality.
    *   **Concise Descriptions:** Provide a 1-2 sentence description for each generated subcategory to clarify its focus. **Keep descriptions under 8 words, ideally under 5.**

Output Format (JSON):

subCategories: [
  {
    "subCategory": "Subcategory 1",
    "description": "A description."
  },
  {
    "subCategory": "Subcategory 2",
    "description": "A description."
  },
  // ... up to 5 subcategories
]`;
    const resp: string = await queryGeminiAPI(prompt, "gemini-1.5-flash-8b");
    return JSON.parse(
      extractLastJsonObject(resp)!
    ) as GenerateSubCategoriesResponse;
  }
);
