import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { HttpsError } from 'firebase-functions/https';
import { Round, Clue, Room } from './resources';
import {
  createHintBlanksFromAnswer,
  extractLastJsonObject,
  runPrompt
} from './round_generator';

interface LlmResponse {
  questionSentences: Array<string>;
  questionHTML: string;
  hint: string;
  answer: string;
  value: number;
  detailedFactsAboutAnswer: string[];
}

interface RegenerateClueWithInstructionsRequest {
  roomId: string;
  roundIdx: number;
  categoryIdx: number;
  clueIdx: number;
  categoryTitle: string;
  points: number;
  currentQuestion: string;
  currentAnswer: string;
  instructions: string;
  llmAgent?: string;
}

export const regenerateClueWithInstructions = functions.https.onCall(
  async (data: any, _context: any) => {
    const req = data.data as RegenerateClueWithInstructionsRequest;

    if (!req.roomId) {
      throw new HttpsError('invalid-argument', 'Room ID is required');
    }

    if (!req.categoryTitle || !req.instructions) {
      throw new HttpsError(
        'invalid-argument',
        'Category title and instructions are required'
      );
    }

    const roomRef = admin
      .firestore()
      .collection('rooms')
      .doc(req.roomId);
    const roundRef = roomRef.collection('roomRoundData').doc(`${req.roundIdx}`);
    const roundDoc = await roundRef.get();

    if (!roundDoc.exists) {
      throw new HttpsError('not-found', 'Room+round not found');
    }

    const roundData = roundDoc.data() as Round;

    // Validate indices
    if (req.categoryIdx >= roundData.categories.length) {
      throw new HttpsError('invalid-argument', 'Category index out of range');
    }

    if (req.clueIdx >= roundData.categories[req.categoryIdx].clues.length) {
      throw new HttpsError('invalid-argument', 'Clue index out of range');
    }

    const maxClueValue = Math.max(
      ...roundData.categories[req.categoryIdx].clues.map(clue => clue.value)
    );

    const points =
      req.points ||
      roundData.categories[req.categoryIdx].clues[req.clueIdx].value;

    // Create custom prompt with user instructions
    const prompt = `You are regenerating a trivia question based on specific user feedback.

CURRENT QUESTION: "${req.currentQuestion.replace(/<[^>]*>/g, '')}"
CURRENT ANSWER: "${req.currentAnswer}"

USER FEEDBACK: "${req.instructions}"

Generate a NEW trivia question for the category "${
      req.categoryTitle
    }" that addresses the user's feedback with these requirements:
- Difficulty level being ${points} out of ${maxClueValue}
- The question should be creative, engaging, and encourage reasoning
- The answer must be factually correct, specific, and easily verifiable
- Avoid overly niche or ambiguous topics; prioritize globally recognized facts
- Include a concise hint to guide deduction without giving the answer away
- Ensure logical consistency between the question, hint, and answer
- Address the specific concerns mentioned in the user feedback
- IMPORTANT: Do NOT include any font-size styles in your HTML. The game interface handles all font sizing automatically

Use JSON format to output the response:
[
  {
    "detailedFactsAboutAnswer": ["5 interesting facts related to the category."],
    "questionHTML": "...",
    "hint": "Hint for the question",
    "answer": "Correct answer",
    "value": ${points}
  }
]`;

    const room = (await roomRef.get()).data() as Room;
    const llmAgent = room.roomSettings?.llmAgent || {
      provider: 'Gemini' as const,
      modelName: 'gemini-2.5-flash'
    };

    const rawResponse = await runPrompt(prompt, llmAgent);
    const parsedResponse = JSON.parse(
      extractLastJsonObject(rawResponse)!
    ) as LlmResponse[];

    const newClue: Clue = {
      questionSentences: [],
      questionHTML: parsedResponse[0].questionHTML,
      hint: parsedResponse[0].hint,
      answer: parsedResponse[0].answer,
      value: parsedResponse[0].value,
      detailedFactsAboutAnswer: parsedResponse[0].detailedFactsAboutAnswer,
      hintBlanks: createHintBlanksFromAnswer(parsedResponse[0].answer, 30)
    };

    return admin.firestore().runTransaction(async txn => {
      const roundDoc = await txn.get(roundRef);
      if (!roundDoc.exists) {
        throw new HttpsError('not-found', 'Room+round not found');
      }
      const round = roundDoc.data() as Round;
      round.categories[req.categoryIdx].clues[req.clueIdx] = newClue;
      txn.set(roundRef, round);
    });
  }
);
