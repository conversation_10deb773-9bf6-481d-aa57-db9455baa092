import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {HttpsError} from "firebase-functions/https";
import {Room, RoomState, RoomSummary} from "./resources";

interface RevertToGameStateRequest {
  roomId: string;
  playerId: string;
}

function validate(
  roomData: Room,
  _req: RevertToGameStateRequest, // Unused but kept for consistency
  _nowTs: number,
  context: any
) {
  // Check if the room is in RoomSummary state
  if (roomData.gameState.gameProgress.type !== "RoomSummary") {
    throw new HttpsError("failed-precondition", "Room is not in summary state");
  }

  // Check if the original state is available
  const roomSummary = roomData.gameState.gameProgress as RoomSummary;
  if (!roomSummary.originalState) {
    throw new HttpsError(
      "failed-precondition",
      "Original game state is not available"
    );
  }

  // Check if the user is the host (if auth context is available)
  if (context && context.auth && context.auth.uid) {
    if (roomData.host !== context.auth.uid) {
      throw new HttpsError(
        "permission-denied",
        "Only the host can revert the game state"
      );
    }
  }
}

function modify(
  roomData: Room,
  _req: RevertToGameStateRequest,
  _nowTs: number
): Room {
  // Get the original RoomState from the RoomSummary
  const roomSummary = roomData.gameState.gameProgress as RoomSummary;
  const originalState = roomSummary.originalState;

  // Restore the original RoomState
  roomData.gameState = {
    gameProgress: originalState as RoomState,
  };

  return roomData;
}

export const revertToGameState = functions.https.onCall(
  async (data: any, context: any) => {
    const req = data.data as RevertToGameStateRequest;
    const db = admin.firestore();

    return db.runTransaction(async (txn) => {
      const roomRef = db.collection("rooms").doc(req.roomId);
      const roomDoc = await txn.get(roomRef);

      if (!roomDoc.exists) {
        throw new HttpsError("not-found", "Room not found");
      }

      const roomData = roomDoc.data() as Room;
      const nowTs = Date.now();

      validate(roomData, req, nowTs, context);
      const updatedRoom = modify(roomData, req, nowTs);

      await txn.set(roomRef, updatedRoom);
      return updatedRoom;
    });
  }
);
