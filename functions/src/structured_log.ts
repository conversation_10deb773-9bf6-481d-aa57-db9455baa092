// Structured log types for JeopardyGPT

export enum LogType {
  CLUE_SELECTED = "CLUE_SELECTED",
  BUZZ_IN = "BUZZ_IN",
  ANSWER_CORRECT = "ANSWER_CORRECT",
  ANSWER_INCORRECT = "ANSWER_INCORRECT",
  PASS = "PASS",
  TIMER_EXPIRED = "TIMER_EXPIRED",
  HINT_OPENED = "HINT_OPENED",
  NO_CORRECT_ANSWER = "NO_CORRECT_ANSWER",
  WAITING_FOR_ANSWER = "WAITING_FOR_ANSWER",
  SHOW_ANSWER = "SHOW_ANSWER",
  POINTS_ASSIGNED = "POINTS_ASSIGNED"
}

// Base interface for all log entries
export interface BaseLogEntry {
  type: LogType;
  timestamp: number;
  snarkyComment?: string; // Optional snarky comment for the log entry
}

// Clue selected by a player
export interface ClueSelectedLog extends BaseLogEntry {
  type: LogType.CLUE_SELECTED;
  playerId: string;
  playerName: string;
  categoryTitle: string;
  clueValue: number;
}

// Player buzzed in
export interface BuzzInLog extends BaseLogEntry {
  type: LogType.BUZZ_IN;
  playerId: string;
  playerName: string;
  timeTaken: number; // Time in seconds it took to buzz in
}

// Player answered correctly
export interface AnswerCorrectLog extends BaseLogEntry {
  type: LogType.ANSWER_CORRECT;
  playerId: string;
  playerName: string;
  guess: string;
  pointsAwarded: number;
}

// Player answered incorrectly
export interface AnswerIncorrectLog extends BaseLogEntry {
  type: LogType.ANSWER_INCORRECT;
  playerId: string;
  playerName: string;
  guess: string;
  pointsDeducted: number;
}

// Player passed their turn
export interface PassLog extends BaseLogEntry {
  type: LogType.PASS;
  playerId: string;
  playerName: string;
  pointsDeducted?: number; // Optional, as passing might not always deduct points
}

// Player's answer timer expired
export interface TimerExpiredLog extends BaseLogEntry {
  type: LogType.TIMER_EXPIRED;
  playerId: string;
  playerName: string;
  pointsDeducted?: number;
}

// Hint was opened
export interface HintOpenedLog extends BaseLogEntry {
  type: LogType.HINT_OPENED;
}

// No player answered correctly
export interface NoCorrectAnswerLog extends BaseLogEntry {
  type: LogType.NO_CORRECT_ANSWER;
}

// Waiting for a player to answer
export interface WaitingForAnswerLog extends BaseLogEntry {
  type: LogType.WAITING_FOR_ANSWER;
  playerId: string;
  playerName: string;
}

// Show the answer
export interface ShowAnswerLog extends BaseLogEntry {
  type: LogType.SHOW_ANSWER;
  answer: string;
}

// Points assigned (for gamemaster mode)
export interface PointsAssignedLog extends BaseLogEntry {
  type: LogType.POINTS_ASSIGNED;
  playerId?: string;
  playerName?: string;
  teamName?: string;
  pointsAwarded: number;
}

// Union type of all possible log entries
export type LogEntry =
  | ClueSelectedLog
  | BuzzInLog
  | AnswerCorrectLog
  | AnswerIncorrectLog
  | PassLog
  | TimerExpiredLog
  | HintOpenedLog
  | NoCorrectAnswerLog
  | WaitingForAnswerLog
  | ShowAnswerLog
  | PointsAssignedLog;

// The structured log container
export interface StructuredClueLog {
  entries: LogEntry[];
}
