import {GameMode, LlmAgent} from "./resources";

export interface CreateRoomRequest {
  userName: string;
  mode: string;
  photoUrl?: string;
}

export interface JoinRoomRequest {
  userName: string;
  roomId: string;
  photoUrl?: string;
}

export interface TimerSettings {
  buzzInTimerDurationMillis: number;
  answerDurationMillis: number;
  showAnswerDurationMillis: number;
  initialThinkingDurationMillis: number;
}

export interface UpdateRoomSettingsRequest {
  roomId: string;
  categoryTitles?: string[];
  teamNames?: string[];
  gameMode?: GameMode;
  numQuestions?: number;
  llmAgent?: LlmAgent;
  timerSettings?: {
    buzzInTimerDurationMillis: number;
    answerDurationMillis: number;
    showAnswerDurationMillis: number;
    initialThinkingDurationMillis: number;
    gameMode: GameMode;
    llmAgent: LlmAgent;
  };
}

export interface StartRoomRequest {
  roomId: string;
  numQuestions: number;
  mode: GameMode;
  categoryTitles: string[];
  llmAgent: LlmAgent;
  teamNames: string[];
  numCategories: number;
  timerSettings?: TimerSettings;
}

export interface SelectClueRequest {
  roomId: string;
  roundIdx: number;
  categoryIdx: number;
  clueIdx: number;
}

export interface BuzzInRequest {
  roomId: string;
  roundIdx: number;
  categoryIdx: number;
  clueIdx: number;
  playerId: string;
  hintOpened: boolean;
}

export interface SubmitGuessRequest {
  // Which playerId are we submitting the guess on behalf.
  // This should only be present in GameMaster mode.
  playerId?: string;
  roomId: string;
  roundIdx: number;
  categoryIdx: number;
  clueIdx: number;
  guess: string;
}

export interface RegenerateClueRequest {
  roomId: string;
  roundIdx: number;
  categoryIdx: number;
  clueIdx: number;
}

export interface SkipShowAnswerRequest {
  roomId: string;
  roundIdx: number;
  categoryIdx: number;
  clueIdx: number;
  playerId: string;
}

export interface GenerateSubCategoriesRequest {
  categories: Array<string>;
}

export interface SubCategory {
  subCategory: string;
  description: string;
}
export interface GenerateSubCategoriesResponse {
  subCategories: Array<SubCategory>;
}

// Any timer based events
//   1. Trigger:BuzzInTimerExpired && BuzzInQueue completed && hint not opened -> ShowHint.
//   2. Trigger:BuzzInTimerExpired && BuzzInQueue completed && hint opened -> ShowAnswer.
//   3. Trigger:AnswerTimerExpired && BuzzInQueue not completed -> ChangeTurn.
//   4. Trigger:ShowAnswer && ShowAnswerTimerExpired -> CloseClue.
export interface PingRoomRequest {
  playerId: string;
  roomId: string;
  endGame?: boolean;
}

export interface RegisterPlayerRequest {
  userName: string;
}

export interface GameMasterActionsRequest {
  roomId: string;
  roundIdx?: number;
  categoryIdx?: number;
  clueIdx?: number;
  action:
    | "toggleHint"
    | "toggleAnswer"
    | "END_GAME"
    | "startCountdown"
    | "resetCountdown"
    | "pauseCountdown";
  countdownDurationSeconds?: number; // Duration for countdown timer in seconds
}
