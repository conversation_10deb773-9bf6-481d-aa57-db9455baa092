import {HttpsError} from "firebase-functions/https";
import {BuzzMessage, ClueState, GameMode, Room, RoomState} from "./resources";
import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {BuzzInRequest} from "./services";
import {repeatedPingRoom} from "./ping_room";
import {LogType} from "./structured_log";
import {addStructuredLogEntry} from "./log_utils";

function validate(roomData: Room, requestData: BuzzInRequest, nowTs: number) {
  if (
    !requestData.roomId ||
    requestData.roundIdx === undefined ||
    requestData.categoryIdx === undefined ||
    requestData.clueIdx === undefined ||
    !requestData.playerId
  ) {
    throw new HttpsError(
      "invalid-argument",
      "Room ID, RoundIdx, categoryIdx, clueIdx, and playerId are required"
    );
  }

  if (!roomData.playerIds.some((p) => p == requestData.playerId)) {
    throw new HttpsError("invalid-argument", "PlayerId not part of this room");
  }
  // Check if the game is in progress
  if (roomData.gameState.gameProgress.type !== "RoomState") {
    throw new HttpsError("failed-precondition", "Game not in progress");
  }
  const roomState = roomData.gameState.gameProgress as RoomState;
  if (roomState.roundIdx != requestData.roundIdx) {
    throw new HttpsError("invalid-argument", "Invalid roundIdx");
  }
  if (
    roomState.roundStates[requestData.roundIdx].currentCategoryIdx !=
    requestData.categoryIdx
  ) {
    throw new HttpsError("invalid-argument", "Invalid categoryIdx");
  }
  if (
    roomState.roundStates[requestData.roundIdx].currentClueIdx !=
    requestData.clueIdx
  ) {
    throw new HttpsError("invalid-argument", "Invalid clueIdx");
  }
  const clueState = roomState.roundStates[requestData.roundIdx].categoryStates[
    requestData.categoryIdx
  ].clueStates[requestData.clueIdx] as ClueState;

  if (
    clueState.buzzedInPlayerQueue &&
    clueState.buzzedInPlayerQueue.some((p) => p == requestData.playerId)
  ) {
    throw new HttpsError(
      "invalid-argument",
      "This player has already buzzed in"
    );
  }
  if (
    clueState.buzzInTimerStartTime! +
      roomData.roomSettings!.buzzInTimerDurationMillis! <
    nowTs
  ) {
    throw new HttpsError(
      "invalid-argument",
      "BuzzIn timer is already expired."
    );
  }
  if (clueState.clueComplete) {
    throw new HttpsError("invalid-argument", "Clue is already complete.");
  }
  if (clueState.passedPlayers.some((p) => p == requestData.playerId)) {
    throw new HttpsError(
      "invalid-argument",
      "This player has already passed. Can't buzz in."
    );
  }
}

export const buzzIn = functions.https.onCall(
  async (data: any, _context: any) => {
    const req = data.data as BuzzInRequest;
    const db = admin.firestore();
    return await db.runTransaction(async (txn) => {
      const roomRef = db.collection("rooms").doc(req.roomId);
      const roomDoc = await txn.get(roomRef);
      if (!roomDoc.exists) {
        throw new HttpsError("not-found", "Room not found");
      }
      const roomData = roomDoc.data() as Room;
      if (roomData.mode != GameMode.GAMEMASTER) {
        req.playerId = data.auth.uid!;
      }
      const nowTs = Date.now();
      validate(roomData, req, nowTs);
      const roomState = roomData.gameState.gameProgress as RoomState;
      const newClueState: ClueState =
        roomState.roundStates[req.roundIdx].categoryStates[req.categoryIdx]
          .clueStates[req.clueIdx];
      if (newClueState.buzzedInPlayerQueue === undefined) {
        newClueState.buzzedInPlayerQueue = [];
      }
      // Scan all elements in buzzerQueue subcollection and merge them into roomData.buzzedInPlayerQueue.
      const buzzerQueueRef = roomRef
        .collection("rounds")
        .doc(`${req.roundIdx}`)
        .collection("categories")
        .doc(`${req.categoryIdx}`)
        .collection("clues")
        .doc(`${req.clueIdx}`)
        .collection("hintOpened")
        .doc(`${req.hintOpened}`)
        .collection("buzzerQueue")
        .orderBy("buzzTime", "asc");
      const buzzerQueueSnapshot = await txn.get(buzzerQueueRef);
      buzzerQueueSnapshot.forEach((doc) => {
        const buzzMessage = doc.data() as BuzzMessage;
        if (!newClueState.buzzedInPlayerQueue.includes(buzzMessage.playerId)) {
          newClueState.buzzedInPlayerQueue.push(buzzMessage.playerId);

          const playerIdx = roomData.playerIds.findIndex(
            (p) => p == buzzMessage.playerId
          );
          const playerName =
            roomData.playerInfos[playerIdx].userName || "Unknown Player";
          const timeTaken =
            (buzzMessage.buzzTime.toMillis() -
              newClueState.buzzInTimerStartTime!) /
            1000;

          // Add structured log entry for buzz in
          const buzzInLogEntry = {
            type: LogType.BUZZ_IN as LogType.BUZZ_IN,
            timestamp: Date.now(),
            playerId: buzzMessage.playerId,
            playerName: playerName,
            timeTaken: timeTaken,
          };
          addStructuredLogEntry(newClueState, buzzInLogEntry);

          // Add structured log entry for waiting for answer
          const waitingLogEntry = {
            type: LogType.WAITING_FOR_ANSWER as LogType.WAITING_FOR_ANSWER,
            timestamp: Date.now(),
            playerId: buzzMessage.playerId,
            playerName: playerName,
          };
          addStructuredLogEntry(newClueState, waitingLogEntry);
        }
      });
      const newRoomData = await repeatedPingRoom(
        txn,
        roomData,
        {
          roomId: roomData.roomId,
          playerId: req.playerId,
        },
        nowTs
      );

      await txn.set(roomRef, newRoomData);
      return newRoomData;
    });
  }
);
