import { onCall, HttpsError } from 'firebase-functions/v2/https';
import { getFirestore } from 'firebase-admin/firestore';
import { logger } from 'firebase-functions';

export interface CastMediaCommand {
  type: 'play' | 'pause' | 'seek' | 'volume' | 'pauseAll' | 'mute' | 'unmute';
  questionId?: string; // Changed from playerId to questionId
  value?: number; // for seek time or volume
  timestamp: number;
}

export interface SendCastMediaCommandRequest {
  roomId: string;
  command: CastMediaCommand;
}

export const sendCastMediaCommand = onCall({ cors: true }, async request => {
  try {
    const { roomId, command } = request.data as SendCastMediaCommandRequest;

    // Validate input
    if (!roomId || !command) {
      throw new HttpsError('invalid-argument', 'Missing roomId or command');
    }

    if (!command.type || !command.timestamp) {
      throw new HttpsError('invalid-argument', 'Invalid command format');
    }

    // Validate command type
    const validTypes = [
      'play',
      'pause',
      'seek',
      'volume',
      'pauseAll',
      'mute',
      'unmute'
    ];
    if (!validTypes.includes(command.type)) {
      throw new HttpsError(
        'invalid-argument',
        `Invalid command type: ${command.type}`
      );
    }

    // For commands that require a questionId, validate it's present
    if (
      ['play', 'pause', 'seek', 'volume', 'mute', 'unmute'].includes(
        command.type
      ) &&
      !command.questionId
    ) {
      throw new HttpsError(
        'invalid-argument',
        `Command type ${command.type} requires questionId`
      );
    }

    // For seek and volume commands, validate value is present
    if (
      ['seek', 'volume'].includes(command.type) &&
      command.value === undefined
    ) {
      throw new HttpsError(
        'invalid-argument',
        `Command type ${command.type} requires value`
      );
    }

    const db = getFirestore();

    // Store the command in Firestore for the cast room to pick up
    // Using a subcollection to keep commands organized
    const commandRef = db
      .collection('rooms')
      .doc(roomId)
      .collection('mediaCommands')
      .doc('latest');

    await commandRef.set({
      ...command,
      createdAt: new Date(),
      processed: false
    });

    logger.info(`Media command sent for room ${roomId}:`, {
      type: command.type,
      questionId: command.questionId,
      timestamp: command.timestamp
    });

    return {
      success: true,
      message: 'Media command sent successfully'
    };
  } catch (error) {
    logger.error('Error sending cast media command:', error);

    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError('internal', 'Failed to send media command');
  }
});
