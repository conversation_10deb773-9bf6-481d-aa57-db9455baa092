import * as functions from "firebase-functions";
import {CreateRoomRequest} from "./services";
import {HttpsError} from "firebase-functions/https";
import * as admin from "firebase-admin";
import {GameMode, Room} from "./resources";

export const createRoom = functions.https.onCall(async (data, _context) => {
  const requestData: CreateRoomRequest = data.data;
  if (!data.auth!.uid) {
    throw new HttpsError(
      "permission-denied",
      "Please login before creating room."
    );
  }
  if (!requestData.userName) {
    throw new HttpsError("invalid-argument", "Missing userName from request");
  }

  // Get playerId and username from the request
  const {mode} = requestData;
  // TODO: make this cleaner.
  const gameMode =
    mode === "multiplayer" ? GameMode.TURN_BASED : GameMode.GAMEMASTER;
  // Generate a unique room ID
  const roomId = generateRoomId();
  // Initialize room data differently based on game mode
  const initialRoomData: Room = {
    roomId: roomId,
    gameState: {
      gameProgress: {type: "WaitingRoom"}, // Initial state is WaitingRoom
      // No need for clueState at the beginning
    },
    host: data.auth!.uid, // Set the host to the creating player's ID
    createdAt: Date.now(), // TODO: Use server-side timestamp
    mode: gameMode,
    // In gamemaster mode, don't add the creator as a player
    playerInfos:
      gameMode === GameMode.GAMEMASTER ?
        [] :
        [
          {
            userName: requestData.userName,
            score: 0,
            photoUrl: requestData.photoUrl,
          },
        ],
    // In gamemaster mode, initialize with empty playerIds array
    playerIds: gameMode === GameMode.GAMEMASTER ? [] : [data.auth!.uid],
  };
  try {
    await admin
      .firestore()
      .collection("rooms")
      .doc(roomId)
      .set(initialRoomData);

    return initialRoomData;
  } catch (error) {
    console.error("Error creating room:", error);
    throw new HttpsError("internal", "Internal Server Error");
  }
});

// Function to generate a unique room ID (consider using a library like UUID
// for more robust IDs)
function generateRoomId(): string {
  return Math.random()
    .toString(36)
    .substring(2, 8)
    .toUpperCase();
}
