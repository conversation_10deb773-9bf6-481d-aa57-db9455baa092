import {HttpsError} from "firebase-functions/https";
import {ClueState, Room, RoomState, Round} from "./resources";
import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {LogType} from "./structured_log";
import {addStructuredLogEntry} from "./log_utils";

interface AssignPointsRequest {
  roomId: string;
  roundIdx: number;
  categoryIdx: number;
  clueIdx: number;
  team: string;
}

export function validate(
  roomData: Room,
  data: any,
  requestData: AssignPointsRequest,
  _nowTs: number
) {
  if (
    !requestData.roomId ||
    requestData.roundIdx === undefined ||
    requestData.categoryIdx === undefined ||
    requestData.clueIdx === undefined
  ) {
    throw new HttpsError(
      "invalid-argument",
      "Room ID, roundIdx, categoryIdx, and clueIdx are required."
    );
  }

  if (roomData.playerIds.length === 0) {
    throw new HttpsError(
      "failed-precondition",
      "No players found in the room."
    );
  }
  if (roomData.host != data.auth.uid) {
    throw new HttpsError(
      "permission-denied",
      "Only the host is allowed to assign points"
    );
  }

  const teamNames = Array.from(new Set(roomData.playerIds));

  if (
    requestData.team &&
    requestData.team != "none" &&
    !teamNames.includes(requestData.team)
  ) {
    throw new HttpsError(
      "invalid-argument",
      `Invalid team name: ${requestData.team}`
    );
  }

  const roomState = roomData.gameState.gameProgress as RoomState;
  if (roomState.roundIdx !== requestData.roundIdx) {
    throw new HttpsError("invalid-argument", "Invalid roundIdx.");
  }

  const clueState: ClueState =
    roomState.roundStates[requestData.roundIdx].categoryStates[
      requestData.categoryIdx
    ].clueStates[requestData.clueIdx];

  if (clueState.clueComplete) {
    throw new HttpsError(
      "failed-precondition",
      "Points cannot be assigned to a completed clue."
    );
  }

  // Check if the answer is already being shown
  if (clueState.showAnswer) {
    throw new HttpsError(
      "failed-precondition",
      "Points cannot be assigned while the answer is being shown."
    );
  }
}

export async function modify(
  roomData: Room,
  requestData: AssignPointsRequest,
  _nowTs: number
) {
  const roomState = roomData.gameState.gameProgress as RoomState;

  const clueState: ClueState =
    roomState.roundStates[requestData.roundIdx].categoryStates[
      requestData.categoryIdx
    ].clueStates[requestData.clueIdx];

  const roundDoc = await admin
    .firestore()
    .collection("rooms")
    .doc(roomData.roomId)
    .collection("roomRoundData")
    .doc(`${requestData.roundIdx}`)
    .get();
  if (!roundDoc.exists) {
    throw new HttpsError("not-found", "Room not found");
  }
  const round = roundDoc.data() as Round;

  const clueValue =
    requestData.team === "none" ?
      0 :
      round.categories[requestData.categoryIdx].clues[requestData.clueIdx]
        .value;

  // Create the points assigned log entry
  const pointsAssignedLogEntry = {
    type: LogType.POINTS_ASSIGNED as LogType.POINTS_ASSIGNED,
    timestamp: Date.now(),
    pointsAwarded: clueValue,
  };

  if (requestData.team && requestData.team !== "none") {
    // Assign points to the team (which is same as player).
    const playerIdx = roomData.playerIds.findIndex(
      (p) => p == `${requestData.team}`
    );
    roomData.playerInfos[playerIdx].score += clueValue;
    clueState.answeredByPlayerId = requestData.team;

    // Add team info to the log entry
    (pointsAssignedLogEntry as any).teamName = requestData.team;
  }

  // Initiate the show answer phase
  clueState.showAnswer = true;
  clueState.showAnswerStartTime = _nowTs;
  // Note: clueComplete will be set to true later by pingRoom after the timer expires

  // Add the structured log entry
  addStructuredLogEntry(clueState, pointsAssignedLogEntry);

  return JSON.parse(JSON.stringify(roomData));
}

export const assignPoints = functions.https.onCall(async (data, _context) => {
  const req = data.data as AssignPointsRequest;
  const db = admin.firestore();
  return db.runTransaction(async (txn) => {
    const roomRef = db.collection("rooms").doc(req.roomId);
    const roomDoc = await txn.get(roomRef);
    if (!roomDoc.exists) {
      throw new HttpsError("not-found", "Room not found");
    }
    const roomData = roomDoc.data() as Room;
    const nowTs = Date.now();
    validate(roomData, data, req, nowTs);
    const updatedRoom = await modify(roomData, req, nowTs);
    await txn.set(roomRef, updatedRoom);
    return updatedRoom;
  });
});
