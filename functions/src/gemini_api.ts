const {
  GoogleGenerativeA<PERSON>,
  HarmBlockThreshold,
  HarmCategory,
} = require("@google/generative-ai");

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
export async function queryGeminiAPI(
  prompt: string,
  llmAgent: string
): Promise<any> {
  try {
    const model = genAI.getGenerativeModel({
      model: llmAgent,
      generation_config: {
        temperature: 2,
        top_p: 0.9,
        top_k: 40,
        max_output_tokens: 8192,
        response_mime_type: "text/plain",
      },
      safety_settings: [
        {
          category: HarmCategory.HARM_CATEGORY_HARASSMENT,
          threshold: HarmBlockThreshold.BLOCK_NONE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
          threshold: HarmBlockThreshold.BLOCK_NONE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
          threshold: HarmBlockThreshold.BLOCK_NONE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
          threshold: HarmBlockThreshold.BLOCK_NONE,
        },
      ],
    });
    console.log(prompt);
    const response = await model.generateContent(prompt);
    const textResponse = response.response.text();
    console.log("Raw response from Gemini API:", textResponse);
    return textResponse;
  } catch (error) {
    console.error("Error querying Gemini API:", error);
    throw new Error("Failed to fetch or parse response from Gemini API");
  }
}
