// Any timer based events should be handled here.
//   1. Trigger:BuzzInTimerExpired && BuzzInQueue completed && hint not opened -> ShowHint.
//   2. Trigger:BuzzInTimerExpired && BuzzInQueue completed && hint opened -> ShowAnswer.
//   3. Trigger:AnswerTimerExpired && BuzzInQueue not completed -> ChangeTurn.
//   4. Trigger:ShowAnswer && ShowAnswerTimerExpired -> CloseClue.

import {PingRoomRequest} from "./services";
import {HttpsError} from "firebase-functions/https";
import {
  GameMode,
  MemorableQuestion,
  Room,
  RoomState,
  Round,
} from "./resources";
import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {Transaction} from "firebase-admin/firestore";
import {LogType} from "./structured_log";
import {addStructuredLogEntry} from "./log_utils";

function validate(_roomData: Room, _req: PingRoomRequest, _nowTs: number) {}

/**
 * Selects memorable questions from the game based on various criteria
 * @param txn Firestore transaction
 * @param roomData Room data
 * @param roomState Current room state
 * @returns Array of memorable questions
 */
async function selectMemorableQuestions(
  txn: Transaction,
  roomData: Room,
  roomState: RoomState
): Promise<MemorableQuestion[]> {
  const memorableQuestions: MemorableQuestion[] = [];
  const roundIdx = roomState.roundIdx;

  try {
    // Get the round data to access questions
    const roundDoc = await txn.get(
      admin
        .firestore()
        .collection("rooms")
        .doc(roomData.roomId)
        .collection("roomRoundData")
        .doc(`${roundIdx}`)
    );

    if (!roundDoc.exists) {
      console.error(
        `Round data not found for room ${roomData.roomId}, round ${roundIdx}`
      );
      return [];
    }

    const roundData = roundDoc.data() as Round;

    // Track questions with interesting properties
    const questionsWithMetrics: Array<{
      category: string;
      question: string;
      answer: string;
      value: number;
      categoryIdx: number;
      clueIdx: number;
      metrics: {
        correctAnswers: number;
        incorrectAnswers: number;
        attempts: number;
        isHighValue: boolean;
        hasHintOpened: boolean;
      };
    }> = [];

    // Loop through all categories and clues to gather metrics
    roundData.categories.forEach((category, categoryIdx) => {
      category.clues.forEach((clue, clueIdx) => {
        // Only consider completed clues
        const clueState =
          roomState.roundStates[roundIdx].categoryStates[categoryIdx]
            .clueStates[clueIdx];

        if (clueState.clueComplete) {
          // Count correct and incorrect answers from the structured log
          let correctAnswers = 0;
          let incorrectAnswers = 0;
          const hasHintOpened = clueState.hintOpened;

          if (clueState.structuredClueLog?.entries) {
            correctAnswers = clueState.structuredClueLog.entries.filter(
              (entry) => entry.type === LogType.ANSWER_CORRECT
            ).length;

            incorrectAnswers = clueState.structuredClueLog.entries.filter(
              (entry) => entry.type === LogType.ANSWER_INCORRECT
            ).length;
          }

          // Add to our tracking array
          questionsWithMetrics.push({
            category: category.categoryTitle,
            question: clue.questionHTML || clue.questionSentences.join(" "),
            answer: clue.answer,
            value: clue.value,
            categoryIdx,
            clueIdx,
            metrics: {
              correctAnswers,
              incorrectAnswers,
              attempts: correctAnswers + incorrectAnswers,
              isHighValue: clue.value >= 800, // Consider high value questions
              hasHintOpened,
            },
          });
        }
      });
    });

    // No completed questions
    if (questionsWithMetrics.length === 0) {
      return [];
    }

    // Find the most challenging question (most incorrect answers)
    const mostChallenging = [...questionsWithMetrics].sort(
      (a, b) => b.metrics.incorrectAnswers - a.metrics.incorrectAnswers
    )[0];

    if (mostChallenging && mostChallenging.metrics.incorrectAnswers > 0) {
      memorableQuestions.push({
        category: mostChallenging.category,
        question: mostChallenging.question,
        answer: mostChallenging.answer,
        value: mostChallenging.value,
        reason: `This question stumped ${
          mostChallenging.metrics.incorrectAnswers
        } player${mostChallenging.metrics.incorrectAnswers !== 1 ? "s" : ""}!`,
      });
    }

    // Find the most engaging question (most total attempts)
    const mostEngaging = [...questionsWithMetrics]
      .filter((q) => !memorableQuestions.some((mq) => mq.question === q.question)) // Exclude already selected
      .sort((a, b) => b.metrics.attempts - a.metrics.attempts)[0];

    if (mostEngaging && mostEngaging.metrics.attempts > 1) {
      memorableQuestions.push({
        category: mostEngaging.category,
        question: mostEngaging.question,
        answer: mostEngaging.answer,
        value: mostEngaging.value,
        reason: `This was the most attempted question with ${mostEngaging.metrics.attempts} tries!`,
      });
    }

    // Find the highest value question that was answered correctly
    const highValueCorrect = [...questionsWithMetrics]
      .filter(
        (q) =>
          q.metrics.isHighValue &&
          q.metrics.correctAnswers > 0 &&
          !memorableQuestions.some((mq) => mq.question === q.question)
      )
      .sort((a, b) => b.value - a.value)[0];

    if (highValueCorrect) {
      memorableQuestions.push({
        category: highValueCorrect.category,
        question: highValueCorrect.question,
        answer: highValueCorrect.answer,
        value: highValueCorrect.value,
        reason: `This high-value question was worth ${highValueCorrect.value} points!`,
      });
    }

    // If we still need more questions, add any remaining completed questions
    if (memorableQuestions.length < 3) {
      const remainingQuestions = questionsWithMetrics
        .filter((q) => !memorableQuestions.some((mq) => mq.question === q.question))
        .sort(() => 0.5 - Math.random()); // Shuffle remaining questions

      // Add up to 3 total questions
      for (
        let i = 0;
        i < remainingQuestions.length && memorableQuestions.length < 3;
        i++
      ) {
        const q = remainingQuestions[i];
        memorableQuestions.push({
          category: q.category,
          question: q.question,
          answer: q.answer,
          value: q.value,
          reason:
            q.metrics.hasHintOpened && q.metrics.correctAnswers > 0 ?
              "This question required a hint to solve!" :
              "This was one of the memorable questions from the game.",
        });
      }
    }

    return memorableQuestions;
  } catch (error) {
    console.error("Error selecting memorable questions:", error);
    return [];
  }
}

async function pingRoomImpl(
  txn: Transaction,
  roomData: Room,
  req: PingRoomRequest,
  nowTs: number
) {
  // Check if all clues are complete
  function areAllCluesComplete(roomData: Room): boolean {
    if (roomData.gameState.gameProgress.type != "RoomState") {
      return false;
    }
    const roomState = roomData.gameState.gameProgress as RoomState;
    const roundState = roomState.roundStates[roomState.roundIdx];

    // Check each category and clue
    for (const categoryState of roundState.categoryStates) {
      for (const clueState of categoryState.clueStates) {
        if (!clueState.clueComplete) {
          return false;
        }
      }
    }
    return true;
  }
  if (roomData.gameState.gameProgress.type != "RoomState") {
    return roomData;
  }
  // Handle endGame request
  if (req.endGame || areAllCluesComplete(roomData)) {
    // Get the current RoomState before transitioning
    const currentRoomState = roomData.gameState.gameProgress as RoomState;

    // Select memorable questions from the game
    const memorableQuestions = await selectMemorableQuestions(
      txn,
      roomData,
      currentRoomState
    );

    // Update the game state to RoomSummary while preserving the original state
    roomData.gameState = {
      gameProgress: {
        type: "RoomSummary",
        // Store the original RoomState for access in the winner screen
        originalState: currentRoomState,
        // Include the memorable questions
        memorableQuestions: memorableQuestions,
      },
    };
    return roomData;
  }

  const roomState = roomData.gameState.gameProgress as RoomState;
  const roundState = roomState.roundStates[roomState.roundIdx];
  if (
    roomState.roundStates[roomState.roundIdx].currentCategoryIdx == undefined
  ) {
    return roomData;
    // Unreachable code removed
  }
  const clueState =
    roomState.roundStates[roomState.roundIdx].categoryStates[
      roomState.roundStates[roomState.roundIdx].currentCategoryIdx!
    ].clueStates[roomState.roundStates[roomState.roundIdx].currentClueIdx!];
  if (clueState.showAnswerStartTime != undefined) {
    if (
      clueState.showAnswerStartTime +
        roomData.roomSettings!.showAnswerDurationMillis <
      nowTs
    ) {
      roomState.roundStates[roomState.roundIdx].currentCategoryIdx = undefined;
      roomState.roundStates[roomState.roundIdx].currentClueIdx = undefined;
      if (
        roomData.roomSettings?.gameMode == GameMode.TURN_BASED &&
        clueState.clueComplete != true
      ) {
        roomState.currentPlayerIdx += 1;
        roomState.currentPlayerIdx %= roomData.playerIds.length;
      }
      clueState.clueComplete = true;
      clueState.clueLog.logs.push(
        "Showing answer timeout reached. Marking clue as complete."
      );
    }
    return roomData;
  }
  const buzzInTimerExpired =
    clueState.buzzInTimerStartTime! +
      roomData.roomSettings!.buzzInTimerDurationMillis <
    nowTs;
  if (clueState.buzzedInPlayerQueue == undefined) {
    clueState.buzzedInPlayerQueue = [];
  }
  const buzzInQueueComplete =
    clueState.buzzedInPlayerQueue.length == clueState.queueAnswerTurnIdx + 1;
  const hintOpened = clueState.hintOpened;

  let answerExpirationTime =
    clueState.answerStartTime! + roomData.roomSettings!.answerDurationMillis;
  if (clueState.queueAnswerTurnIdx == 0) {
    answerExpirationTime += roomData.roomSettings!
      .initialThinkingDurationMillis;
  }
  const answerTimerExpired = answerExpirationTime < nowTs;
  if (answerTimerExpired) {
    if (clueState.answerStartTime) {
      const currentPlayer =
        clueState.buzzedInPlayerQueue[clueState.queueAnswerTurnIdx];
      const currentPlayerIdx = roomData.playerIds.findIndex(
        (p) => p == currentPlayer
      );
      const playerName =
        roomData.playerInfos[currentPlayerIdx].userName || "Unknown Player";

      // Create the timer expired log entry
      const timerExpiredLogEntry = {
        type: LogType.TIMER_EXPIRED as LogType.TIMER_EXPIRED,
        timestamp: nowTs,
        playerId: currentPlayer,
        playerName: playerName,
      };

      if (roomData.roomSettings?.gameMode == GameMode.BUZZER) {
        const roundDoc = await txn.get(
          admin
            .firestore()
            .collection("rooms")
            .doc(roomData.roomId)
            .collection("roomRoundData")
            .doc(`${roomState.roundIdx}`)
        );
        const clue = (roundDoc.data()! as Round).categories[
          roundState.currentCategoryIdx!
        ].clues[roundState.currentClueIdx!];

        const pointsDeducted = clue.value / 2;
        roomData.playerInfos[currentPlayerIdx].score -= pointsDeducted;

        // Add points deducted to the log entry
        (timerExpiredLogEntry as any).pointsDeducted = pointsDeducted;
      }

      // Add the structured log entry
      addStructuredLogEntry(clueState, timerExpiredLogEntry);
    }
    clueState.answerStartTime = 0;
  }
  const everyoneAttempted =
    buzzInQueueComplete &&
    clueState.answerStartTime == 0 &&
    new Set([...clueState.passedPlayers, ...clueState.buzzedInPlayerQueue])
      .size == roomData.playerIds.length;
  if (
    ((buzzInTimerExpired && buzzInQueueComplete) || everyoneAttempted) &&
    answerTimerExpired
  ) {
    if (hintOpened) {
      clueState.showAnswerStartTime = nowTs;
      clueState.showAnswer = true;

      // Add structured log entry for no correct answer
      const noCorrectAnswerLogEntry = {
        type: LogType.NO_CORRECT_ANSWER as LogType.NO_CORRECT_ANSWER,
        timestamp: nowTs,
      };
      addStructuredLogEntry(clueState, noCorrectAnswerLogEntry);

      // Add structured log entry for show answer
      const showAnswerLogEntry = {
        type: LogType.SHOW_ANSWER as LogType.SHOW_ANSWER,
        timestamp: nowTs,
        answer: (
          await txn.get(
            admin
              .firestore()
              .collection("rooms")
              .doc(roomData.roomId)
              .collection("roomRoundData")
              .doc(`${roomState.roundIdx}`)
          )
        ).data()!.categories[roundState.currentCategoryIdx!].clues[
          roundState.currentClueIdx!
        ].answer,
      };
      addStructuredLogEntry(clueState, showAnswerLogEntry);
    } else {
      clueState.hintOpened = true;
      if (roomData.roomSettings?.gameMode == GameMode.BUZZER) {
        clueState.buzzedInPlayerQueue = [];
      }
      clueState.passedPlayers = [];
      clueState.queueAnswerTurnIdx = -1;
      clueState.answerStartTime = 0;
      clueState.buzzInTimerStartTime = nowTs;

      // Add structured log entry for no correct answer
      const noCorrectAnswerLogEntry = {
        type: LogType.NO_CORRECT_ANSWER as LogType.NO_CORRECT_ANSWER,
        timestamp: nowTs,
      };
      addStructuredLogEntry(clueState, noCorrectAnswerLogEntry);

      // Add structured log entry for hint opened
      const hintOpenedLogEntry = {
        type: LogType.HINT_OPENED as LogType.HINT_OPENED,
        timestamp: nowTs,
      };
      addStructuredLogEntry(clueState, hintOpenedLogEntry);
    }
    return roomData;
  }
  if (
    answerTimerExpired &&
    clueState.queueAnswerTurnIdx + 1 < clueState.buzzedInPlayerQueue.length
  ) {
    clueState.answerStartTime = nowTs;
    clueState.queueAnswerTurnIdx += 1;

    const currentPlayer =
      clueState.buzzedInPlayerQueue[clueState.queueAnswerTurnIdx];
    const playerIdx = roomData.playerIds.findIndex((p) => p == currentPlayer);
    const playerName =
      roomData.playerInfos[playerIdx].userName || "Unknown Player";

    // Add structured log entry for waiting for answer
    const waitingLogEntry = {
      type: LogType.WAITING_FOR_ANSWER as LogType.WAITING_FOR_ANSWER,
      timestamp: nowTs,
      playerId: currentPlayer,
      playerName: playerName,
    };
    addStructuredLogEntry(clueState, waitingLogEntry);
  }
  return roomData;
}

export async function repeatedPingRoom(
  txn: Transaction,
  roomData: Room,
  req: PingRoomRequest,
  nowTs: number
) {
  // Hack!
  for (let i = 0; i < 4; i++) {
    roomData = await pingRoomImpl(txn, roomData, req, nowTs);
  }

  return roomData;
}

export const pingRoom = functions.https.onCall(
  async (data: any, _context: any) => {
    const req = data.data as PingRoomRequest;
    const db = admin.firestore();
    return db.runTransaction(async (txn) => {
      const roomRef = db.collection("rooms").doc(req.roomId);
      const roomDoc = await txn.get(roomRef);
      if (!roomDoc.exists) {
        throw new HttpsError("not-found", "Room not found");
      }
      const roomData = roomDoc.data() as Room;
      const nowTs = Date.now();
      validate(roomData, req, nowTs);
      const updatedRoom = JSON.parse(
        JSON.stringify(await repeatedPingRoom(txn, roomData, req, nowTs))
      );
      await txn.set(roomRef, updatedRoom);
      return updatedRoom;
    });
  }
);
