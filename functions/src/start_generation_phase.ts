import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {HttpsError} from "firebase-functions/https";
import {Room} from "./resources";

interface StartGenerationPhaseRequest {
  roomId: string;
}

function validate(
  roomData: Room,
  _data: any,
  _req: StartGenerationPhaseRequest,
  _nowTs: number
) {
  // Only the host can start the generation phase
  if (_data.auth?.uid !== roomData.host) {
    throw new HttpsError(
      "permission-denied",
      "Only the host can start the generation phase"
    );
  }

  // Ensure the room is in the WaitingRoom state
  if (roomData.gameState.gameProgress.type !== "WaitingRoom") {
    throw new HttpsError(
      "failed-precondition",
      "Room is not in the WaitingRoom state"
    );
  }
}

function modify(
  roomData: Room,
  _req: StartGenerationPhaseRequest,
  _nowTs: number
): Room {
  // Update the room state to GeneratingQuestions
  roomData.gameState.gameProgress = {
    type: "GeneratingQuestions",
    progress: 0,
    message: "Starting question generation...",
    startTime: _nowTs,
  };

  return roomData;
}

export const startGenerationPhase = functions.https.onCall(
  async (data: any, _context: any) => {
    const req = data.data as StartGenerationPhaseRequest;
    const db = admin.firestore();

    return db.runTransaction(async (txn) => {
      const roomRef = db.collection("rooms").doc(req.roomId);
      const roomDoc = await txn.get(roomRef);

      if (!roomDoc.exists) {
        throw new HttpsError("not-found", "Room not found");
      }

      const roomData = roomDoc.data() as Room;
      const nowTs = Date.now();

      validate(roomData, data, req, nowTs);
      const updatedRoom = modify(roomData, req, nowTs);

      await txn.set(roomRef, updatedRoom);
      return updatedRoom;
    });
  }
);
