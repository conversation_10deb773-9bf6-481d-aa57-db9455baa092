import {ClueState} from "./resources";
import {LogEntry, LogType} from "./structured_log";
import {generateSnarkyComment} from "./snark_service";

/**
 * Adds a structured log entry to the clueState and also adds a corresponding text log
 * for backward compatibility
 *
 * @param clueState The clue state to update
 * @param logEntry The structured log entry to add
 * @return ClueState The updated clue state
 */
export function addStructuredLogEntry(
  clueState: ClueState,
  logEntry: LogEntry
): ClueState {
  // Initialize structuredClueLog if it doesn't exist
  if (!clueState.structuredClueLog) {
    clueState.structuredClueLog = {entries: []};
  }

  // Generate a snarky comment for the log entry
  const snarkyComment = generateSnarkyComment(logEntry);

  // Add the snarky comment to the log entry
  const logEntryWithSnark = {
    ...logEntry,
    snarkyComment,
  };

  // Add the structured log entry
  clueState.structuredClueLog.entries.push(logEntryWithSnark);

  // Also add a text log for backward compatibility
  const textLog = generateTextLog(logEntry);
  clueState.clueLog.logs.push(textLog);

  return clueState;
}

/**
 * Generates a text log from a structured log entry for backward compatibility
 *
 * @param logEntry The structured log entry
 * @return A text log message
 */
function generateTextLog(logEntry: LogEntry): string {
  switch (logEntry.type) {
  case LogType.CLUE_SELECTED:
    return `Clue selected by ${logEntry.playerName}`;

  case LogType.BUZZ_IN:
    return `${logEntry.playerName} buzzed in, in ${logEntry.timeTaken.toFixed(
      2
    )}s.`;

  case LogType.ANSWER_CORRECT:
    return `${logEntry.playerName} answered "${logEntry.guess}", which was correct! And they get ${logEntry.pointsAwarded} points`;

  case LogType.ANSWER_INCORRECT:
    return `${logEntry.playerName} answered "${logEntry.guess}", which was incorrect, and they get -${logEntry.pointsDeducted} points.`;

  case LogType.PASS:
    if (logEntry.pointsDeducted) {
      return `${logEntry.playerName} passed, and they get -${logEntry.pointsDeducted} points.`;
    } else {
      return `${logEntry.playerName} passed`;
    }

  case LogType.TIMER_EXPIRED:
    if (logEntry.pointsDeducted) {
      return `${logEntry.playerName}'s answer timer expired, and they get -${logEntry.pointsDeducted} points`;
    } else {
      return `${logEntry.playerName}'s answer timer expired`;
    }

  case LogType.HINT_OPENED:
    return "No one got it... Opening hint.";

  case LogType.NO_CORRECT_ANSWER:
    return "No one got it...";

  case LogType.WAITING_FOR_ANSWER:
    return `Now waiting for answer from: ${logEntry.playerName}`;

  case LogType.SHOW_ANSWER:
    return `The answer was: ${logEntry.answer}`;

  case LogType.POINTS_ASSIGNED:
    if (logEntry.playerName || logEntry.teamName) {
      const name = logEntry.playerName || logEntry.teamName;
      return `Team "${name}" awarded ${logEntry.pointsAwarded} points.`;
    } else {
      return "No team awarded points for this clue.";
    }

  default:
    return `Unknown log entry type: ${(logEntry as any).type}`;
  }
}
