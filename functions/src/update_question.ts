import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { HttpsError } from 'firebase-functions/https';
import { Round, Clue } from './resources';
import { createHintBlanksFromAnswer } from './round_generator';

interface UpdateQuestionRequest {
  roomId: string;
  roundIdx: number;
  categoryIdx: number;
  clueIdx: number;
  updatedClue: {
    questionHTML: string;
    hint: string;
    answer: string;
    value: number;
    detailedFactsAboutAnswer?: string[];
  };
}

export const updateQuestion = functions.https.onCall(
  async (data: any, context: any) => {
    const req = data.data as UpdateQuestionRequest;

    if (!req.roomId) {
      throw new HttpsError('invalid-argument', 'Room ID is required');
    }

    if (req.roundIdx === undefined || req.roundIdx < 0) {
      throw new HttpsError('invalid-argument', 'Valid round index is required');
    }

    if (req.categoryIdx === undefined || req.categoryIdx < 0) {
      throw new HttpsError(
        'invalid-argument',
        'Valid category index is required'
      );
    }

    if (req.clueIdx === undefined || req.clueIdx < 0) {
      throw new HttpsError('invalid-argument', 'Valid clue index is required');
    }

    if (!req.updatedClue) {
      throw new HttpsError('invalid-argument', 'Updated clue data is required');
    }

    try {
      const db = admin.firestore();

      return await db.runTransaction(async transaction => {
        const roundRef = db
          .collection('rooms')
          .doc(req.roomId)
          .collection('roomRoundData')
          .doc(req.roundIdx.toString());

        const roundDoc = await transaction.get(roundRef);

        if (!roundDoc.exists) {
          throw new HttpsError('not-found', 'Round data not found');
        }

        const round = roundDoc.data() as Round;

        // Validate indices
        if (req.categoryIdx >= round.categories.length) {
          throw new HttpsError(
            'invalid-argument',
            'Category index out of range'
          );
        }

        if (req.clueIdx >= round.categories[req.categoryIdx].clues.length) {
          throw new HttpsError('invalid-argument', 'Clue index out of range');
        }

        // Create the updated clue
        const updatedClue: Clue = {
          questionSentences: [], // Keep empty for HTML questions
          questionHTML: req.updatedClue.questionHTML,
          hint: req.updatedClue.hint,
          answer: req.updatedClue.answer,
          value: req.updatedClue.value,
          detailedFactsAboutAnswer:
            req.updatedClue.detailedFactsAboutAnswer || [],
          hintBlanks: createHintBlanksFromAnswer(req.updatedClue.answer, 30)
        };

        // Update the clue in the round data
        round.categories[req.categoryIdx].clues[req.clueIdx] = updatedClue;

        // Save the updated round data
        transaction.set(roundRef, round);

        return updatedClue;
      });
    } catch (error) {
      console.error('Error updating question:', error);
      if (error instanceof HttpsError) {
        throw error;
      }
      throw new HttpsError('internal', 'Failed to update question');
    }
  }
);
