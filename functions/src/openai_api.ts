import OpenAI from "openai";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function queryOpenAI(
  prompt: string,
  llmAgent: string
): Promise<any> {
  try {
    const response = await openai.chat.completions.create({
      model: llmAgent,
      messages: [
        {
          role: "system",
          content:
            "You are a helpful assistant built to generate jeopardy style questions and return response in JSON format.",
        },
        {role: "user", content: prompt},
      ],
      temperature: 1,
    });
    const text = response.choices[0]?.message?.content;
    console.log("Raw response from OpenAI API:", text);
    return text;
  } catch (error) {
    console.error("Error querying OpenAI API:", error);
    throw new Error("Failed to fetch or parse response from OpenAI API");
  }
}
