import {SelectClueRequest} from "./services";
import {HttpsError} from "firebase-functions/https";
import {ClueState, GameMode, Room, RoomState} from "./resources";
import {repeatedPingRoom} from "./ping_room";
import {LogType} from "./structured_log";
import {addStructuredLogEntry} from "./log_utils";
import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {Transaction} from "firebase-admin/firestore";

function validate(
  roomData: Room,
  data: any,
  requestData: SelectClueRequest,
  _nowTs: number
) {
  if (roomData.mode === GameMode.GAMEMASTER) {
    return; // No need to validate for gamemaster mode
  }
  if (
    !requestData.roomId ||
    requestData.categoryIdx === undefined ||
    requestData.clueIdx === undefined ||
    !data.auth.uid
  ) {
    throw new HttpsError(
      "invalid-argument",
      "Room ID, category index, clue index, and player ID are required"
    );
  }

  // Check if the game is in progress
  if (roomData.gameState.gameProgress.type !== "RoomState") {
    throw new HttpsError("failed-precondition", "Game not in progress");
  }
  const roomState = roomData.gameState.gameProgress as RoomState;
  if (roomState.roundIdx != requestData.roundIdx) {
    throw new HttpsError("invalid-argument", "Invalid roundIdx");
  }
  // Check if it's the player's turn
  if (roomData.playerIds[roomState.currentPlayerIdx] !== data.auth.uid!) {
    throw new HttpsError("permission-denied", "Not your turn");
  }

  // Check if a clue is already selected
  if (roomState.roundStates[requestData.roundIdx].currentClueIdx != undefined) {
    throw new HttpsError(
      "invalid-argument",
      "Cannot select a clue while a clue is open"
    );
  }
  if (
    roomState.roundStates[requestData.roundIdx].categoryStates[
      requestData.categoryIdx
    ].clueStates[requestData.clueIdx].clueComplete
  ) {
    throw new HttpsError("already-exists", "Clue is already done");
  }
}

async function modify(
  txn: Transaction,
  roomData: Room,
  data: any,
  requestData: SelectClueRequest,
  nowTs: number
) {
  // Create the initial clue state
  const newClueState: ClueState = {
    hintOpened: false,
    showAnswer: false,
    buzzedInPlayerQueue: [],
    passedPlayers: [],
    buzzInTimerStartTime: nowTs,
    clueComplete: false,
    queueAnswerTurnIdx: -1,
    answerStartTime: 0,
    clueLog: {
      logs: [],
    },
    structuredClueLog: {
      entries: [],
    },
    answerExplanations: [],
  };

  // Add the structured log entry for clue selection
  if (roomData.mode != GameMode.GAMEMASTER) {
    const playerIdx = roomData.playerIds.findIndex((d) => d == data.auth.uid!);
    const playerName =
      roomData.playerInfos[playerIdx].userName || "Unknown Player";
    // Fetch the round data from Firestore
    const roundDoc = await txn.get(
      admin
        .firestore()
        .collection("rooms")
        .doc(roomData.roomId)
        .collection("roomRoundData")
        .doc(`${requestData.roundIdx}`)
    );

    if (!roundDoc.exists) {
      throw new HttpsError("not-found", "Round not found");
    }

    const round = roundDoc.data() as any;

    const categoryTitle =
      round.categories[requestData.categoryIdx].categoryTitle;
    const clueValue =
      round.categories[requestData.categoryIdx].clues[requestData.clueIdx]
        .value;

    // Create the structured log entry
    const logEntry = {
      type: LogType.CLUE_SELECTED as LogType.CLUE_SELECTED,
      timestamp: nowTs,
      playerId: data.auth.uid!,
      playerName: playerName,
      categoryTitle: categoryTitle,
      clueValue: clueValue,
    };

    // Add the log entry and generate the text log for backward compatibility
    addStructuredLogEntry(newClueState, logEntry);
  } else {
    // For gamemaster mode, just add a simple text log
    newClueState.clueLog.logs.push("Clue selected");
  }

  const roomState = roomData.gameState.gameProgress as RoomState;
  roomState.roundStates[requestData.roundIdx].categoryStates[
    requestData.categoryIdx
  ].clueStates[requestData.clueIdx] = newClueState;
  // Update the currentCategoryIdx and currentClueIdx in the RoundState
  roomState.roundStates[requestData.roundIdx].currentCategoryIdx =
    requestData.categoryIdx;
  roomState.roundStates[requestData.roundIdx].currentClueIdx =
    requestData.clueIdx;
  // If the game is turn based, just simulate a buzz-in, in order of the players.
  if (roomData.roomSettings?.gameMode == GameMode.TURN_BASED) {
    for (let i = 0; i < roomData.playerIds.length; i++) {
      newClueState.buzzedInPlayerQueue.push(
        roomData.playerIds[
          (i + roomState.currentPlayerIdx) % roomData.playerIds.length
        ]
      );
    }
  }

  return await repeatedPingRoom(
    txn,
    roomData,
    {playerId: data.auth.uid!, roomId: requestData.roomId},
    nowTs
  ); // Commit the changes
}

export const selectClue = functions.https.onCall(
  async (data: any, _context: any) => {
    const req = data.data as SelectClueRequest;
    const db = admin.firestore();
    return db.runTransaction(async (txn) => {
      const roomRef = db.collection("rooms").doc(req.roomId);
      const roomDoc = await txn.get(roomRef);
      if (!roomDoc.exists) {
        throw new HttpsError("not-found", "Room not found");
      }
      const roomData = roomDoc.data() as Room;
      const nowTs = Date.now();
      await validate(roomData, data, req, nowTs);
      const updatedRoomData = await modify(txn, roomData, data, req, nowTs);
      await txn.set(roomRef, updatedRoomData);
      return updatedRoomData;
    });
  }
);
