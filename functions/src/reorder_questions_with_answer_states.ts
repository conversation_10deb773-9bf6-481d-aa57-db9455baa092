import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {HttpsError} from "firebase-functions/https";
import {Round, Clue, Room, RoomState, ClueState} from "./resources";

interface ReorderQuestionsWithAnswerStatesRequest {
  roomId: string;
  roundIdx: number;
  reorderedCategories: {
    categoryIdx: number;
    clues: Clue[];
  }[];
}

export const reorderQuestionsWithAnswerStates = functions.https.onCall(
  async (data: any, _context: any) => {
    const req = data.data as ReorderQuestionsWithAnswerStatesRequest;
    
    if (!req.roomId) {
      throw new HttpsError("invalid-argument", "Room ID is required");
    }
    
    if (req.roundIdx === undefined || req.roundIdx < 0) {
      throw new HttpsError("invalid-argument", "Valid round index is required");
    }
    
    if (!req.reorderedCategories || !Array.isArray(req.reorderedCategories)) {
      throw new HttpsError("invalid-argument", "Reordered categories data is required");
    }

    try {
      const db = admin.firestore();
      
      return await db.runTransaction(async (transaction) => {
        const roomRef = db.collection("rooms").doc(req.roomId);
        const roundRef = roomRef.collection("roomRoundData").doc(req.roundIdx.toString());
        
        // Get both room data and round data
        const [roomDoc, roundDoc] = await Promise.all([
          transaction.get(roomRef),
          transaction.get(roundRef)
        ]);
        
        if (!roomDoc.exists || !roundDoc.exists) {
          throw new HttpsError("not-found", "Room or round data not found");
        }
        
        const roomData = roomDoc.data() as Room;
        const round = roundDoc.data() as Round;
        
        // Check if we're in an active game state
        const gameState = roomData.gameState?.gameProgress;
        if (gameState?.type === "RoomState") {
          const roomState = gameState as RoomState;
          const roundState = roomState.roundStates[req.roundIdx];
          
          if (roundState) {
            // Process each category that's being reordered
            req.reorderedCategories.forEach(({ categoryIdx, clues }) => {
              if (categoryIdx >= 0 && categoryIdx < round.categories.length) {
                const originalClues = round.categories[categoryIdx].clues;
                const categoryState = roundState.categoryStates[categoryIdx];
                
                // Create mapping from old positions to new positions
                const positionMapping = new Map<number, number>();
                clues.forEach((newClue, newIndex) => {
                  const oldIndex = originalClues.findIndex(oldClue => 
                    oldClue.questionHTML === newClue.questionHTML && 
                    oldClue.answer === newClue.answer
                  );
                  if (oldIndex !== -1) {
                    positionMapping.set(oldIndex, newIndex);
                  }
                });
                
                // Create new clue states array with preserved answer states
                const newClueStates: ClueState[] = new Array(clues.length);
                
                // Copy existing clue states to their new positions
                categoryState.clueStates.forEach((clueState, oldIndex) => {
                  const newIndex = positionMapping.get(oldIndex);
                  if (newIndex !== undefined) {
                    newClueStates[newIndex] = { ...clueState };
                  }
                });
                
                // Fill any missing positions with default clue states
                for (let i = 0; i < newClueStates.length; i++) {
                  if (!newClueStates[i]) {
                    newClueStates[i] = {
                      hintOpened: false,
                      showAnswer: false,
                      buzzedInPlayerQueue: [],
                      passedPlayers: [],
                      queueAnswerTurnIdx: -1,
                      clueComplete: false,
                      clueLog: { logs: [] },
                      answerExplanations: []
                    };
                  }
                }
                
                // Update the category state with reordered clue states
                roundState.categoryStates[categoryIdx].clueStates = newClueStates;
                
                // Update the round data with reordered clues
                round.categories[categoryIdx].clues = clues;
              }
            });
            
            // Update room data with modified game state
            roomData.gameState.gameProgress = roomState;
            
            // Save both room data and round data
            transaction.set(roomRef, roomData);
          }
        } else {
          // Not in active game state, just update the round data
          req.reorderedCategories.forEach(({ categoryIdx, clues }) => {
            if (categoryIdx >= 0 && categoryIdx < round.categories.length) {
              round.categories[categoryIdx].clues = clues;
            }
          });
        }
        
        // Save the updated round data
        transaction.set(roundRef, round);
        
        return { success: true, preservedAnswerStates: gameState?.type === "RoomState" };
      });
    } catch (error) {
      console.error("Error reordering questions with answer states:", error);
      if (error instanceof HttpsError) {
        throw error;
      }
      throw new HttpsError("internal", "Failed to reorder questions with answer states");
    }
  }
);
