import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {HttpsError} from "firebase-functions/https";
import {GameMode, Room} from "./resources";
import {UpdateRoomSettingsRequest} from "./services";

function validate(
  roomData: Room,
  data: any,
  _req: UpdateRoomSettingsRequest,
  _nowTs: number
) {
  // Only the host can update room settings
  if (data.auth?.uid !== roomData.host) {
    throw new HttpsError(
      "permission-denied",
      "Only the host can update room settings"
    );
  }

  // Validate that the room is in waiting room state
  if (roomData.gameState.gameProgress.type !== "WaitingRoom") {
    throw new HttpsError(
      "failed-precondition",
      "Room settings can only be updated in the waiting room"
    );
  }
}

function modify(
  roomData: Room,
  req: UpdateRoomSettingsRequest,
  _nowTs: number
): Room {
  // Update category titles if provided
  if (req.categoryTitles) {
    roomData.categoryTitles = req.categoryTitles;
  }

  // Update team names if provided (for gamemaster mode)
  if (req.teamNames) {
    // Ensure we're in GAMEMASTER mode when updating teams
    if (
      req.gameMode === GameMode.GAMEMASTER ||
      roomData.mode === GameMode.GAMEMASTER
    ) {
      roomData.mode = GameMode.GAMEMASTER; // Ensure mode is set to GAMEMASTER
      roomData.playerIds = req.teamNames;
      // Update player infos to match team names
      roomData.playerInfos = req.teamNames.map((teamName, index) => {
        // Preserve existing scores if available
        const existingScore = roomData.playerInfos[index]?.score || 0;
        return {
          userName: teamName,
          score: existingScore,
        };
      });
    }
  }

  // Update game mode if provided and not updating teams
  if (req.gameMode && !req.teamNames) {
    roomData.mode = req.gameMode;
  }

  // Update number of questions if provided
  if (req.numQuestions !== undefined) {
    if (!roomData.roomSettings) {
      roomData.roomSettings = {
        gameMode: roomData.mode,
        buzzInTimerDurationMillis: 60000, // Default values
        answerDurationMillis: 60000,
        showAnswerDurationMillis: 10000,
        initialThinkingDurationMillis: 20000,
        llmAgent: req.llmAgent || {
          provider: "Gemini",
          modelName: "gemini-2.0-flash-thinking-exp-01-21",
        },
      };
      roomData.roomSettings.numQuestions = req.numQuestions;
    } else {
      roomData.roomSettings.numQuestions = req.numQuestions;
    }
  }

  // Update LLM agent if provided
  if (req.llmAgent && !req.timerSettings) {
    if (!roomData.roomSettings) {
      roomData.roomSettings = {
        gameMode: roomData.mode,
        buzzInTimerDurationMillis: 60000, // Default values
        answerDurationMillis: 60000,
        showAnswerDurationMillis: 10000,
        initialThinkingDurationMillis: 20000,
        llmAgent: req.llmAgent,
      };
    } else {
      roomData.roomSettings.llmAgent = req.llmAgent;
    }
  }

  // Update timer settings if provided
  if (req.timerSettings) {
    if (!roomData.roomSettings) {
      roomData.roomSettings = {
        gameMode: req.timerSettings.gameMode,
        buzzInTimerDurationMillis: req.timerSettings.buzzInTimerDurationMillis,
        answerDurationMillis: req.timerSettings.answerDurationMillis,
        showAnswerDurationMillis: req.timerSettings.showAnswerDurationMillis,
        initialThinkingDurationMillis:
          req.timerSettings.initialThinkingDurationMillis,
        llmAgent: req.timerSettings.llmAgent,
      };
    } else {
      roomData.roomSettings.gameMode = req.timerSettings.gameMode;
      roomData.roomSettings.buzzInTimerDurationMillis =
        req.timerSettings.buzzInTimerDurationMillis;
      roomData.roomSettings.answerDurationMillis =
        req.timerSettings.answerDurationMillis;
      roomData.roomSettings.showAnswerDurationMillis =
        req.timerSettings.showAnswerDurationMillis;
      roomData.roomSettings.initialThinkingDurationMillis =
        req.timerSettings.initialThinkingDurationMillis;
      roomData.roomSettings.llmAgent = req.timerSettings.llmAgent;
    }

    // Also update the room mode to match the game mode in timer settings
    // This ensures consistency between room.mode and roomSettings.gameMode
    // But don't override GAMEMASTER mode if we're updating teams
    if (!req.teamNames && req.timerSettings.gameMode !== GameMode.GAMEMASTER) {
      roomData.mode = req.timerSettings.gameMode;
    } else if (req.timerSettings.gameMode === GameMode.GAMEMASTER) {
      // If explicitly setting to GAMEMASTER mode, always update
      roomData.mode = GameMode.GAMEMASTER;
    }
  }

  return roomData;
}

export const updateRoomSettings = functions.https.onCall(
  async (data: any, _context: any) => {
    const req = data.data as UpdateRoomSettingsRequest;
    const db = admin.firestore();

    return db.runTransaction(async (txn) => {
      const roomRef = db.collection("rooms").doc(req.roomId);
      const roomDoc = await txn.get(roomRef);

      if (!roomDoc.exists) {
        throw new HttpsError("not-found", "Room not found");
      }

      const roomData = roomDoc.data() as Room;
      const nowTs = Date.now();

      validate(roomData, data, req, nowTs);
      const updatedRoom = modify(roomData, req, nowTs);

      await txn.set(roomRef, updatedRoom);
      return updatedRoom;
    });
  }
);
