import {HttpsError} from "firebase-functions/https";
import {ClueState, MemorableQuestion, Room, RoomState} from "./resources";
import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {GameMasterActionsRequest} from "./services";
export function validate(
  roomData: Room,
  data: any,
  requestData: GameMasterActionsRequest,
  _nowTs: number
) {
  if (!requestData.roomId) {
    throw new HttpsError("invalid-argument", "Room ID is required.");
  }

  // Skip validation for END_GAME action
  if (requestData.action === "END_GAME") {
    return;
  }

  // For other actions, validate roundIdx, categoryIdx, and clueIdx
  if (
    requestData.roundIdx === undefined ||
    requestData.categoryIdx === undefined ||
    requestData.clueIdx === undefined
  ) {
    throw new HttpsError(
      "invalid-argument",
      "roundIdx, categoryIdx, and clueIdx are required for this action."
    );
  }

  if (roomData.playerIds.length === 0) {
    throw new HttpsError(
      "failed-precondition",
      "No players found in the room."
    );
  }
  if (roomData.host != data.auth.uid) {
    throw new HttpsError(
      "permission-denied",
      "Only the host is allowed to assign points"
    );
  }

  const roomState = roomData.gameState.gameProgress as RoomState;
  if (roomState.roundIdx !== requestData.roundIdx) {
    throw new HttpsError("invalid-argument", "Invalid roundIdx.");
  }

  const clueState: ClueState =
    roomState.roundStates[requestData.roundIdx].categoryStates[
      requestData.categoryIdx
    ].clueStates[requestData.clueIdx];

  if (clueState.clueComplete) {
    throw new HttpsError(
      "failed-precondition",
      "Cannot take actions on a completed clue."
    );
  }
}

export async function modify(
  roomData: Room,
  requestData: GameMasterActionsRequest,
  _nowTs: number
) {
  // Handle END_GAME action
  if (requestData.action === "END_GAME") {
    // Get the current RoomState before transitioning
    const currentRoomState = roomData.gameState.gameProgress as RoomState;

    // We need to import the selectMemorableQuestions function from ping_room.ts
    // Since we can't easily do that, we'll create a simplified version here
    // In a real implementation, you would refactor this to avoid code duplication
    const memorableQuestions: MemorableQuestion[] = [];

    try {
      // Get the round data to access questions
      const roundIdx = currentRoomState.roundIdx;
      const roundDoc = await admin
        .firestore()
        .collection("rooms")
        .doc(roomData.roomId)
        .collection("roomRoundData")
        .doc(`${roundIdx}`)
        .get();

      if (roundDoc.exists) {
        const roundData = roundDoc.data();

        // Create some sample memorable questions
        if (
          roundData &&
          roundData.categories &&
          roundData.categories.length > 0
        ) {
          // Find a few completed questions to highlight
          for (
            let categoryIdx = 0;
            categoryIdx < roundData.categories.length;
            categoryIdx++
          ) {
            const category = roundData.categories[categoryIdx];
            if (category.clues && category.clues.length > 0) {
              for (
                let clueIdx = 0;
                clueIdx < category.clues.length;
                clueIdx++
              ) {
                const clue = category.clues[clueIdx];
                const clueState =
                  currentRoomState.roundStates[roundIdx].categoryStates[
                    categoryIdx
                  ].clueStates[clueIdx];

                if (clueState.clueComplete && memorableQuestions.length < 3) {
                  memorableQuestions.push({
                    category: category.categoryTitle,
                    question:
                      clue.questionHTML || clue.questionSentences.join(" "),
                    answer: clue.answer,
                    value: clue.value,
                    reason:
                      "This was one of the memorable questions from the game.",
                  });
                }
              }
            }
          }
        }
      }
    } catch (error) {
      console.error("Error creating memorable questions:", error);
    }

    // Update the game state to RoomSummary while preserving the original state
    roomData.gameState = {
      gameProgress: {
        type: "RoomSummary",
        // Store the original RoomState for access in the winner screen
        originalState: currentRoomState,
        // Include the memorable questions
        memorableQuestions: memorableQuestions,
      },
    };
    return roomData;
  }

  // Handle other actions
  const roomState = roomData.gameState.gameProgress as RoomState;

  const clueState: ClueState =
    roomState.roundStates[requestData.roundIdx!].categoryStates[
      requestData.categoryIdx!
    ].clueStates[requestData.clueIdx!];
  if (requestData.action == "toggleAnswer") {
    clueState.showAnswer = !clueState.showAnswer;
  } else if (requestData.action == "toggleHint") {
    // Toggle the hint state
    clueState.hintOpened = !clueState.hintOpened;

    // If hint is being shown, reset the countdown timer if it's active
    if (
      clueState.hintOpened &&
      clueState.countdownActive &&
      clueState.countdownStartTime
    ) {
      clueState.countdownStartTime = _nowTs;
    }
  } else if (requestData.action == "startCountdown") {
    // Start a new countdown timer
    clueState.countdownStartTime = _nowTs;
    clueState.countdownDurationSeconds =
      requestData.countdownDurationSeconds || 60; // Default to 60 seconds if not specified
    clueState.countdownActive = true;
  } else if (requestData.action == "resetCountdown") {
    // Reset the countdown timer
    clueState.countdownStartTime = _nowTs;
    clueState.countdownActive = true;
  } else if (requestData.action == "pauseCountdown") {
    // Pause the countdown timer (keep the timer visible but inactive)
    clueState.countdownActive = false;
  }
  return roomData;
}

export const gameMasterActions = functions.https.onCall(
  async (data, _context) => {
    const req = data.data as GameMasterActionsRequest;
    const db = admin.firestore();
    return db.runTransaction(async (txn) => {
      const roomRef = db.collection("rooms").doc(req.roomId);
      const roomDoc = await txn.get(roomRef);
      if (!roomDoc.exists) {
        throw new HttpsError("not-found", "Room not found");
      }
      const roomData = roomDoc.data() as Room;
      const nowTs = Date.now();

      // Ensure the user is the host
      if (data.auth && roomData.host != data.auth.uid) {
        throw new HttpsError(
          "permission-denied",
          "Only the host is allowed to perform this action"
        );
      }

      // Validate other parameters if needed
      if (req.action !== "END_GAME") {
        validate(roomData, data, req, nowTs);
      }

      const updatedRoom = await modify(roomData, req, nowTs);
      await txn.set(roomRef, updatedRoom);
      return updatedRoom;
    });
  }
);
