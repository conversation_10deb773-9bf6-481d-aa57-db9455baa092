import {JoinRoomRequest} from "./services";
import {HttpsError} from "firebase-functions/https";
import * as admin from "firebase-admin";
import {GameMode, Room} from "./resources";
import * as functions from "firebase-functions";

function validate(
  roomData: Room,
  data: any,
  req: JoinRoomRequest,
  _nowTs: number
) {
  if (!req.roomId) {
    throw new HttpsError("invalid-argument", "Room ID is required");
  }

  if (roomData.mode === GameMode.GAMEMASTER) {
    throw new HttpsError(
      "failed-precondition",
      "Cannot join gamemaster mode room"
    );
  }

  if (roomData.gameState.gameProgress.type !== "WaitingRoom") {
    // Game has already started
    throw new HttpsError("failed-precondition", "Game already started");
  }

  const players = roomData.playerIds || {};
  console.log(players);
  if (players.some((p) => p == data.auth!.uid)) {
    // Player already in the room
    throw new HttpsError("already-exists", "Player already in this room");
  }
}

export const joinRoom = functions.https.onCall(
  async (data: any, _context: any) => {
    const req = data.data as JoinRoomRequest;
    const db = admin.firestore();
    return db.runTransaction(async (txn) => {
      const roomRef = db.collection("rooms").doc(req.roomId);
      const roomDoc = await txn.get(roomRef);
      if (!roomDoc.exists) {
        throw new HttpsError("not-found", "Room not found");
      }
      const roomData = roomDoc.data() as Room;
      const nowTs = Date.now();
      validate(roomData, data, req, nowTs);
      roomData.playerIds.push(data.auth!.uid);
      roomData.playerInfos.push({
        userName: req.userName,
        score: 0,
        photoUrl: req.photoUrl,
      });
      txn.set(roomRef, roomData);
      return roomData;
    });
  }
);
