import {HttpsError} from "firebase-functions/https";
import {ClueState, GameMode, Room, RoomState} from "./resources";
import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {SkipShowAnswerRequest, PingRoomRequest} from "./services";

// Import the pingRoom function
import {repeatedPingRoom} from "./ping_room";

function validate(
  roomData: Room,
  requestData: SkipShowAnswerRequest,
  _nowTs: number
) {
  if (
    !requestData.roomId ||
    requestData.roundIdx === undefined ||
    requestData.categoryIdx === undefined ||
    requestData.clueIdx === undefined ||
    !requestData.playerId
  ) {
    throw new HttpsError(
      "invalid-argument",
      "Room ID, RoundIdx, categoryIdx, clueIdx, and playerId are required"
    );
  }

  if (!roomData.playerIds.some((p) => p == requestData.playerId)) {
    throw new HttpsError("invalid-argument", "PlayerId not part of this room");
  }
  // Check if the game is in progress
  if (roomData.gameState.gameProgress.type !== "RoomState") {
    throw new HttpsError("failed-precondition", "Game not in progress");
  }
  const roomState = roomData.gameState.gameProgress as RoomState;
  if (roomState.roundIdx != requestData.roundIdx) {
    throw new HttpsError("invalid-argument", "Invalid roundIdx");
  }
  if (
    roomState.roundStates[requestData.roundIdx].currentCategoryIdx !=
    requestData.categoryIdx
  ) {
    throw new HttpsError("invalid-argument", "Invalid categoryIdx");
  }
  if (
    roomState.roundStates[requestData.roundIdx].currentClueIdx !=
    requestData.clueIdx
  ) {
    throw new HttpsError("invalid-argument", "Invalid clueIdx");
  }
  const clueState = roomState.roundStates[requestData.roundIdx].categoryStates[
    requestData.categoryIdx
  ].clueStates[requestData.clueIdx] as ClueState;

  if (
    clueState.skipShowAnswer &&
    clueState.skipShowAnswer.some((p) => p == requestData.playerId)
  ) {
    throw new HttpsError(
      "invalid-argument",
      "This player has already skipped show answer."
    );
  }
  if (clueState.clueComplete) {
    throw new HttpsError("invalid-argument", "Clue is already complete.");
  }
}

export const skipShowAnswer = functions.https.onCall(
  async (data: any, _context: any) => {
    const req = data.data as SkipShowAnswerRequest;
    const db = admin.firestore();
    return await db.runTransaction(async (txn) => {
      const roomRef = db.collection("rooms").doc(req.roomId);
      const roomDoc = await txn.get(roomRef);
      if (!roomDoc.exists) {
        throw new HttpsError("not-found", "Room not found");
      }
      const roomData = roomDoc.data() as Room;
      if (roomData.mode != GameMode.GAMEMASTER) {
        req.playerId = data.auth.uid!;
      }
      const nowTs = Date.now();
      validate(roomData, req, nowTs);
      const roomState = roomData.gameState.gameProgress as RoomState;
      const newClueState: ClueState =
        roomState.roundStates[req.roundIdx].categoryStates[req.categoryIdx]
          .clueStates[req.clueIdx];
      if (!newClueState.skipShowAnswer) {
        newClueState.skipShowAnswer = [];
      }
      newClueState.skipShowAnswer.push(req.playerId);
      if (newClueState.skipShowAnswer.length == roomData.playerIds.length) {
        newClueState.clueComplete = true;
        roomState.roundStates[req.roundIdx].currentClueIdx = undefined;
        roomState.roundStates[req.roundIdx].currentCategoryIdx = undefined;
        const pingReq: PingRoomRequest = {
          roomId: req.roomId,
          playerId: req.playerId,
        };
        const newRoomData = await repeatedPingRoom(
          txn,
          roomData,
          pingReq,
          nowTs
        );

        // Save the updated room data
        await txn.set(roomRef, JSON.parse(JSON.stringify(newRoomData)));
      } else {
        await txn.set(roomRef, roomData);
      }
      return roomData;
    });
  }
);
