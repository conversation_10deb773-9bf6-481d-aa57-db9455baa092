rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /players/{playerId} {
      // Players DB should only be read/modified by registerPlayer.
      allow read, create, update, delete: if false;
    }


    match /rooms/{roomId} {
      // Allow reading only if a player is part of a room.
      allow read: if true;
      // allow read: if request.auth != null && (
      //   request.auth.uid in resource.data.playerIds ||
      //   resource.data.mode == 3
      // );

      // Only firebase functions are allowed to update room.
      allow create, update, delete: if false;
      
      match /roomRoundData/{roundDataId} {
        allow read: if true;
        // allow read: if request.auth != null && (
        //   get(/databases/$(database)/documents/rooms/$(roomId)).data.playerIds.hasAny([request.auth.uid]) ||
        //   get(/databases/$(database)/documents/rooms/$(roomId)).data.mode == 3
        // );
        allow create, update, delete: if false;
      }
      match /mediaCommands/latest {
        allow read: if true;
        // allow read: if request.auth != null && (
        //   get(/databases/$(database)/documents/rooms/$(roomId)).data.playerIds.hasAny([request.auth.uid]) ||
        //   get(/databases/$(database)/documents/rooms/$(roomId)).data.mode == 3
        // );
        allow create, update, delete: if false;
      }

      match /messages/{messageId} {
        // Allow reading messages if the playerId is in the room's playerIds
        allow read, create, update, delete: if get(/databases/$(database)/documents/rooms/$(roomId)).data.playerIds.hasAny([request.auth.uid]);

      //  allow read, create, update, delete: if true;
      }

      match /rounds/{roundIdx}/categories/{categoryIdx}/clues/{clueIdx}/hintOpened/{hintOpened}/buzzerQueue/{playerId} {
        // TODO: Validate that the resource.data.buzzTime==request.time.
        allow read, create: if get(/databases/$(database)/documents/rooms/$(roomId)).data.playerIds.hasAny([request.auth.uid]);


        allow update, delete: if false;
      }
    }
  }
}
