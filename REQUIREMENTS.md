# JeopardyGPT Requirements Document

## Overview

JeopardyGPT is a web-based trivia game application inspired by the popular TV show Jeopardy. The game allows users to create and join game rooms, play with friends, and compete by answering questions generated by AI language models. The application supports multiple game modes and features a responsive, material design-based UI.

## Game Modes

### 1. Buzzer Mode

#### Overview
- Competitive mode where players race to answer questions
- Simulates the classic TV show experience with a buzzer mechanic
- Rewards quick thinking and fast reactions
- Suitable for players who enjoy competitive gameplay

#### Detailed Mechanics
- When a clue is revealed, all players can see the question simultaneously
- A configurable buzz-in timer (default: 60 seconds) starts counting down
- Players must press the "Buzz" button before the timer expires
- The first player to buzz in gets priority to answer
- If multiple players buzz in, a queue is formed based on buzz-in time
- The player who buzzed in first has a configurable time (default: 60 seconds) to submit their answer
- If the first player answers incorrectly or time expires, the next player in the buzz-in queue gets a chance
- Players who have already buzzed in cannot buzz in again for the same clue
- Players can pass their turn if they don't know the answer
- Points equal to the clue's value are awarded for correct answers
- Points are not deducted for incorrect answers
- The clue is closed after all buzzed-in players have answered or when the host skips to the next clue

#### UI Elements
- Buzz-in button with timer display
- Visual indication of who has buzzed in
- Answer submission form with timer
- Pass button
- History log showing buzz-in order and results

### 2. Turn-Based Mode

#### Overview
- Strategic mode where players take turns selecting and answering clues
- More structured gameplay with less time pressure
- Ensures all players get equal opportunities to participate
- Suitable for casual play or educational settings

#### Detailed Mechanics
- Players take turns in a predetermined order
- The current player selects a clue from the board
- When a clue is selected, all players are automatically placed in a queue to answer
- The queue order starts with the current player and follows the player order
- The current player gets an additional "initial thinking time" (default: 20 seconds) before their answer timer starts
- Each player has a configurable time (default: 60 seconds) to submit their answer
- If a player answers incorrectly or time expires, the next player in the queue gets a chance
- Players can pass their turn if they don't know the answer
- Points equal to the clue's value are awarded for correct answers
- Points are not deducted for incorrect answers
- After a clue is completed, the turn passes to the next player in order
- The game continues until all clues are answered or the host ends the game

#### UI Elements
- Visual indication of whose turn it is
- Answer submission form with timer
- Pass button
- History log showing answer attempts and results

### 3. Gamemaster Mode

#### Overview
- Host-controlled mode for structured, facilitated gameplay
- Designed for educational settings, team-based play, or presentations
- Allows for flexible pacing and manual scoring
- Suitable for classroom use, trivia nights, or corporate events

#### Detailed Mechanics
- Before starting the game, the host defines team names
- No individual players - scoring is tracked by team
- The host (gamemaster) controls all aspects of the game flow
- No automatic timers - the gamemaster determines the pace
- The gamemaster selects clues from the board
- When a clue is revealed, the gamemaster can:
  - Show or hide hints to provide additional context
  - Show or hide answers when teams have finished guessing
  - Regenerate questions if they're not suitable
  - Manually assign points to teams based on verbal answers
- Teams verbally provide answers (outside the system)
- The gamemaster judges answer correctness and assigns points accordingly
- Points can be assigned to any team, multiple teams, or no teams
- The game continues until all clues are answered or the gamemaster ends the game

#### UI Elements
- Special gamemaster controls panel
- Team selection dropdown for point assignment
- Show/hide hint button
- Show/hide answer button
- Regenerate question button
- Assign points button
- Cast room link sharing for displaying the game on a shared screen

## Game Flow

### Room Creation
1. User creates a new room
2. System generates a unique room ID
3. Creator becomes the host/admin of the room
4. Room is initialized in "WaitingRoom" state

### Waiting Room
1. Players join the room using the room ID
2. Host configures game settings:
   - Game mode (Buzzer, Turn-Based, Gamemaster)
   - Timer durations:
     - Buzz-in timer (default: 60 seconds): Time allowed for players to buzz in after a clue is revealed
     - Answer timer (default: 60 seconds): Time allowed for a player to submit their answer
     - Show answer timer (default: 10 seconds): Time the answer is displayed before moving to the next clue
     - Initial thinking timer (default: 20 seconds): Additional time given to the first player in Turn-Based mode
   - LLM agent selection for question generation:
     - OpenAI models (e.g., GPT-4)
     - Gemini models
     - DeepSeekAI models
     - Fake provider (for testing)
   - Number of questions per category (default: 5)
   - Number of categories (default: 5)
   - Category titles (user-defined or generated)
   - Team names (for Gamemaster mode)
3. Non-admin users can see the settings in read-only mode with visual indicators showing when settings are modified
4. Host can share the room link via a copy-to-clipboard button
5. Host starts the game when ready by clicking the "Start Game" button

### Question Generation
1. Game enters "GeneratingQuestions" state when host clicks "Start Game"
2. System shows progress indicators to all users:
   - Percentage-based progress bar showing overall completion
   - Animated text messages describing the current generation step
   - Current category being processed
   - Question index within the category
3. AI generates questions based on selected categories:
   - Each category has the specified number of questions (default: 5)
   - Questions are generated with increasing difficulty/value (200, 400, 600, 800, 1000 points)
   - The AI model creates high-quality, Jeopardy-style clues that:
     - Follow the "answer in the form of a question" format
     - Are factually accurate and educational
     - Have a clear, single correct answer
     - Maintain consistent difficulty within point values
     - Avoid ambiguity and subjective content
   - Each question includes:
     - Question text in HTML format with appropriate formatting
     - Text optimized for Text-to-Speech (TTS) reading
     - A hint to help players without giving away the answer
     - The correct answer with acceptable variations
     - Hint blanks showing the structure of the answer (e.g., "_ _ _ _ _")
     - Detailed facts about the answer for educational value
     - A point value corresponding to difficulty
4. Progress updates are sent to all players in real-time:
   - Updates occur approximately every 1.5 seconds
   - Progress is stored on the server to ensure all users see the same state
   - Messages are dynamically generated to provide context about what's happening
5. Once generation is complete (100% progress), game automatically transitions to "RoomState"

### Gameplay (Buzzer Mode)
1. Game board displays with categories and clue values arranged in a grid
2. Current player selects a clue by clicking on its value
3. Clue is displayed to all players with an animation effect
4. Buzz-in timer starts immediately and is visible to all players
5. Players can:
   - Click the "Buzz" button to attempt to answer (enabled only during the buzz-in period)
   - See who has already buzzed in via the history log
6. When a player buzzes in:
   - Their buzz-in is recorded with a timestamp
   - A message appears in the clue log showing who buzzed in and how quickly
   - If they're first, they immediately get to answer
   - Otherwise, they wait in the queue
7. The answering player:
   - Sees an answer input field and a timer
   - Can type their answer and submit it
   - Can pass their turn if they don't know the answer
8. System evaluates the answer:
   - Compares the submitted answer to the correct answer using intelligent matching:
     - Case-insensitive comparison
     - Ignores minor spelling variations
     - Accepts common synonyms and alternative phrasings
     - Handles articles and non-essential words ("the", "a", etc.)
   - Provides immediate feedback on correctness
   - Shows an explanation for why the answer is correct or incorrect
   - For incorrect answers, may provide hints about what was wrong
   - Creates an answer explanation object with correctness flag and explanation text
   - Logs the answer attempt in the clue history
9. If the answer is correct:
   - Points equal to the clue value are added to the player's score
   - The answer is revealed to all players
   - After the show-answer timer expires, the clue closes
10. If the answer is incorrect or the player passes:
    - The next player in the buzz-in queue gets a chance to answer
    - If no players remain in the queue, the answer is revealed
11. Game continues until all clues are answered or host ends the game

### Gameplay (Turn-Based Mode)
1. Game board displays with categories and clue values arranged in a grid
2. Visual indicator shows whose turn it is to select a clue
3. Current player selects a clue by clicking on its value
4. Clue is displayed to all players with an animation effect
5. All players are automatically placed in a queue starting with the current player
6. The current player:
   - Gets initial thinking time (additional time before their answer timer starts)
   - Sees an answer input field and a timer
   - Can type their answer and submit it
   - Can pass their turn if they don't know the answer
7. System evaluates the answer:
   - Compares the submitted answer to the correct answer using intelligent matching:
     - Case-insensitive comparison
     - Ignores minor spelling variations
     - Accepts common synonyms and alternative phrasings
     - Handles articles and non-essential words ("the", "a", etc.)
   - Provides immediate feedback on correctness
   - Shows an explanation for why the answer is correct or incorrect
   - For incorrect answers, may provide hints about what was wrong
   - Creates an answer explanation object with correctness flag and explanation text
   - Logs the answer attempt in the clue history
8. If the answer is correct:
   - Points equal to the clue value are added to the player's score
   - The answer is revealed to all players
   - After the show-answer timer expires, the clue closes
   - The turn passes to the next player in order
9. If the answer is incorrect or the player passes:
   - The next player in the queue gets a chance to answer
   - If no players remain in the queue, the answer is revealed
   - The turn passes to the next player in order
10. Game continues until all clues are answered or host ends the game

### Gameplay (Gamemaster Mode)
1. Game board displays with categories and clue values arranged in a grid
2. Host selects a clue by clicking on its value
3. Clue is displayed to all players with an animation effect
4. Teams discuss and verbally provide answers (outside the system)
5. Host has special controls to manage the game flow:
   - "Show/Hide Hint" button: Reveals or hides the hint to provide additional context
   - "Show/Hide Answer" button: Reveals or hides the correct answer when teams have finished guessing
   - "Regenerate Question" button: Requests a new question if the current one is problematic
   - Team selection dropdown: Allows selecting which team(s) to award points to
   - "Assign Points" button: Awards points to the selected team(s)
6. When the host shows the answer:
   - The correct answer is displayed to all players
   - Detailed facts about the answer are shown for educational value
7. When the host assigns points:
   - Points equal to the clue value are added to the selected team's score
   - The scoreboard updates in real-time
   - The clue closes automatically
8. The host can share a special "Cast Room" link that displays only the game board and clues (no controls or answers)
   - Useful for displaying the game on a shared screen or projector
   - Integrated with Google Cast API for streaming to Chromecast devices
   - Cast button appears when compatible devices are detected on the network
   - Automatic session management and reconnection
   - Optimized layout for TV display
9. Game continues until all clues are answered or host ends the game by clicking the "End Game" button

### Game End
1. Host can end the game at any time by clicking the "End Game" button (appears as a trophy icon)
2. Game automatically transitions to "RoomSummary" state
3. Winner's screen displays:
   - Game over announcement
   - Winner announcement (or tie announcement if multiple players have the highest score)
   - Trophy icon for visual celebration
   - Leaderboard showing all players ranked by score
   - Visual highlighting of winners and the current user
   - Game statistics including:
     - Highest score achieved
     - Number of players/teams
     - Result type (single winner or tie)
4. Players can view their performance in the leaderboard
5. The system doesn't support multiple games in the same room, so there is no "Play Again" functionality

## Key Components

### Room
- Unique room ID (randomly generated alphanumeric code)
- Host player ID (UID of the room creator)
- Game mode (TURN_BASED, BUZZER, or GAMEMASTER)
- Player IDs and information:
  - Array of player UIDs
  - Array of player info objects containing:
    - Score (numeric value)
    - Username (display name)
    - Photo URL (optional profile picture)
- Game state (current state of the game)
- Room settings (configurable options)
- Category titles (array of strings)
- Creation timestamp

### Game Progress States
1. **WaitingRoom**: Initial state when players join
   - Players can join/leave
   - Host configures settings
   - No gameplay yet
2. **GeneratingQuestions**: State during question generation
   - Shows progress percentage (0-100%)
   - Displays current generation message
   - Indicates which category is being generated
   - Shows question index within category
   - Records start time of generation
3. **RoomState**: Active gameplay state
   - Contains current round index
   - Tracks current player index
   - Maintains array of round states
   - Each round state contains:
     - Current category and clue indices
     - Array of category states
     - Each category state contains array of clue states
     - Each clue state tracks:
       - Whether hint is opened
       - Whether answer is shown
       - Queue of buzzed-in players
       - List of passed players
       - Timer start times
       - Log of player actions
4. **RoomSummary**: End-of-game state showing results
   - Final scores
   - Player rankings
   - Game statistics
   - No gameplay functionality

### Room Settings
- Game mode (enum: TURN_BASED, BUZZER, or GAMEMASTER)
- Timer durations (all in milliseconds):
  - Buzz-in timer (buzzInTimerDurationMillis): Time allowed for players to buzz in (default: 60000ms)
  - Answer timer (answerDurationMillis): Time allowed for a player to submit their answer (default: 60000ms)
  - Show answer timer (showAnswerDurationMillis): Time the answer is displayed before moving to the next clue (default: 10000ms)
  - Initial thinking timer (initialThinkingDurationMillis): Additional time given to the first player in Turn-Based mode (default: 20000ms)
- LLM agent configuration:
  - Provider (OpenAI, Gemini, DeepSeekAI, or Fake)
  - Model name (specific model to use, e.g., "gpt-4")
- Number of questions per category (numQuestions, default: 5)
- Number of categories (numCategories, default: 5)

### Game Board
- Grid layout with categories as columns and clue values as rows
- Multiple categories (default: 5)
- Each category has multiple clues (default: 5 per category)
- Clues of different point values (increasing from top to bottom)
- Visual indication of answered clues (grayed out or disabled)
- Visual indication of the current player's turn (in Turn-Based mode)
- Category titles displayed at the top of each column
- Clue values displayed in each cell (e.g., 200, 400, 600, 800, 1000)

### Clue
- Question text (HTML formatted for rich display)
- Question sentences (array of individual sentences for processing)
- Hint (additional context to help players)
- Hint blanks (representation of the answer with blanks, e.g., "_ _ _ _ _")
- Answer (correct response)
- Point value (numeric value of the clue)
- Detailed facts about the answer (array of educational facts)
- Optional flag indicating if it's a good clue (for quality control)

### Clue State
- Hint opened flag (whether hint has been revealed)
- Show answer flag (whether answer has been revealed)
- Buzzed-in player queue (ordered list of players who buzzed in)
- Passed players list (players who skipped their turn)
- Queue answer turn index (current position in the buzz-in queue)
- Buzz-in timer start time (timestamp when clue was revealed)
- Answer start time (timestamp when current player started answering)
- Show answer start time (timestamp when answer was revealed)
- Clue complete flag (whether clue has been fully processed)
- Clue log (history of player actions and events)
- Answer explanations (feedback on player answers)

### Chat Message
- Sender information (player ID, username, avatar)
- Message content (text with optional formatting)
- Timestamp (when the message was sent)
- Message type (user message, system notification, etc.)
- Read status (which players have seen the message)
- Optional mentions/tags of other players
- Unique message ID for reference and ordering

## User Interface Requirements

### General UI
- Responsive design that works well on all device sizes
- No hardcoded pixel values
- Material Design 3 principles
- Use of material variables in CSS
- Consistent typography using var(--mat-sys-body-large)
- Theme support with ability to switch between light mode and dark mode
- Fun and playful color scheme that maintains appropriate contrast
- Smooth and modern UI with engaging animations
- Animated transitions to mask server interactions and loading states
- Optimized performance for minimal perceived latency

### Login UI
- Clean, simple login screen
- Google sign-in button with logo
- Anonymous sign-in option for quick access
- Responsive layout that works on all device sizes
- Error handling for failed authentication attempts
- Redirect functionality to return users to their intended destination after login
- Loading indicators during authentication process

### Waiting Room UI
- Room code display ("Waiting Room: [roomId]")
- Share button with tooltip (copies room link to clipboard)
- Player list card showing:
  - Player icons/avatars
  - Player names
  - Join status
- Game settings configuration for admin:
  - Category input with dynamic list
  - Team names input (for Gamemaster mode)
  - Number of questions slider (1-10)
  - Number of categories slider (1-10)
  - LLM agent selection dropdown
  - Game mode selection dropdown
  - Timer settings with sliders:
    - Buzz-in timer (0-120 seconds)
    - Answer timer (0-120 seconds)
    - Show answer timer (0-60 seconds)
    - Initial thinking timer (0-60 seconds)
  - Visual indicators for modified settings
- Read-only settings display for non-admin users:
  - All the same settings as admin view but non-interactive
  - Updates in real-time as admin changes settings
  - Info icons with tooltips explaining each setting
- Start game button (admin only) with play icon
- Loading indicator during question generation

### Question Generation UI
- Material Design progress bar showing percentage completion (0-100%)
- Progress percentage text ("XX% Complete")
- Animated progress messages that change as generation proceeds
- Current category being processed ("Category: [name]")
- Visual indication of which question is being generated
- Consistent updates across all connected clients
- Responsive layout that works on all device sizes
- Material Design 3 styling with appropriate color variables

### Game Board UI
- Responsive grid layout that adapts to screen size
- Category titles in column headers with appropriate styling
- Clue values in cells with consistent formatting
- Visual differentiation between:
  - Available clues (clickable)
  - Selected clue (currently in play)
  - Completed clues (already answered)
- Current player indicator (in Turn-Based mode)
- End game button (trophy icon) for the host
- Material Design elevation and shadows for depth
- Consistent typography using material design variables
- Appropriate color contrast for readability
- Responsive sizing that works on mobile and desktop

### Clue UI
- Animated entrance effect when clue is revealed
- Category and value display at the top ("Category (Value)")
- Question display with HTML formatting
- Buzz-in button with timer display (Buzzer mode)
- Pass button for skipping turn
- Answer input field with Material Design styling
- Submit answer button
- Multiple timer displays:
  - Buzz-in timer: Time remaining to buzz in
  - Answer timer: Time remaining to submit answer
  - Show answer timer: Time remaining before clue closes
- Hint section (when revealed):
  - Hint text
  - Answer blanks representation
- Answer section (when revealed):
  - Correct answer
  - Explanation of why the answer is correct
  - Detailed facts about the answer
- History log showing player actions:
  - Who buzzed in and when
  - Who answered correctly/incorrectly
  - Pass actions
- Gamemaster controls (in Gamemaster mode):
  - Show/Hide Hint button
  - Show/Hide Answer button
  - Regenerate Question button
  - Team selection dropdown
  - Assign Points button
- Skip button to advance past the answer display
- Visual feedback for correct/incorrect answers
- Responsive layout that adapts to different screen sizes
- TTS controls:
  - Play/pause button for audio reading
  - Volume control slider
  - Voice selection option (if multiple voices available)
  - Visual indicator showing TTS is active

### Cast Room UI
- Optimized for display on large screen TVs and projectors
- Larger font sizes and UI elements for visibility from a distance
- High contrast colors for better readability in various lighting conditions
- Simplified layout that focuses on essential game elements
- Properly scaled content that fits entirely on screen without scrolling
- Clear visual indicators for current game state
- Prominent display of questions, answers, and player scores
- Animations and transitions that are visible and engaging from a distance
- Optimized for 16:9 aspect ratio common in modern TVs
- Support for different resolution displays while maintaining readability

### Winner's Screen UI
- Game over announcement with large typography
- Winner announcement with trophy emoji
- Tie announcement (if applicable)
- Leaderboard card showing:
  - Rank column with badges
  - Player column with names and avatars
  - Score column with final scores
  - Visual highlighting of winners
  - Visual highlighting of current user
- Game statistics card showing:
  - Highest score achieved
  - Number of players/teams
  - Result type (single winner or tie)
- Responsive grid layout that adapts to screen size
- Material Design styling with appropriate color variables:
  - Winner row with primary color highlighting
  - Rank badges with appropriate styling
  - Material card components with proper elevation
- Consistent typography using material design variables
- Appropriate spacing and alignment
- Animations for visual interest

### Chat Room UI
- Collapsible/expandable chat panel that doesn't interfere with gameplay
- Real-time message display with appropriate timestamps
- User identification with names and optional avatars
- Message input field with send button
- Support for basic text formatting (bold, italic, etc.)
- Emoji support for expressive communication
- Notification indicator for new messages
- Unread message counter when chat is collapsed
- Auto-scrolling to newest messages with manual scroll override
- Message history retention throughout the game session
- Visual differentiation between system messages and user messages
- Ability to mention/tag other players
- Mobile-friendly design with appropriate keyboard handling
- Appropriate character limits to prevent spam
- Responsive layout that adapts to available screen space
- Option to minimize or hide chat during intense gameplay moments

## Technical Requirements

### Firebase Integration
- Firestore for data storage
- Firebase Functions for server-side logic
- Real-time updates for all players

### AI Integration
- Support for multiple LLM providers:
  - OpenAI
  - Gemini
  - DeepSeekAI
- Question generation with category support
- Answer evaluation
- Minimize expensive LLM calls:
  - Batch question generation when possible
  - Cache generated content appropriately
  - Optimize prompts for efficiency
  - Use smaller/cheaper models for less complex tasks
  - Implement fallback mechanisms for API failures

### Question Generation and Quality
- Questions must be factually accurate and educational
- Each category should have a coherent theme with related questions
- Questions should vary in difficulty within each category (easier at lower values, harder at higher values)
- Questions should include:
  - Clear, concise wording in Jeopardy style (answer phrased as a question)
  - HTML formatting for emphasis and structure
  - Appropriate difficulty level corresponding to point value
  - Educational content that teaches players something new
- Hints should provide meaningful clues without giving away the answer
- Answer evaluation should be flexible enough to accept minor variations (spelling, synonyms, etc.)
- Detailed facts about answers should provide interesting, educational context
- Questions should be appropriate for the target audience (no offensive content)
- Questions should be diverse in topics and formats
- Questions should be regenerable if the host finds them unsuitable
- The system should filter out low-quality questions during generation

### Authentication
- User authentication for player identification
- Multiple authentication methods:
  - Google sign-in integration
  - Anonymous sign-in for quick access
- Persistent authentication state across sessions
- User profile information display (name, photo)
- Logout functionality
- Automatic redirection to login page for unauthenticated users
- Protected routes requiring authentication
- Host privileges for room creators

### Google Cast Integration
- Integration with Google Cast API for Chromecast support
- Cast button that appears only when compatible devices are detected
- Custom receiver application for optimal TV display
- Session state management for connecting, disconnecting, and reconnecting
- URL-based content casting to receiver application
- Responsive design optimized for TV display
- Automatic content updates when game state changes

### Real-time Communication
- Updates synchronized across all players
- Game state changes reflected immediately

## Admin Features

- Configure game settings in the waiting room:
  - Sliders for timer durations
  - Game mode selection
  - LLM agent selection
- Non-admin users see read-only versions of settings
- Host can end the game at any time
- In Gamemaster mode, host has additional controls:
  - Show/hide hints
  - Show/hide answers
  - Assign points
  - Regenerate questions

## Accessibility and UX

- Clear visual feedback for user actions
- Timer indicators for time-sensitive actions
- Progress indicators during loading states
- Responsive design for all device sizes
- Minimal yet engaging and fun user experience

### User Settings
- Theme selection:
  - Light mode / Dark mode toggle
  - Persistent theme preference across sessions
  - Dynamic application of theme changes without page reload
- Sound settings:
  - Enable/disable Text-to-Speech functionality
  - Voice selection from available system voices
  - Preference persistence across sessions
- Settings UI:
  - Accessible settings panel
  - Toggle switches for binary options
  - Dropdowns for selection from multiple options
  - Immediate application of setting changes
  - Local storage of user preferences

### Text-to-Speech (TTS) Integration
- Automatic reading of questions when they are revealed
- Support for multiple voices and speech rates
- Proper handling of special terms, abbreviations, and complex words
- Pause/resume functionality for TTS playback
- Intelligent parsing of HTML-formatted text for natural speech
- Synchronization between visual display and audio reading
- Fallback mechanisms for devices without TTS support
- Volume control independent of system volume
- Support for different languages and accents where available
- Optimization for Jeopardy-style question reading (dramatic and clear)
