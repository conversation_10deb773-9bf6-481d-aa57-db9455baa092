// postbuild.js
const postcss = require('postcss');
const fs = require('fs').promises; // Use promises version for async/await
const {globby} = require('globby');
const path = require('path');
const defaultConfig = require('./postcss.config.js'); // Import default config

const distDir = './dist/jeopardy-gpt/'; // Replace <your-project-name> with your project's name

async function processCSS() {
  const cssFiles = await globby(`${distDir}/**/*.css`);

  for (const file of cssFiles) {
    console.log(`Processing: ${file}`);
    const css = await fs.readFile(file);

    // Get config for each file
    const config = { ...defaultConfig };
    try {
      const customConfigPath = path.join(
        path.dirname(file),
        'postcss.config.js'
      );
      const customConfig = require(customConfigPath);
      Object.assign(config.plugins, customConfig.plugins);
      console.log(
        `Custom config found and applied for: ${file} at ${customConfigPath}`
      );
    } catch (error) {
      // No custom config found. Ignore and use default
      console.log(`No custom config found for ${file}. Using default.`);
    }

    try {
      const result = await postcss(config.plugins).process(css, {
        from: file,
        to: file,
      });
      await fs.writeFile(file, result.css);

      if (result.map) {
        await fs.writeFile(`${file}.map`, result.map.toString());
      }
    } catch (error) {
      console.error(`Error processing ${file}:`, error);
    }
  }
}

processCSS();
