{"name": "jeopardy-gpt", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "postbuild": "node postbuild.js"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/cdk": "^19.0.3", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/fire": "^19.0.0-rc.0", "@angular/forms": "^19.0.0", "@angular/material": "^19.0.3", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@capacitor-community/text-to-speech": "^5.1.0", "@capacitor-firebase/authentication": "^6.3.1", "@capacitor-firebase/firestore": "^6.3.1", "@capacitor-firebase/functions": "^6.3.1", "@capacitor/android": "^6.2.0", "@capacitor/angular": "^2.0.3", "@capacitor/app": "^6.0.2", "@capacitor/core": "latest", "@capacitor/ios": "^6.2.0", "@capacitor/keyboard": "^6.0.3", "@firebase/app": "^0.10.16", "@firebase/database": "^1.0.10", "@firebase/functions": "^0.11.10", "firebase-admin": "^13.0.2", "lint": "^0.8.19", "moment": "^2.30.1", "plyr": "^3.7.8", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.4", "@angular/cli": "^19.0.4", "@angular/compiler-cli": "^19.0.0", "@capacitor/cli": "latest", "@csstools/postcss-color-mix-function": "^3.0.7", "@csstools/postcss-logical-viewport-units": "^3.0.3", "@types/chrome": "^0.0.293", "@types/chromecast-caf-sender": "^1.0.11", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.20", "globby": "^14.0.2", "jasmine-core": "~5.4.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postbuild": "^2.1.0", "postcss": "^8.4.49", "postcss-loader": "^8.1.1", "postcss-preset-env": "^10.1.3", "sass": "^1.83.0", "source-map-explorer": "^2.5.3", "typescript": "~5.6.2"}}