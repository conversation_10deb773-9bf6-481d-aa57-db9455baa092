<mat-card class="category-wizard-card">
  <mat-card-title class="category-wizard-title">Category Wizard</mat-card-title>
  <mat-card-content class="category-wizard-container primary-container">
    <mat-accordion class="category-accordian">
      <mat-expansion-panel *ngFor="let area of broadAreas | keyvalue">
        <mat-expansion-panel-header>
          <mat-panel-title class="category-panel-title">
            <div>
              {{ area.key }}
            </div>
            <button
              mat-flat-button
              (click)="addCategory(area.key, $event)"
              class="add-button"
            >
              + Add
            </button>
          </mat-panel-title>
        </mat-expansion-panel-header>

        <button
          mat-flat-button
          class="add-button"
          *ngFor="let subcategory of area.value | keyvalue"
          (click)="addCategory(subcategory.key, $event)"
        >
          + {{ subcategory.key }}
        </button>
      </mat-expansion-panel>
    </mat-accordion>

    <div class="selected-categories">
      <app-dynamic-list-input
        #dynamicList
        [listLabel]="'Category'"
        [maxLength]="2"
        [autoCompleteList]="allBroadAreas"
      ></app-dynamic-list-input>
      <div class="generate-buttons">
        <button
          mat-raised-button
          (click)="resetCategories()"
          class="generate-subcategories-button"
        >
          Random
        </button>
        <div style="flex-grow:1">
          <button
            mat-raised-button
            color="primary"
            (click)="generateSubCategories()"
            [disabled]="isLoading"
            class="generate-subcategories-button"
            style="width:100%"
          >
            <div style="width:100%">
              {{ isLoading ? "Generating..." : "Generate Subcategories" }}
            </div>
            <mat-progress-bar
              *ngIf="isLoading"
              mode="indeterminate"
              style="width:100%"
            ></mat-progress-bar>
          </button>
        </div>
      </div>
    </div>

    <div class="generated-subcategories">
      <button
        *ngFor="let cat of generatedSubCategories?.subCategories"
        mat-raised-button
        (click)="addSubCategoryToGame(cat)"
        class="add-button generated-subcategory-button"
      >
        + {{ cat.subCategory }}
      </button>
    </div>
  </mat-card-content>
</mat-card>
