.category-wizard-container {
  display: flex;
  gap: 1%;
  overflow-x: scroll;
  overflow-y: scroll;
}

.category-accordian {
  max-width: 40%;
  min-width: 400px;
}
.selected-categories {
  max-width: 30%;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-grow: 1;
}
.category-panel-title {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.add-button {
  --mdc-filled-button-container-height: auto;
}
.category-wizard-title {
  text-align: center;
  color: var(--mat-sys-on-primary-container);
  padding: 2%;
}
.category-wizard-card {
  background-color: var(--mat-sys-secondary-container);
}
.generated-subcategories {
  flex-grow: 1;
  align-items: center;
  display: flex;
  justify-content: center;
  flex-direction: column;
  gap: 3%;
  min-width: 300px;
}
.generated-subcategory-button {
  width: 100%;
}

.generate-subcategories-button {
  display: flex;
  flex-direction: column;
  gap: 1%;
  justify-content: space-evenly;
  align-items: center;
  display: block;
}
.generate-buttons {
  display: flex;
  gap: 1%;
}
.category-accordian {
  max-height: 30vh;
  overflow-y: scroll;
}
