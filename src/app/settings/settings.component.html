<div class="settings-panel">
  <mat-list>
    <mat-list-item>
      <span matListItemTitle>
        Light Mode
        <mat-icon
          class="info-icon"
          matTooltip="Switch between light and dark theme"
          >brightness_medium</mat-icon
        ></span
      >
      <mat-slide-toggle
        matListItemMeta
        name="themeToggle"
        [(ngModel)]="this.isLightMode"
        (change)="toggleLightMode()"
        color="primary"
      >
      </mat-slide-toggle>
    </mat-list-item>
    <mat-list-item>
      <span matListItemTitle>
        Voice
        <mat-icon class="info-icon" matTooltip="Turn text-to-speech on or off"
          >record_voice_over</mat-icon
        ></span
      >
      <mat-slide-toggle
        matListItemMeta
        name="soundToggle"
        [(ngModel)]="isSoundOn"
        (change)="toggleSound()"
        color="primary"
      >
      </mat-slide-toggle>
    </mat-list-item>
    <div *ngIf="isSoundOn" class="voice-selector-container">
      <mat-form-field appearance="outline">
        <mat-label matTooltip="Choose a voice for text-to-speech"
          >Select Voice</mat-label
        >
        <mat-select
          [(ngModel)]="selectedVoice"
          (selectionChange)="onVoiceSelectionChanged($event)"
        >
          <mat-option
            *ngFor="let v of ttsService.getEnglishVoices()"
            [value]="v.voiceURI"
            >{{ v.name }}</mat-option
          >
        </mat-select>
      </mat-form-field>
    </div>
    <mat-list-item *ngIf="this.authService.getUser()">
      <div class="logout-container">
        <button
          class="logout-button error-button"
          mat-raised-button
          (click)="this.authService.logout()"
          matTooltip="Sign out of your account"
        >
          <mat-icon>logout</mat-icon> Logout
        </button>
      </div>
    </mat-list-item>
  </mat-list>
</div>
