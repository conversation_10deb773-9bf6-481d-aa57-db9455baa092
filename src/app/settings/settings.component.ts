import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatListModule } from '@angular/material/list';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { SettingsService } from '../services/settings.service';
import { AuthService } from '../services/auth.service';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectChange, MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TtsService } from '../services/tts.service';

@Component({
  selector: 'app-settings',
  imports: [
    MatSlideToggleModule,
    CommonModule,
    FormsModule,
    MatListModule,
    MatButtonModule,
    MatSelectModule,
    MatIconModule,
    MatInputModule,
    MatTooltipModule
  ],
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.css'
})
export class SettingsComponent implements OnInit {
  isSoundOn: boolean = true;
  isLightMode: boolean = false;
  settingsService = inject(SettingsService);
  authService = inject(AuthService);
  ttsService = inject(TtsService);
  selectedVoice = '';
  ngOnInit(): void {
    this.settingsService.settings$.subscribe(settings => {
      this.isSoundOn = settings.soundEnabled;
      this.isLightMode = settings.theme === 'light';
      this.ttsService.setVoiceUri(settings.voiceUri || '');
      this.selectedVoice = this.ttsService.getVoiceUri();
    });
  }
  onVoiceSelectionChanged(event: MatSelectChange) {
    console.log('setting: ', this.selectedVoice);
    this.ttsService.setVoiceUri(this.selectedVoice);
    this.settingsService.updateSetting('voiceUri', this.selectedVoice);
  }
  toggleSound() {
    this.settingsService.updateSetting('soundEnabled', this.isSoundOn);
  }
  toggleLightMode() {
    this.settingsService.updateSetting(
      'theme',
      this.isLightMode ? 'light' : 'dark'
    );
  }
}
