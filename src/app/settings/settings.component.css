:host {
  display: block;
  min-width: 300px;
  width: auto;
  padding: 0.75rem;
  border-radius: 1rem;
  overflow: visible;
}

.settings-panel {
  background-color: var(--mat-sys-secondary-container);
  border-radius: 0.75rem;
  overflow: hidden;
}

mat-list {
  padding: 0;
}

mat-list-item {
  height: auto !important;
  margin-bottom: 0.5rem;
  padding: 0.5rem 0;
  width: 100%;
  box-sizing: border-box;
}

/* Fix for the voice dropdown - scoped to settings component */
:host ::ng-deep .mat-mdc-form-field {
  width: 100%;
  box-sizing: border-box;
}

:host ::ng-deep .mat-mdc-text-field-wrapper {
  width: 100%;
  box-sizing: border-box;
}

mat-list-item:last-child {
  margin-bottom: 0;
}

.logout-container {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 0.25rem 0;
}

.logout-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: 0.25rem; /* Reduced margin */
  transition: all 0.3s ease;
}

.error-button {
  background-color: var(--mat-sys-error) !important;
  color: var(--mat-sys-on-error) !important;
}

.logout-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px
    color-mix(in srgb, var(--mat-sys-shadow) 20%, transparent);
}

.voice-selector-container {
  width: 100%;
  padding: 0.25rem 1rem;
  box-sizing: border-box;
  margin-bottom: 0.25rem;
}

:host mat-form-field {
  width: 100%;
  margin-top: 0.25rem;
  box-sizing: border-box;
}

/* Make sure text is readable in both light and dark modes */
span[matListItemTitle] {
  color: var(--mat-sys-on-secondary-container);
  font-weight: 500;
  font-size: 1rem;
  padding-left: 0.5rem;
}
