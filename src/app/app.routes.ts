import { Routes } from '@angular/router';
import { LobbyComponent } from './lobby/lobby.component';
import { RoomComponent } from './room/room.component';
import { LoginComponent } from './login/login.component';
import { TestComponent } from './test/test.component';

export const routes: Routes = [
  { path: '', redirectTo: '/lobby', pathMatch: 'full' },
  { path: 'lobby', component: LobbyComponent },
  { path: 'lobby/:roomId', component: LobbyComponent },
  { path: 'room/:roomId', component: RoomComponent },
  {
    path: 'castRoom/:roomId',
    component: RoomComponent,
    data: { isCast: true }
  },
  { path: 'login', component: LoginComponent },
  { path: 'test', component: TestComponent }
];
