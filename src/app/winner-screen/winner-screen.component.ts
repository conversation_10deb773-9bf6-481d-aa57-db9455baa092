import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import {
  MemorableQuestion,
  Room,
  RoomState,
  RoomSummary
} from '../../../functions/src/resources';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { GameEndService } from '../services/game-end.service';
import {
  trigger,
  transition,
  style,
  animate,
  stagger,
  query,
  keyframes
} from '@angular/animations';

interface PlayerStats {
  id: string;
  name: string;
  photoUrl?: string;
  score: number;
  isWinner: boolean;
  isLoser: boolean;
  isCurrentUser: boolean;
  rank: number;
}

@Component({
  selector: 'app-winner-screen',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatTooltipModule,
    MatProgressBarModule,
    MatProgressSpinnerModule
  ],
  host: { '[class.is-cast]': 'isCast' },
  templateUrl: './winner-screen.component.html',
  styleUrl: './winner-screen.component.css',
  animations: [
    trigger('sectionFade', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(10px)' }),
        animate(
          '600ms ease-in',
          style({ opacity: 1, transform: 'translateY(0)' })
        )
      ]),
      transition(':leave', [
        animate(
          '600ms ease-out',
          style({ opacity: 0, transform: 'translateY(-10px)' })
        )
      ])
    ]),
    trigger('factCycle', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('800ms ease-in', style({ opacity: 1 }))
      ]),
      transition(':leave', [animate('800ms ease-out', style({ opacity: 0 }))])
    ]),
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('0.5s ease-out', style({ opacity: 1 }))
      ])
    ]),
    trigger('bounceIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'scale(0.5)' }),
        animate(
          '0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
          style({ opacity: 1, transform: 'scale(1)' })
        )
      ])
    ]),
    trigger('slideInStagger', [
      transition('* => *', [
        query(
          ':enter',
          [
            style({ opacity: 0, transform: 'translateY(20px)' }),
            stagger('100ms', [
              animate(
                '0.4s ease-out',
                style({ opacity: 1, transform: 'translateY(0)' })
              )
            ])
          ],
          { optional: true }
        )
      ])
    ]),
    trigger('pulse', [
      transition('* => *', [
        animate(
          '1s ease-in-out',
          keyframes([
            style({ transform: 'scale(1)', offset: 0 }),
            style({ transform: 'scale(1.05)', offset: 0.5 }),
            style({ transform: 'scale(1)', offset: 1 })
          ])
        )
      ])
    ]),
    trigger('shakeIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX(-20px)' }),
        animate(
          '0.6s cubic-bezier(0.36, 0.07, 0.19, 0.97)',
          keyframes([
            style({
              opacity: 0.5,
              transform: 'translateX(-10px)',
              offset: 0.2
            }),
            style({ opacity: 0.7, transform: 'translateX(8px)', offset: 0.4 }),
            style({ opacity: 0.8, transform: 'translateX(-6px)', offset: 0.6 }),
            style({ opacity: 0.9, transform: 'translateX(4px)', offset: 0.8 }),
            style({ opacity: 1, transform: 'translateX(0)', offset: 1 })
          ])
        )
      ])
    ])
  ]
})
export class WinnerScreenComponent implements OnInit, OnDestroy {
  @Input() room?: Room;
  @Input() isCast: boolean = false;

  playerStats: PlayerStats[] = [];
  winnerIds: string[] = [];
  highestScore: number = 0;
  lowestScore: number = 0;
  scoreDifference: number = 0;
  winnerComment: string = '';
  loserComment: string = '';
  funFacts: string[] = [];
  showConfetti: boolean = true;
  hasNegativeScorer: boolean = false;
  isSoloPlayer: boolean = false;
  soloPlayerComment: string = '';
  negativeScoreComment: string = '';

  // Store the original game state from RoomSummary
  originalRoomState?: RoomState;

  // Store memorable questions from the game
  memorableQuestions: MemorableQuestion[] = [];

  // Track if the current user is the host (for showing the return button)
  isHost = false; // TypeScript will infer this as boolean

  // Auto-cycling properties for individual components
  currentQuestionIndex = 0;
  currentFactIndex = 0;
  questionCycleInterval = 8000; // 8 seconds per question
  factCycleInterval = 6000; // 6 seconds per fact
  questionProgressValue = 0;
  factProgressValue = 0;
  private questionCycleTimer: any;
  private factCycleTimer: any;
  private questionProgressTimer: any;
  private factProgressTimer: any;

  constructor(
    private router: Router,
    private authService: AuthService,
    private gameEndService: GameEndService
  ) {}

  ngOnInit(): void {
    // Extract the original game state and memorable questions if available
    if (this.room && this.room.gameState.gameProgress.type === 'RoomSummary') {
      const roomSummary = this.room.gameState.gameProgress as RoomSummary;
      this.originalRoomState = roomSummary.originalState;

      // Get the server-provided memorable questions
      if (
        roomSummary.memorableQuestions &&
        roomSummary.memorableQuestions.length > 0
      ) {
        this.memorableQuestions = roomSummary.memorableQuestions;
        console.log(
          'Server-provided memorable questions:',
          this.memorableQuestions
        );
      }

      // Log the original state to verify it's available
      console.log('Original game state preserved:', this.originalRoomState);

      // Check if the current user is the host
      const currentUser = this.authService.getUser();
      if (currentUser && this.room.host) {
        this.isHost = currentUser.uid === this.room.host;
      } else {
        this.isHost = false;
      }
    }

    this.calculateStats();

    // Start the auto-cycling for components
    this.startComponentCycling();
  }

  ngOnDestroy(): void {
    // Clean up timers when component is destroyed
    this.stopComponentCycling();
  }

  /**
   * Start the auto-cycling of individual components
   */
  startComponentCycling(): void {
    // Clear any existing timers
    this.stopComponentCycling();

    // Start cycling questions if there are any
    if (this.memorableQuestions.length > 0) {
      // Start the question cycling timer
      this.questionCycleTimer = setInterval(() => {
        // Create a temporary variable to hold the current index
        const tempIndex = this.currentQuestionIndex;

        // First set the progress to 0 to indicate a new item is coming
        this.questionProgressValue = 0;

        // Use a timeout to allow the animation to complete
        setTimeout(() => {
          // Update to the next index
          this.currentQuestionIndex =
            (tempIndex + 1) % this.memorableQuestions.length;
        }, 800); // Match the animation duration
      }, this.questionCycleInterval);

      // Start the question progress bar timer
      const questionProgressStep = 100 / (this.questionCycleInterval / 100); // Update every 100ms
      this.questionProgressTimer = setInterval(() => {
        this.questionProgressValue += questionProgressStep;
        if (this.questionProgressValue >= 100) {
          this.questionProgressValue = 0;
        }
      }, 100);
    }

    // Start cycling fun facts if there are any
    if (this.funFacts.length > 0) {
      // Start the fact cycling timer
      this.factCycleTimer = setInterval(() => {
        // Create a temporary variable to hold the current index
        const tempIndex = this.currentFactIndex;

        // First set the progress to 0 to indicate a new item is coming
        this.factProgressValue = 0;

        // Use a timeout to allow the animation to complete
        setTimeout(() => {
          // Update to the next index
          this.currentFactIndex = (tempIndex + 1) % this.funFacts.length;
        }, 800); // Match the animation duration
      }, this.factCycleInterval);

      // Start the fact progress bar timer
      const factProgressStep = 100 / (this.factCycleInterval / 100); // Update every 100ms
      this.factProgressTimer = setInterval(() => {
        this.factProgressValue += factProgressStep;
        if (this.factProgressValue >= 100) {
          this.factProgressValue = 0;
        }
      }, 100);
    }
  }

  /**
   * Stop all component cycling timers
   */
  stopComponentCycling(): void {
    // Clear question timers
    if (this.questionCycleTimer) {
      clearInterval(this.questionCycleTimer);
      this.questionCycleTimer = null;
    }

    if (this.questionProgressTimer) {
      clearInterval(this.questionProgressTimer);
      this.questionProgressTimer = null;
    }

    // Clear fact timers
    if (this.factCycleTimer) {
      clearInterval(this.factCycleTimer);
      this.factCycleTimer = null;
    }

    if (this.factProgressTimer) {
      clearInterval(this.factProgressTimer);
      this.factProgressTimer = null;
    }
  }

  /**
   * Go to the next question manually
   */
  nextQuestion(): void {
    if (this.memorableQuestions.length > 0) {
      // Reset the timers
      if (this.questionCycleTimer) {
        clearInterval(this.questionCycleTimer);
      }
      if (this.questionProgressTimer) {
        clearInterval(this.questionProgressTimer);
      }

      // Immediately update to the next index
      this.currentQuestionIndex =
        (this.currentQuestionIndex + 1) % this.memorableQuestions.length;

      // Reset progress value
      this.questionProgressValue = 0;

      // Restart the cycling
      this.startComponentCycling();
    }
  }

  /**
   * Go to the next fact manually
   */
  nextFact(): void {
    if (this.funFacts.length > 0) {
      // Reset the timers
      if (this.factCycleTimer) {
        clearInterval(this.factCycleTimer);
      }
      if (this.factProgressTimer) {
        clearInterval(this.factProgressTimer);
      }

      // Immediately update to the next index
      this.currentFactIndex =
        (this.currentFactIndex + 1) % this.funFacts.length;

      // Reset progress value
      this.factProgressValue = 0;

      // Restart the cycling
      this.startComponentCycling();
    }
  }

  calculateStats(): void {
    if (!this.room) return;

    // Check if there's only one player
    this.isSoloPlayer = this.room.playerIds.length === 1;

    // Find the highest score
    this.highestScore = Math.max(...this.room.playerInfos.map(p => p.score));

    // Find the lowest score
    this.lowestScore = Math.min(...this.room.playerInfos.map(p => p.score));

    // Check if any player has a negative or zero score
    this.hasNegativeScorer = this.room.playerInfos.some(p => p.score <= 0);

    // Calculate score difference
    this.scoreDifference = this.highestScore - this.lowestScore;

    // Find all players with the highest score (could be a tie)
    this.winnerIds = this.room.playerIds.filter(
      (_, index) => this.room!.playerInfos[index].score === this.highestScore
    );

    // Create player stats with rankings
    const sortedPlayers = [...this.room.playerInfos]
      .map((info, index) => ({
        info,
        id: this.room!.playerIds[index]
      }))
      .sort((a, b) => b.info.score - a.info.score);

    // Assign ranks (players with same score get same rank)
    let currentRank = 1;
    let previousScore = -1;

    // Find the lowest rank (last place)
    const lowestRank = sortedPlayers.length;

    this.playerStats = sortedPlayers.map((player, index) => {
      // If this score is different from the previous one, update the rank
      if (player.info.score !== previousScore) {
        currentRank = index + 1;
        previousScore = player.info.score;
      }

      // Determine if this player is in last place
      const isLastPlace = currentRank === lowestRank;

      return {
        id: player.id,
        name: player.info.userName || 'Unknown Player',
        photoUrl: player.info.photoUrl,
        score: player.info.score,
        isWinner: this.winnerIds.includes(player.id),
        isLoser: isLastPlace,
        isCurrentUser: player.id === this.authService.getUser()?.uid,
        rank: currentRank
      };
    });

    // Generate snarky comments
    this.generateComments();

    // Generate fun facts
    this.generateFunFacts();
  }

  generateComments(): void {
    // Solo player comments
    const soloPlayerComments = [
      "Playing alone? That's one way to guarantee you won't lose to anyone else!",
      'Congratulations on beating absolutely nobody! What an achievement!',
      'Solo player? More like "too scared to face real competition"!',
      'You won! And lost. And tied. All at the same time. Impressive!',
      "Playing by yourself? I guess that's one way to avoid social anxiety.",
      "You're the winner! Also the loser. And the middle-placer. Congrats?",
      'Solo mode: When you want to feel smart without the risk of being proven otherwise.',
      'Competing against yourself and still barely winning? Interesting strategy!',
      "You've proven once and for all that you're better than... absolutely no one.",
      "Congratulations! You've successfully beaten your imaginary opponents!",
      'Playing alone is like marking your own homework - suspiciously high scores!',
      'Solo victory! All your imaginary friends are very impressed.',
      "You've won the prestigious competition against absolutely nobody. Frame this achievement!",
      "Winning a solo game is like being the tallest person in a room when you're alone.",
      'Congratulations on your uncontested victory! Literally uncontested.',
      'Solo player? Did all your friends suddenly remember they had other plans?',
      "You've proven you're the best player in this room... because you're the only one.",
      "Playing alone? I guess that's one way to ensure a 100% win rate.",
      'Solo victory! Your competition was non-existent... literally.',
      'Congratulations on beating all zero of your opponents! What a champion!'
    ];

    // Negative score comments
    const negativeScoreComments = [
      'Negative score? That takes a special kind of talent!',
      "Congratulations on achieving a score lower than when you started. That's almost impressive!",
      "Your score is so low it's actually impressive. Like watching someone fall UP the stairs.",
      'Negative points? Were you actively trying to get everything wrong?',
      'Your score suggests you might have been playing an entirely different game.',
      'Achieving a score below zero is like digging a hole when asked to build a mountain.',
      "Negative score? At least you're exceptional at something!",
      'Your score is lower than my expectations, and those were already underground.',
      'Congratulations on finding new depths in the scoring system!',
      "Your score is so low it's actually wrapped around to being impressive.",
      "Negative points? That's like owing knowledge to the universe.",
      'Your score suggests you might have been answering for the other team.',
      "Below zero? You've boldly gone where few players have gone before!",
      "Your score is so low it's making mathematicians question the number line.",
      'Negative score? You must have been playing 4D chess while everyone else was playing trivia.',
      'Your score suggests you have a promising career in being wrong professionally.',
      'Congratulations on breaking the game in a way the developers never anticipated!',
      "Your score is so low it's actually fascinating from a statistical perspective.",
      "Negative points? That's like finishing a marathon behind the starting line.",
      'Your score suggests you might have misunderstood the fundamental concept of trivia.'
    ];

    // Winner comments
    const winnerComments = [
      'Congratulations! Your brain cells actually showed up today!',
      'Winner winner, chicken dinner! Though your opponents played like vegetables.',
      "You won! Either you're really smart or everyone else is... well, let's be polite.",
      'First place! Your useless trivia knowledge finally paid off!',
      'Victory! Now you can finally brag about something at parties!',
      'Congratulations on winning! Your parents might finally be proud.',
      'You won! All those hours of watching Jeopardy instead of having a social life paid off!',
      'First place! Now you can add "trivia champion" to your otherwise empty resume.',
      'Winner! Your opponents never stood a chance... mainly because they were trying.',
      'Victory is yours! Time to celebrate with all your imaginary friends!',
      'You crushed it! Almost as impressively as you crush social interactions.',
      'Champion! Your useless knowledge has finally become... slightly less useless.',
      'You won! Proof that knowing random facts is occasionally rewarding.',
      'First place! Your brain is clearly filled with information no one asked for.',
      'Congratulations! Your trivial knowledge finally triumphed over your trivial existence.',
      'Winner! Your opponents are as disappointed as your high school teachers.',
      "Victory! This is definitely going in your dating profile, isn't it?",
      'You won! Clearly the result of a glitch in the matrix.',
      'First place! Your strategy of "actually knowing things" really paid off.',
      'Champion! Your opponents are questioning their life choices right now.',
      "You won! The only competition you've ever won that didn't involve eating.",
      'Victory! Your brain is apparently good for something after all.',
      'Winner! Now go share this achievement with someone who cares... if you can find them.',
      'First place! All those Wikipedia rabbit holes were worth it!',
      "Congratulations! You've peaked. It's all downhill from here.",
      'You won! Probably because the questions were too easy.',
      'Victory! Your opponents let you win out of pity.',
      "Champion! This is the highlight of your week, isn't it?",
      "You won! Your parents still won't understand what this means.",
      "First place! The only place you'll ever come first."
    ];

    // Loser comments
    const loserComments = [
      'Better luck next time! Maybe try reading a book... or any book, really.',
      'Last place is just first place from the bottom. Think positive!',
      "Don't feel bad, Einstein would've probably lost too... if he was asleep.",
      'You may not have won, but you definitely participated!',
      'Your score suggests you might have been answering questions for a different game.',
      "Last place! At least you're consistent in your mediocrity.",
      'You lost so badly even your phone is embarrassed to be seen with you.',
      'That was rough to watch. Have you considered knitting as a hobby instead?',
      'Last place! But hey, at least you set a record... for lowest score ever.',
      'Your performance was so bad, the app considered giving you negative points.',
      'Congratulations on finding the bottom! Most people stop digging sooner.',
      "You're living proof that knowledge isn't genetic.",
      'Last place! Your teachers called - they want their effort back.',
      'Your score is lower than my expectations, and those were already underground.',
      'If ignorance is bliss, you must be the happiest person here!',
      'Last place! But first in our hearts... just kidding.',
      'Your score suggests you were answering with your eyes closed. Were you?',
      "You've redefined what it means to be clueless. Impressive, really.",
      "Last place! Don't worry, someone had to demonstrate what failure looks like.",
      'Your knowledge is like your love life - nonexistent.',
      'That was painful to watch. Like a car crash, but with fewer correct answers.',
      "Last place! But you're number one in showing us how not to play.",
      'Your brain called. It wants a refund on all those years of education.',
      "If trivia knowledge was money, you'd be deeply in debt.",
      "Last place! But you're winning at being consistently wrong.",
      'Your score is lower than the bar I set for you, and I put that bar on the ground.',
      'You lost so badly even participation trophies are offended.',
      'Last place! But first place in making everyone else feel smart.',
      'Your performance was so bad, Google just suggested "basic facts for toddlers" for you.',
      "If knowledge was water, you'd be a desert."
    ];

    // Tie comments
    const tieComments = [
      'A tie! Apparently mediocrity loves company.',
      "You all tied! It's like you coordinated your wrong answers.",
      "Nobody wins, nobody loses. Just like in real life, it's all meaningless!",
      'A tie! What are the odds? (Actually, pretty high with this group.)',
      'Everyone\'s equally... something. Let\'s go with "special".',
      "A tie! You all equally don't deserve to win.",
      "You tied! Proving that two wrongs don't make a right, but they can make the same score.",
      'A perfect tie! Almost as if none of you knew the answers.',
      "You've all managed to be equally mediocre. Congratulations?",
      'A tie! The statistical probability of you all being this average is astounding.',
      "You tied! It's like watching identical twins compete in a mirror-looking contest.",
      'A perfect demonstration of collective average-ness.',
      "You've all won! And by won, I mean none of you lost more than the others.",
      'A tie! The universe\'s way of saying "meh" to all of you.',
      'You tied! Proving that great minds think alike, and so do yours.',
      'A synchronized display of adequacy from everyone involved.',
      "You've all managed to be equally unimpressive. That's almost impressive.",
      'A tie! Like watching multiple versions of the same person make the same mistakes.',
      'You tied! The trivia equivalent of a participation trophy for everyone.',
      "A perfect demonstration that you're all exactly as average as each other.",
      "You've achieved perfect balance - everyone equally disappointed.",
      "A tie! It's almost as if you all studied from the same wrong textbook.",
      'You tied! The trivia gods have declared you all equally worthy... of mockery.',
      'A statistical anomaly of shared mediocrity.',
      "You've all managed to be precisely as uninformed as each other.",
      'A tie! Like watching multiple computers run the same buggy program.',
      'You tied! Proving that when no one stands out, everyone blends in.',
      'A perfect example of group-think resulting in group-wrong.',
      "You've all achieved the same level of almost-but-not-quite-good-enough.",
      "A tie! The universe's way of saying none of you deserve special recognition."
    ];

    // Select truly random comments from each array
    const winnerIndex = Math.floor(Math.random() * winnerComments.length);
    const loserIndex = Math.floor(Math.random() * loserComments.length);
    const tieIndex = Math.floor(Math.random() * tieComments.length);
    const soloIndex = Math.floor(Math.random() * soloPlayerComments.length);
    const negativeIndex = Math.floor(
      Math.random() * negativeScoreComments.length
    );

    // Handle solo player case
    if (this.isSoloPlayer) {
      this.winnerComment = soloPlayerComments[soloIndex];
      this.soloPlayerComment = soloPlayerComments[soloIndex];
      this.loserComment = '';
      return;
    }

    // Handle tie case
    if (this.winnerIds.length > 1) {
      this.winnerComment = tieComments[tieIndex];
      this.loserComment = '';
    } else {
      this.winnerComment = winnerComments[winnerIndex];
      this.loserComment = loserComments[loserIndex];
    }

    // Handle negative score case
    if (this.hasNegativeScorer) {
      this.negativeScoreComment = negativeScoreComments[negativeIndex];
    }
  }

  generateFunFacts(): void {
    if (!this.room) return;

    const facts = [];

    // Add facts from the original game state if available
    if (this.originalRoomState) {
      // Count total number of clues answered
      let totalClues = 0;
      let completedClues = 0;
      let correctAnswers = 0;
      let incorrectAnswers = 0;

      // Loop through all rounds, categories, and clues to gather stats
      this.originalRoomState.roundStates.forEach(roundState => {
        roundState.categoryStates.forEach(categoryState => {
          categoryState.clueStates.forEach(clueState => {
            totalClues++;

            if (clueState.clueComplete) {
              completedClues++;

              // Count correct and incorrect answers from the structured log if available
              if (clueState.structuredClueLog?.entries) {
                const correctEntries = clueState.structuredClueLog.entries.filter(
                  entry => entry.type === 'ANSWER_CORRECT'
                );
                const incorrectEntries = clueState.structuredClueLog.entries.filter(
                  entry => entry.type === 'ANSWER_INCORRECT'
                );

                correctAnswers += correctEntries.length;
                incorrectAnswers += incorrectEntries.length;
              }
            }
          });
        });
      });

      // Add facts based on the game stats
      if (completedClues > 0) {
        const completionPercentage = Math.round(
          (completedClues / totalClues) * 100
        );
        facts.push(
          `This game covered ${completedClues} out of ${totalClues} possible clues (${completionPercentage}%). ${
            completionPercentage === 100
              ? "You're all either very thorough or had way too much time on your hands."
              : 'Apparently some questions were just too scary to attempt.'
          }`
        );
      }

      if (correctAnswers > 0 || incorrectAnswers > 0) {
        const totalAttempts = correctAnswers + incorrectAnswers;
        const correctPercentage =
          totalAttempts > 0
            ? Math.round((correctAnswers / totalAttempts) * 100)
            : 0;

        if (correctPercentage > 80) {
          facts.push(
            `Players answered correctly ${correctPercentage}% of the time. Either the questions were too easy or you're all suspiciously knowledgeable.`
          );
        } else if (correctPercentage < 30) {
          facts.push(
            `Only ${correctPercentage}% of answers were correct. I've seen random guessing with better results.`
          );
        } else {
          facts.push(
            `Players got ${correctPercentage}% of their answers right. Perfectly mediocre, as expected.`
          );
        }
      }
    }

    // Score difference fact - only if it's positive
    if (this.scoreDifference > 1000) {
      facts.push(
        `The winner outscored the competition by a whopping ${this.scoreDifference} points. Someone's been studying their encyclopedia collection!`
      );
    } else if (this.scoreDifference < 100 && this.scoreDifference > 0) {
      facts.push(
        `Only ${this.scoreDifference} points separated first and last place. That's what we call a nail-biter! Or what we would call it if anyone actually bit their nails over trivia.`
      );
    }

    // Number of players fact - more positive phrasing
    const playerCount = this.playerStats.length;
    if (this.isSoloPlayer) {
      facts.push(
        `This game was played in solo mode - the trivia equivalent of talking to yourself in the mirror.`
      );
      facts.push(
        `In solo mode, you're guaranteed to be in the top 100% of players!`
      );
    } else if (playerCount > 3) {
      facts.push(
        `${playerCount} players competed in this trivia showdown${
          this.winnerIds.length > 1
            ? ' and somehow managed to tie for first place. What are the odds?'
            : ' and one emerged victorious while the others are Googling the answers they missed.'
        }`
      );
    } else if (playerCount === 2) {
      facts.push(
        `This was a head-to-head trivia duel${
          this.winnerIds.length > 1
            ? ' that ended in a tie. Perfectly balanced, as all things should be.'
            : ' where only one could claim victory. The other is probably demanding a rematch.'
        }`
      );
    }

    // Add a fact about the winner's score if it's impressive
    if (this.highestScore > 5000) {
      facts.push(
        `Wow! The top score of ${this.highestScore} points could buy a lot of useless trivia books! Or, you know, just be a number on a screen.`
      );
    }

    // Add a fact about negative scores if applicable
    if (this.hasNegativeScorer) {
      facts.push(
        `This game featured a player who achieved the rare feat of a negative score. That takes real commitment!`
      );
      facts.push(
        `When you answer incorrectly in this game, you lose points. Some players took this as a personal challenge.`
      );
    }

    // Random fun facts - witty and interesting
    const randomFacts = [
      "If knowledge is power, someone just got a serious upgrade! Too bad it doesn't help with your electricity bill.",
      'Studies show that Jeopardy winners are 73% more likely to bring up random facts at parties. And 100% more likely to be avoided at them.',
      'The average Jeopardy contestant can name all 50 states but still forgets where they put their keys. Priorities!',
      'Fun fact: The word "trivia" comes from Latin meaning "three roads" - perfect for when your knowledge takes you down strange paths that nobody asked about!',
      'If you stacked all the points from this game, they would reach approximately nowhere because points are abstract concepts. Much like your social life after becoming a trivia master.',
      "The first Jeopardy game show aired in 1964. This game is slightly more modern, but your jokes probably aren't.",
      'The human brain can store approximately 2.5 petabytes of information. You just used about 3 kilobytes of that. Efficient!',
      'Congratulations on completing this game! Your brain cells thank you for the exercise. Your friends thank you for finally shutting up about trivia facts.',
      "Did you know? The more trivia games you play, the more likely you are to play another trivia game! It's like potato chips for nerds.",
      'The word "quiz" allegedly originated when a Dublin theater owner bet he could introduce a nonsense word into the language within 24 hours. The word "selfie" took considerably less effort.',
      "The highest-scoring Jeopardy contestant of all time won over $2.5 million. You... well, you participated, and that's what counts!",
      'In ancient Rome, trivia contests were held in bathhouses. Thankfully, we have evolved to playing in our pajamas on the couch. Progress!',
      'The average human attention span is now shorter than a goldfish. Congratulations on making it through this entire game! Wait, what were we talking about?',
      "Studies show that playing trivia games may help prevent cognitive decline. So technically, this was healthcare. You're welcome!",
      'The first known trivia contest was held in 1965. Before that, people just called it "knowing stuff nobody cares about."',
      "The longest-running trivia contest is at Lawrence University, lasting 50 continuous hours. This game was mercifully shorter, wasn't it?",
      'Trivia fact: 73% of all trivia facts are made up on the spot. Including this one. And possibly that percentage.',
      'The part of your brain that stores trivia is also responsible for remembering where you put your keys. Guess which one it prioritizes?',
      "Statistically, someone who is good at trivia is more likely to remember your birthday but forget to buy you a present. It's the thought that counts?",
      'Trivia competitions activate the same brain regions as eating chocolate. But with fewer calories and less joy!',
      'The Guinness World Record for most trivia facts memorized is 91,000. And yet, they still forgot their password.',
      'Playing trivia games increases blood flow to the brain by up to 25%. Your brain just ran a marathon while the rest of you sat there!',
      'The most common trivia category worldwide is entertainment, followed by sports. Knowledge of tax codes ranks last, surprisingly.',
      'People who excel at trivia are 43% more likely to correct grammar at parties. And 100% more likely to be uninvited from the next one.',
      'The term "pub quiz" originated in the UK in the 1970s as a way to attract customers on slow nights. Turns out, people will show up for anything if there\'s beer!',
      'The largest trivia game ever played had over 10,000 participants. This game had fewer, but with 100% more personality.',
      'Trivia enthusiasts have larger hippocampi - the brain region associated with memory. Also associated with being called a "know-it-all" at family gatherings.',
      'The most obscure trivia fact ever recorded was so obscure that everyone forgot it immediately. Kind of like this one will be.',
      'Studies show that people who enjoy trivia are more likely to read the backs of cereal boxes for entertainment. It\'s not weird, it\'s "research."',
      'The oldest trivia question is "What has four legs in the morning, two at noon, and three in the evening?" The answer is still "human," not "shape-shifting alien."'
    ];

    // Add 2-3 random facts
    const numRandomFacts = Math.floor(Math.random() * 2) + 2;
    for (let i = 0; i < numRandomFacts; i++) {
      const randomIndex = Math.floor(Math.random() * randomFacts.length);
      facts.push(randomFacts[randomIndex]);
      // Remove the used fact to avoid duplicates
      randomFacts.splice(randomIndex, 1);
      if (randomFacts.length === 0) break;
    }

    this.funFacts = facts;
  }

  getWinnerName(): string {
    if (this.winnerIds.length === 0 || !this.room) return 'No winner';

    const winnerId = this.winnerIds[0];
    const winnerIndex = this.room.playerIds.findIndex(id => id === winnerId);

    if (winnerIndex === -1) return 'Unknown winner';

    return this.room.playerInfos[winnerIndex].userName || 'Unknown Player';
  }

  getNegativeScorers(): string {
    if (!this.room) return '';

    const negativeScorers = this.room.playerInfos
      .filter(player => player.score <= 0)
      .map(player => player.userName || 'Unknown Player');

    if (negativeScorers.length === 0) return '';
    if (negativeScorers.length === 1) return negativeScorers[0];

    return negativeScorers.join(', ');
  }

  returnToLobby(): void {
    this.router.navigate(['/lobby']);
  }

  /**
   * Return to the game board from the winner screen
   * Only available to the host
   */
  async returnToGameBoard(): Promise<void> {
    if (!this.room) return;

    try {
      await this.gameEndService.revertToGameBoard(this.room.roomId);
    } catch (error) {
      console.error('Error returning to game board:', error);
    }
  }

  // Play Again functionality removed as multiple games in the same room are not supported
}
