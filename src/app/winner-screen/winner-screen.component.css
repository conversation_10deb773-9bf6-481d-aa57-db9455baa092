.winner-screen-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 0.5rem;
  height: 100%;
  gap: 0.5rem;
  background-color: var(--mat-sys-surface);
  color: var(--mat-sys-on-surface);
  font: var(--mat-sys-body-large);
  margin: 0 auto; /* Center the container */
}

.winner-header {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
  text-align: center;
  width: 100%;
}

.winner-header .is-cast {
  flex-direction: row;
}

.winner-title {
  font: var(--mat-sys-headline-large);
  color: var(--mat-sys-on-surface);
  margin-bottom: 1rem;
}

.winner-announcement {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--mat-sys-primary-container);
  color: var(--mat-sys-on-primary-container);
  padding: 1rem 2rem;
  border-radius: 1rem;
  box-shadow: var(--mat-sys-level2);
  position: relative;
  overflow: hidden;
}

.winner-comment,
.negative-comment {
  margin-top: 1rem;
  font: var(--mat-sys-title-medium);
  font-style: italic;
  text-align: center;
  color: var(--mat-sys-on-primary-container);
  opacity: 0.9;
}

.negative-score-announcement {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--mat-sys-error-container);
  color: var(--mat-sys-on-error-container);
  padding: 1rem 2rem;
  border-radius: 1rem;
  box-shadow: var(--mat-sys-level2);
  position: relative;
  overflow: hidden;
  margin-top: 1rem;
  max-width: 80%;
}

.negative-score-announcement h3 {
  font: var(--mat-sys-title-medium);
  margin: 0 0 0.5rem 0;
  text-align: center;
  color: var(--mat-sys-on-error-container);
}

.negative-comment {
  color: var(--mat-sys-on-error-container);
}

.negative-emoji {
  font-size: 2.5rem;
  margin-top: 0.5rem;
}

.loser-comment {
  grid-column: 1 / -1;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  margin: 0.5rem 1rem;
  font: var(--mat-sys-body-medium);
  font-style: italic;
  color: var(--mat-sys-on-error-container);
  background-color: rgba(var(--mat-sys-on-error-container-rgb), 0.1);
  border-radius: 0.5rem;
  animation: fadeIn 0.5s ease-out;
  overflow: visible;
}

.loser-icon {
  color: var(--mat-sys-error);
  min-width: 24px;
  min-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.winner-announcement h2 {
  font: var(--mat-sys-headline-medium);
  margin: 0;
}

.winner-emoji {
  font-size: 3rem;
  margin-top: 0.5rem;
}

/* Content container with grid layout */
.content-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md, 1rem); /* Reduced gap using variable */
  width: 100%;
  max-width: 100%;
  margin-bottom: var(--spacing-md, 1rem); /* Reduced margin using variable */
  height: 100%;
  align-items: center;
}

/* Medium screens - 2 columns layout */
@media (min-width: 900px) {
  .content-container {
    grid-template-columns: repeat(2, 1fr);
    grid-template-areas: "leaderboard questions";
  }

  .leaderboard-card {
    grid-area: leaderboard;
  }

  .interesting-questions-card {
    grid-area: questions;
  }

  /* Fun Facts card removed - now displayed as a quote above */
}

/* Card header with controls */
.card-controls {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 0.75rem; /* Add space between spinner and button */
}

/* Fun Facts Quote styling - responsive with min-height */
.fun-facts-quote {
  width: 100%;
  min-height: 4.5rem; /* Minimum height for consistency */
  /* margin: 0 auto var(--spacing-md, 1rem) auto; */
  display: flex;
  justify-content: center; /* Center the quote container */
}

.card-title {
  display: flex;
  align-items: center;
  height: 100%;
}
.quote-content {
  position: relative;
  background-color: var(--mat-sys-surface-container-high);
  border-radius: 1.5rem;
  padding: 0.75rem 1.25rem;
  display: flex;
  flex-direction: row; /* Horizontal layout */
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--mat-sys-level1);
  /* Responsive dimensions with min-height */
  min-height: 4.5rem; /* Minimum height for 2 lines of text */
  width: 90%; /* Wider for small screens */
  max-width: 800px; /* Maximum width */
}

.quote-icon {
  color: var(--mat-sys-tertiary);
  margin-right: 0.75rem;
  display: flex;
  align-items: flex-start; /* Align to top for multi-line text */
  padding-top: 0.25rem; /* Align with first line of text */
  flex-shrink: 0; /* Prevent icon from shrinking */
}

.quote-text-container {
  flex: 1;
  display: flex;
  align-items: center;
  overflow: visible; /* Allow text to be fully visible */
  justify-content: center;
  padding: 0.25rem 0;
  min-width: 0; /* Allow container to shrink */
  max-width: calc(100% - 6rem); /* Leave space for controls */
}

.quote-content p {
  font: var(--mat-sys-title-medium);
  font-style: italic;
  color: var(--mat-sys-on-surface);
  margin: 0;
  /* Better text handling */
  overflow: visible; /* Allow text to be fully visible */
  white-space: normal;
  text-align: center;
  line-height: 1.4;
  min-height: 2.8em; /* Minimum height for 2 lines */
  width: 100%; /* Use full width of container */
}

.quote-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 1rem; /* Add more space between text and controls */
  flex-shrink: 0; /* Prevent controls from shrinking */
  align-self: flex-start; /* Align to the top when text wraps */
  padding-top: 0.25rem; /* Slight top padding for alignment */
}

.quote-counter {
  font-size: 0.75rem;
  color: var(--mat-sys-outline);
}

/* Small button for quote controls */
.small-button {
  width: 24px;
  height: 24px;
  line-height: 24px;
  padding: 0;
}

.small-button .mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  line-height: 16px;
}

/* Progress spinners */
.question-progress,
.fact-progress {
  margin: 0;
}

/* Customize spinner colors */
::ng-deep .mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,
::ng-deep
  .mat-mdc-progress-spinner
  .mdc-circular-progress__indeterminate-circle-graphic {
  stroke: var(--mat-sys-primary);
}

.fact-progress
  ::ng-deep
  .mat-mdc-progress-spinner
  .mdc-circular-progress__determinate-circle,
.fact-progress
  ::ng-deep
  .mat-mdc-progress-spinner
  .mdc-circular-progress__indeterminate-circle-graphic {
  stroke: var(--mat-sys-tertiary);
}

/* Questions container */
.questions-container {
  position: relative;
  min-height: 200px; /* Ensure consistent height */
}

/* Facts container - smaller height */
.facts-container {
  position: relative;
  min-height: 120px; /* Smaller height for facts */
}

/* Question and fact items */
.question-item,
.fun-fact {
  position: relative;
  width: 100%;
}

/* Counter for questions */
.question-counter {
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
  font-size: 0.8rem;
  color: var(--mat-sys-outline);
  background-color: var(--mat-sys-surface-container-highest);
  padding: 0.2rem 0.5rem;
  border-radius: 1rem;
}

/* Counter for facts - more compact */
.fact-counter {
  position: absolute;
  bottom: 0.3rem; /* Closer to bottom */
  right: 0.3rem; /* Closer to right */
  font-size: 0.7rem; /* Smaller font */
  color: var(--mat-sys-outline);
  background-color: var(--mat-sys-surface-container-highest);
  padding: 0.1rem 0.4rem; /* Smaller padding */
  border-radius: 0.75rem; /* Smaller radius */
}

.leaderboard-card {
  align-self: flex-start;
}

.leaderboard-card,
.fun-facts-card,
.interesting-questions-card {
  width: 100%;
  background-color: var(--mat-sys-surface-container);
  border-radius: 1rem;
  overflow: hidden;
  height: auto; /* Allow height to be determined by content */
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

/* Specific styling for each card type */
.fun-facts-card,
.interesting-questions-card {
  align-self: flex-start; /* Align to the top */
}

.leaderboard {
  width: 100%;
}

/* Ensure card content heights are based on their content */
.leaderboard-card mat-card-content,
.fun-facts-card mat-card-content,
.interesting-questions-card mat-card-content {
  height: auto;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  overflow-y: auto;
}

/* Specific styling for fun facts and questions card content */
.fun-facts-card mat-card-content,
.interesting-questions-card mat-card-content {
  flex: 1;
}

/* Specific styling for the fun facts card in the second row */
.second-row .fun-facts-card {
  width: 100%;
  max-width: 100%;
}

.leaderboard-header,
.player-row {
  display: grid;
  grid-template-columns: 4rem 1fr 6rem;
  align-items: center;
  padding: 0.75rem 1rem;
}

.leaderboard-header {
  font: var(--mat-sys-title-medium);
  color: var(--mat-sys-on-surface-variant);
}

.player-row {
  border-bottom: 1px solid var(--mat-sys-outline-variant);
  position: relative;
}

.player-row:last-child {
  border-bottom: none;
}

.winner-row {
  background-color: var(--mat-sys-tertiary-container);
  color: var(--mat-sys-on-tertiary-container);
}

.loser-row {
  background-color: var(--mat-sys-error-container);
  color: var(--mat-sys-on-error-container);
}

.current-user-row {
  font-weight: bold;
}

.rank-column {
  display: flex;
  justify-content: center;
}

.rank-badge {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: var(--mat-sys-surface-container-highest);
  color: var(--mat-sys-on-surface);
  font: var(--mat-sys-title-medium);
}

.winner-badge {
  background-color: var(--mat-sys-tertiary);
  color: var(--mat-sys-on-tertiary);
}

.loser-badge {
  background-color: var(--mat-sys-error);
  color: var(--mat-sys-on-error);
}

.player-column {
  padding-left: 0.5rem;
}

.player-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.player-avatar,
.player-avatar-placeholder {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--mat-sys-surface-container-highest);
}

.player-avatar {
  object-fit: cover;
}

.player-name {
  flex-grow: 1;
  font: var(--mat-sys-body-large);
}

.trophy-icon {
  color: var(--mat-sys-tertiary);
}

.score-column {
  font: var(--mat-sys-title-large);
  text-align: right;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(8rem, 1fr));
  gap: 1rem;
  padding: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1rem;
  background-color: var(--mat-sys-surface-container-high);
  border-radius: 0.5rem;
}

.stat-value {
  font: var(--mat-sys-headline-medium);
  color: var(--mat-sys-primary);
  margin-bottom: 0.5rem;
}

.stat-label {
  font: var(--mat-sys-body-medium);
  color: var(--mat-sys-on-surface-variant);
}

.action-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.action-buttons button {
  min-width: 180px;
  padding: 0.5rem 1rem;
  font: var(--mat-sys-title-small);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.action-buttons button:hover {
  transform: translateY(-2px);
  box-shadow: var(--mat-sys-level3);
}

/* TV Display Optimizations */
@media (min-width: 1920px) {
  .content-container {
    max-width: 1800px;
    margin: 0 auto;
  }

  .leaderboard-card mat-card-title,
  .fun-facts-card mat-card-title,
  .interesting-questions-card mat-card-title {
    font-size: 2rem;
  }

  .player-name,
  .score-column,
  .rank-badge {
    font-size: 1.5rem;
  }

  .question-text,
  .question-answer {
    font-size: 1.5rem;
    line-height: 1.6;
  }

  /* Quote styling for TV - still single line but larger */
  .quote-content {
    padding: 0.75rem 1.5rem;
    border-radius: 3rem;
  }

  .quote-content p {
    font-size: 1.2rem;
  }

  .quote-counter {
    font-size: 0.9rem;
  }

  .fun-facts-quote {
    max-width: 1200px; /* Wider on TV */
  }

  /* Larger controls for TV */
  .small-button {
    width: 32px;
    height: 32px;
    line-height: 32px;
  }

  .small-button .mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
    line-height: 20px;
  }

  .questions-container {
    min-height: 300px; /* Larger height for TV displays */
  }

  .facts-container {
    min-height: 180px; /* Smaller height for facts even on TV */
  }

  .question-counter,
  .fact-counter {
    font-size: 1.2rem;
    padding: 0.3rem 0.8rem;
  }

  /* Larger spinners for TV displays */
  .mat-progress-spinner {
    transform: scale(1.5);
  }

  /* Increase gap between spinner and button for TV displays */
  .card-controls {
    gap: 1.25rem;
  }

  .card-controls button {
    transform: scale(1.5);
    margin: 0 0.5rem;
  }
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
  .winner-screen-container {
    padding: var(--spacing-xs, 0.5rem);
  }

  .content-container {
    gap: var(--spacing-xs, 0.5rem);
  }

  /* Mobile quote styling - even more compact */
  .fun-facts-quote {
    margin-bottom: var(--spacing-xs, 0.25rem);
  }

  .quote-content {
    padding: 0.4rem 0.75rem;
    border-radius: 1.5rem;
  }

  .quote-content p {
    font-size: 0.8rem;
  }

  .quote-counter {
    font-size: 0.7rem;
  }

  /* Hide the counter on very small screens */
  @media (max-width: 400px) {
    .quote-counter {
      display: none;
    }
  }

  .questions-container,
  .facts-container {
    min-height: 150px; /* Smaller height on mobile */
  }

  .question-counter,
  .fact-counter {
    font-size: 0.7rem;
  }
  .leaderboard-header,
  .player-row {
    grid-template-columns: 3rem 1fr 4rem;
    padding: 0.5rem;
  }

  .player-avatar,
  .player-avatar-placeholder {
    width: 2rem;
    height: 2rem;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
    max-width: 20rem;
  }
}

@media (max-width: 480px) {
  .winner-screen-container {
    padding: 0.5rem;
  }

  .winner-announcement {
    padding: 0.75rem 1rem;
  }

  .winner-emoji {
    font-size: 2rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Card Headers */
.leaderboard-card mat-card-header {
  background-color: var(--mat-sys-surface-container-high);
  padding: 1rem;
  border-bottom: 1px solid var(--mat-sys-outline-variant);
}

/* Thinner header for questions card */
.interesting-questions-card mat-card-header {
  background-color: var(--mat-sys-surface-container-high);
  padding: 0.5rem 1rem; /* Reduced vertical padding */
  border-bottom: 1px solid var(--mat-sys-outline-variant);
  min-height: 1.5rem; /* Reduced height */
}

.leaderboard-card mat-card-title,
.fun-facts-card mat-card-title,
.interesting-questions-card mat-card-title {
  font: var(--mat-sys-title-large);
  margin: 0;
  color: var(--mat-sys-on-surface);
}

/* TV Display Optimizations */
@media (min-width: 1920px) {
  .section-content {
    max-width: 1600px;
  }

  .leaderboard-card mat-card-title,
  .fun-facts-card mat-card-title,
  .interesting-questions-card mat-card-title {
    font-size: 2rem;
  }

  .player-name,
  .score-column,
  .rank-badge {
    font-size: 1.5rem;
  }

  .fun-fact p,
  .question-text,
  .question-answer {
    font-size: 1.5rem;
    line-height: 1.6;
  }

  .section-indicator {
    width: 1rem;
    height: 1rem;
  }

  .section-navigation button {
    transform: scale(1.5);
    margin: 0 1rem;
  }

  .section-navigation button:hover {
    transform: scale(1.7);
  }
}

.fun-facts-list {
  display: grid;
  grid-template-columns: 1fr; /* Default to single column */
  gap: 1rem;
  padding: 0.5rem;
  max-height: none; /* Remove height limit */
  overflow-y: visible;
}

/* Medium screens - 2 columns of fun facts */
@media (min-width: 768px) {
  .fun-facts-list {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Large screens - 4 columns of fun facts */
@media (min-width: 1200px) {
  .fun-facts-list {
    grid-template-columns: repeat(4, 1fr);
  }
}

.fun-fact {
  display: flex;
  align-items: flex-start; /* Align to top for better layout */
  gap: 0.5rem; /* Reduced gap */
  padding: 0.5rem 0.75rem; /* Reduced vertical padding */
  background-color: var(--mat-sys-surface-container-high);
  border-radius: 0.75rem;
  border-left: 3px solid var(--mat-sys-tertiary);
  overflow: visible;
  height: 100%; /* Make all fun facts the same height */
  position: relative; /* For positioning the counter */
  padding-bottom: 1.75rem; /* Reduced space for the counter */
}

.fun-fact p {
  margin: 0;
  font: var(--mat-sys-body-medium); /* Smaller font size */
  color: var(--mat-sys-on-surface);
  line-height: 1.4; /* Tighter line height */
}

.fact-icon {
  color: var(--mat-sys-tertiary);
  min-width: 20px; /* Smaller icon */
  min-height: 20px; /* Smaller icon */
  font-size: 20px; /* Smaller icon */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Icon Container */
.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px; /* Smaller container */
  height: 20px; /* Smaller container */
  flex-shrink: 0;
}

/* Interesting Questions */
.questions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 0.5rem;
  max-height: none; /* Allow the list to grow based on content */
  overflow-y: auto; /* Add scrolling if needed */
}

.question-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: var(--mat-sys-surface-container-high);
  border-radius: 0.75rem;
  border-left: 3px solid var(--mat-sys-secondary);
  overflow: visible;
  position: relative; /* For positioning the counter */
  padding-bottom: 2rem; /* Space for the counter */
}

.question-category {
  font: var(--mat-sys-title-small);
  color: var(--mat-sys-secondary);
  font-weight: 500;
}

.question-text {
  font: var(--mat-sys-body-large);
  color: var(--mat-sys-on-surface);
  margin: 0.5rem 0;
}

.question-answer {
  font: var(--mat-sys-body-large);
  color: var(--mat-sys-primary);
  padding-top: 0.5rem;
  border-top: 1px solid var(--mat-sys-outline-variant);
}

.question-reason {
  font: var(--mat-sys-body-medium);
  color: var(--mat-sys-secondary);
  margin-top: 0.5rem;
  font-style: italic;
}

/* Confetti Animation */
.confetti-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
  overflow: hidden;
}

.confetti {
  position: absolute;
  width: 4px;
  height: 15px;
  background-color: var(--mat-sys-primary);
  opacity: 0.7;
  animation: confetti-fall var(--fall-duration, 6s) ease-in-out infinite,
    confetti-shake var(--shake-duration, 3s) ease-in-out infinite,
    confetti-rotate var(--rotate-duration, 4s) linear infinite;
  transform-origin: center center;
  border-radius: 1px;
}

/* Thinner confetti variant */
.confetti-thin {
  width: 2px;
  height: 12px;
  opacity: 0.8;
}

/* Tiny confetti variant */
.confetti-tiny {
  width: 3px;
  height: 5px;
  opacity: 0.9;
}

/* Rectangle confetti variant */
.confetti-rect {
  width: 6px;
  height: 6px;
  border-radius: 0;
}

/* Line confetti variant */
.confetti-line {
  width: 1px;
  height: 20px;
  opacity: 0.6;
}

/* Skewed confetti variant */
.confetti-skewed {
  transform: skew(15deg, 15deg);
  width: 3px;
  height: 10px;
  opacity: 0.75;
}

/* Curved confetti variant */
.confetti-curved {
  width: 4px;
  height: 8px;
  border-radius: 50% 50% 0 0;
  opacity: 0.85;
}

/* Color variations */
.confetti:nth-child(odd) {
  background-color: var(--mat-sys-tertiary);
}

.confetti:nth-child(even) {
  background-color: var(--mat-sys-secondary);
}

.confetti:nth-child(3n) {
  background-color: var(--mat-sys-error);
}

.confetti:nth-child(4n) {
  background-color: var(--mat-sys-primary-container);
}

.confetti:nth-child(5n) {
  background-color: var(--mat-sys-tertiary-container);
}

.confetti:nth-child(7n) {
  background-color: var(--mat-sys-secondary-container);
}

/* Fall animation with more natural movement */
@keyframes confetti-fall {
  0% {
    top: -5%;
    transform: translateX(0);
  }
  25% {
    transform: translateX(calc(20px * var(--random, 0.5) - 10px));
  }
  50% {
    transform: translateX(calc(-15px * var(--random, 0.5) + 5px));
  }
  75% {
    transform: translateX(calc(25px * var(--random, 0.5) - 15px));
  }
  100% {
    top: 100%;
    transform: translateX(calc(30px * var(--random, 0.5) - 20px));
  }
}

/* More natural side-to-side movement */
@keyframes confetti-shake {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(calc(15px * var(--random, 0.5) - 5px));
  }
  50% {
    transform: translateX(calc(-10px * var(--random, 0.5) + 5px));
  }
  75% {
    transform: translateX(calc(20px * var(--random, 0.5) - 10px));
  }
  100% {
    transform: translateX(0);
  }
}

/* Rotation animation for more natural movement */
@keyframes confetti-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(calc(360deg * var(--rotate-direction, 1)));
  }
}

/* Generate random positions for confetti with more natural irregularity */
.confetti:nth-child(1) {
  left: 3%;
  animation-delay: 0.1s;
  --random: 0.17;
  --fall-duration: 5.7s;
  --shake-duration: 2.8s;
  --rotate-duration: 3.9s;
  --rotate-direction: 1;
}
.confetti:nth-child(2) {
  left: 12%;
  animation-delay: 0.4s;
  --random: 0.33;
  --fall-duration: 6.2s;
  --shake-duration: 3.1s;
  --rotate-duration: 4.3s;
  --rotate-direction: -1;
}
.confetti:nth-child(3) {
  left: 23%;
  animation-delay: 0.9s;
  --random: 0.52;
  --fall-duration: 5.3s;
  --shake-duration: 2.9s;
  --rotate-duration: 4.1s;
  --rotate-direction: 1;
}
.confetti:nth-child(4) {
  left: 37%;
  animation-delay: 1.3s;
  --random: 0.71;
  --fall-duration: 6.7s;
  --shake-duration: 3.3s;
  --rotate-duration: 3.7s;
  --rotate-direction: -1;
}
.confetti:nth-child(5) {
  left: 42%;
  animation-delay: 1.8s;
  --random: 0.89;
  --fall-duration: 5.9s;
  --shake-duration: 3.2s;
  --rotate-duration: 4.2s;
  --rotate-direction: 1;
}
.confetti:nth-child(6) {
  left: 58%;
  animation-delay: 2.3s;
  --random: 0.24;
  --fall-duration: 6.4s;
  --shake-duration: 2.7s;
  --rotate-duration: 3.8s;
  --rotate-direction: -1;
}
.confetti:nth-child(7) {
  left: 67%;
  animation-delay: 2.7s;
  --random: 0.43;
  --fall-duration: 5.5s;
  --shake-duration: 3.4s;
  --rotate-duration: 4.4s;
  --rotate-direction: 1;
}
.confetti:nth-child(8) {
  left: 72%;
  animation-delay: 3.2s;
  --random: 0.62;
  --fall-duration: 6.1s;
  --shake-duration: 2.6s;
  --rotate-duration: 3.6s;
  --rotate-direction: -1;
}
.confetti:nth-child(9) {
  left: 83%;
  animation-delay: 3.7s;
  --random: 0.81;
  --fall-duration: 5.8s;
  --shake-duration: 3.5s;
  --rotate-duration: 4.5s;
  --rotate-direction: 1;
}
.confetti:nth-child(10) {
  left: 92%;
  animation-delay: 4.2s;
  --random: 0.13;
  --fall-duration: 6.3s;
  --shake-duration: 2.5s;
  --rotate-duration: 3.5s;
  --rotate-direction: -1;
}
.confetti:nth-child(11) {
  left: 7%;
  animation-delay: 0.3s;
  --random: 0.37;
  --fall-duration: 5.4s;
  --shake-duration: 3.6s;
  --rotate-duration: 4.6s;
  --rotate-direction: 1;
}
.confetti:nth-child(12) {
  left: 18%;
  animation-delay: 0.6s;
  --random: 0.56;
  --fall-duration: 6.5s;
  --shake-duration: 2.4s;
  --rotate-duration: 3.4s;
  --rotate-direction: -1;
}
.confetti:nth-child(13) {
  left: 27%;
  animation-delay: 1.1s;
  --random: 0.75;
  --fall-duration: 5.6s;
  --shake-duration: 3.7s;
  --rotate-duration: 4.7s;
  --rotate-direction: 1;
}
.confetti:nth-child(14) {
  left: 43%;
  animation-delay: 1.5s;
  --random: 0.94;
  --fall-duration: 6.6s;
  --shake-duration: 2.3s;
  --rotate-duration: 3.3s;
  --rotate-direction: -1;
}
.confetti:nth-child(15) {
  left: 53%;
  animation-delay: 2s;
  --random: 0.28;
  --fall-duration: 5.2s;
  --shake-duration: 3.8s;
  --rotate-duration: 4.8s;
  --rotate-direction: 1;
}

/* Additional confetti pieces for more variety */
.confetti:nth-child(16) {
  left: 63%;
  animation-delay: 2.5s;
  --random: 0.47;
  --fall-duration: 6.8s;
  --shake-duration: 2.2s;
  --rotate-duration: 3.2s;
  --rotate-direction: -1;
}
.confetti:nth-child(17) {
  left: 78%;
  animation-delay: 2.9s;
  --random: 0.66;
  --fall-duration: 5.1s;
  --shake-duration: 3.9s;
  --rotate-duration: 4.9s;
  --rotate-direction: 1;
}
.confetti:nth-child(18) {
  left: 88%;
  animation-delay: 3.4s;
  --random: 0.85;
  --fall-duration: 6.9s;
  --shake-duration: 2.1s;
  --rotate-duration: 3.1s;
  --rotate-direction: -1;
}
.confetti:nth-child(19) {
  left: 5%;
  animation-delay: 3.9s;
  --random: 0.19;
  --fall-duration: 5s;
  --shake-duration: 4s;
  --rotate-duration: 5s;
  --rotate-direction: 1;
}
.confetti:nth-child(20) {
  left: 15%;
  animation-delay: 4.4s;
  --random: 0.38;
  --fall-duration: 7s;
  --shake-duration: 2s;
  --rotate-duration: 3s;
  --rotate-direction: -1;
}
.confetti:nth-child(21) {
  left: 25%;
  animation-delay: 0.2s;
  --random: 0.57;
  --fall-duration: 4.9s;
  --shake-duration: 4.1s;
  --rotate-duration: 5.1s;
  --rotate-direction: 1;
}
.confetti:nth-child(22) {
  left: 35%;
  animation-delay: 0.7s;
  --random: 0.76;
  --fall-duration: 7.1s;
  --shake-duration: 1.9s;
  --rotate-duration: 2.9s;
  --rotate-direction: -1;
}
.confetti:nth-child(23) {
  left: 45%;
  animation-delay: 1.2s;
  --random: 0.95;
  --fall-duration: 4.8s;
  --shake-duration: 4.2s;
  --rotate-duration: 5.2s;
  --rotate-direction: 1;
}
.confetti:nth-child(24) {
  left: 55%;
  animation-delay: 1.7s;
  --random: 0.29;
  --fall-duration: 7.2s;
  --shake-duration: 1.8s;
  --rotate-duration: 2.8s;
  --rotate-direction: -1;
}
.confetti:nth-child(25) {
  left: 65%;
  animation-delay: 2.2s;
  --random: 0.48;
  --fall-duration: 4.7s;
  --shake-duration: 4.3s;
  --rotate-duration: 5.3s;
  --rotate-direction: 1;
}
.confetti:nth-child(26) {
  left: 75%;
  animation-delay: 2.7s;
  --random: 0.67;
  --fall-duration: 7.3s;
  --shake-duration: 1.7s;
  --rotate-duration: 2.7s;
  --rotate-direction: -1;
}
.confetti:nth-child(27) {
  left: 85%;
  animation-delay: 3.2s;
  --random: 0.86;
  --fall-duration: 4.6s;
  --shake-duration: 4.4s;
  --rotate-duration: 5.4s;
  --rotate-direction: 1;
}
.confetti:nth-child(28) {
  left: 95%;
  animation-delay: 3.7s;
  --random: 0.15;
  --fall-duration: 7.4s;
  --shake-duration: 1.6s;
  --rotate-duration: 2.6s;
  --rotate-direction: -1;
}
.confetti:nth-child(29) {
  left: 10%;
  animation-delay: 4.2s;
  --random: 0.34;
  --fall-duration: 4.5s;
  --shake-duration: 4.5s;
  --rotate-duration: 5.5s;
  --rotate-direction: 1;
}
.confetti:nth-child(30) {
  left: 20%;
  animation-delay: 4.7s;
  --random: 0.53;
  --fall-duration: 7.5s;
  --shake-duration: 1.5s;
  --rotate-duration: 2.5s;
  --rotate-direction: -1;
}

/* Additional confetti pieces for even more variety */
.confetti:nth-child(31) {
  left: 30%;
  animation-delay: 0.15s;
  --random: 0.22;
  --fall-duration: 4.3s;
  --shake-duration: 3.3s;
  --rotate-duration: 5.7s;
  --rotate-direction: 1;
}
.confetti:nth-child(32) {
  left: 40%;
  animation-delay: 0.65s;
  --random: 0.41;
  --fall-duration: 7.7s;
  --shake-duration: 1.3s;
  --rotate-duration: 2.3s;
  --rotate-direction: -1;
}
.confetti:nth-child(33) {
  left: 50%;
  animation-delay: 1.15s;
  --random: 0.61;
  --fall-duration: 4.2s;
  --shake-duration: 4.6s;
  --rotate-duration: 5.6s;
  --rotate-direction: 1;
}
.confetti:nth-child(34) {
  left: 60%;
  animation-delay: 1.65s;
  --random: 0.79;
  --fall-duration: 7.8s;
  --shake-duration: 1.2s;
  --rotate-duration: 2.2s;
  --rotate-direction: -1;
}
.confetti:nth-child(35) {
  left: 70%;
  animation-delay: 2.15s;
  --random: 0.97;
  --fall-duration: 4.1s;
  --shake-duration: 4.7s;
  --rotate-duration: 5.8s;
  --rotate-direction: 1;
}
.confetti:nth-child(36) {
  left: 80%;
  animation-delay: 2.65s;
  --random: 0.26;
  --fall-duration: 7.9s;
  --shake-duration: 1.1s;
  --rotate-duration: 2.1s;
  --rotate-direction: -1;
}
.confetti:nth-child(37) {
  left: 90%;
  animation-delay: 3.15s;
  --random: 0.45;
  --fall-duration: 4s;
  --shake-duration: 4.8s;
  --rotate-duration: 5.9s;
  --rotate-direction: 1;
}
.confetti:nth-child(38) {
  left: 8%;
  animation-delay: 3.65s;
  --random: 0.64;
  --fall-duration: 8s;
  --shake-duration: 1s;
  --rotate-duration: 2s;
  --rotate-direction: -1;
}
.confetti:nth-child(39) {
  left: 17%;
  animation-delay: 4.15s;
  --random: 0.83;
  --fall-duration: 3.9s;
  --shake-duration: 4.9s;
  --rotate-duration: 6s;
  --rotate-direction: 1;
}
.confetti:nth-child(40) {
  left: 33%;
  animation-delay: 4.65s;
  --random: 0.12;
  --fall-duration: 8.1s;
  --shake-duration: 0.9s;
  --rotate-duration: 1.9s;
  --rotate-direction: -1;
}

/* Media query for mobile devices */
@media screen and (max-width: 600px) {
  .winner-screen-container {
    padding: 1rem;
  }

  .winner-card {
    padding: 1rem;
  }

  .winner-card-content {
    padding: 0.5rem;
  }

  .winner-name {
    font-size: 1.5rem;
  }

  .winner-score {
    font-size: 1.25rem;
  }

  .leaderboard-title {
    font-size: 1.25rem;
  }

  .player-name,
  .player-score {
    font-size: 1rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }

  .action-buttons button {
    width: 100%;
  }

  /* Fun facts adjustments for small screens */
  .quote-content {
    width: 95%;
    padding: 0.5rem 0.75rem;
    min-height: 5rem; /* Slightly taller on mobile */
  }

  .quote-content p {
    font-size: 0.9rem;
    line-height: 1.3;
  }

  .quote-controls {
    margin-left: 0.5rem;
  }

  .quote-icon {
    margin-right: 0.5rem;
  }
}

/* Cast mode styles - focused on layout optimization */
.is-cast .winner-screen-container {
  padding: 0.5rem;
  gap: 0.5rem;
  height: 100vh;
  justify-content: space-between;
  overflow: hidden;
}

.is-cast .winner-header {
  margin-bottom: 0.5rem;
}

.is-cast .content-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
  width: 100%;
  height: calc(100vh - 200px);
  margin-bottom: 0.5rem;
  align-items: stretch;
}

.is-cast .leaderboard-card,
.is-cast .interesting-questions-card {
  display: flex;
  flex-direction: column;
  margin-bottom: 0.5rem;
}

.is-cast .leaderboard-card mat-card-content,
.is-cast .interesting-questions-card mat-card-content {
  flex: 1 1 auto;
  overflow: auto;
}

.is-cast .fun-facts-quote {
  margin-bottom: 0.5rem;
}

.is-cast .action-buttons {
  margin-top: 0.5rem;
  gap: 0.5rem;
}

.is-cast .leaderboard-header,
.is-cast .player-row {
  padding: 0.5rem;
}
