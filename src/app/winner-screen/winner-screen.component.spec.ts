import { ComponentFixture, TestBed } from '@angular/core/testing';
import { WinnerScreenComponent } from './winner-screen.component';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { GameMode } from '../../../functions/src/resources';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('WinnerScreenComponent', () => {
  let component: WinnerScreenComponent;
  let fixture: ComponentFixture<WinnerScreenComponent>;
  let routerSpy: jasmine.SpyObj<Router>;
  let authServiceSpy: jasmine.SpyObj<AuthService>;

  beforeEach(async () => {
    routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    authServiceSpy = jasmine.createSpyObj('AuthService', ['getUser']);
    
    // Mock the getUser method to return a test user
    authServiceSpy.getUser.and.returnValue({ uid: 'test-user-id' } as any);

    await TestBed.configureTestingModule({
      imports: [
        WinnerScreenComponent,
        MatCardModule,
        MatButtonModule,
        MatIconModule,
        MatDividerModule,
        NoopAnimationsModule
      ],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: AuthService, useValue: authServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(WinnerScreenComponent);
    component = fixture.componentInstance;
    
    // Set up test room data
    component.room = {
      roomId: 'test-room',
      host: 'host-id',
      mode: GameMode.BUZZER,
      createdAt: Date.now(),
      playerIds: ['player1', 'player2', 'player3'],
      playerInfos: [
        { score: 1000, userName: 'Player 1' },
        { score: 500, userName: 'Player 2' },
        { score: 1500, userName: 'Player 3' }
      ],
      gameState: {
        gameProgress: {
          type: 'RoomSummary'
        }
      }
    } as any;
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should calculate player stats correctly', () => {
    expect(component.highestScore).toBe(1500);
    expect(component.winnerIds).toContain('player3');
    expect(component.playerStats.length).toBe(3);
    
    // Check that player3 is marked as winner
    const winner = component.playerStats.find(p => p.id === 'player3');
    expect(winner?.isWinner).toBeTrue();
    expect(winner?.rank).toBe(1);
    
    // Check other players' ranks
    const player1 = component.playerStats.find(p => p.id === 'player1');
    expect(player1?.rank).toBe(2);
    
    const player2 = component.playerStats.find(p => p.id === 'player2');
    expect(player2?.rank).toBe(3);
  });

  it('should navigate to lobby when returnToLobby is called', () => {
    component.returnToLobby();
    expect(routerSpy.navigate).toHaveBeenCalledWith(['/lobby']);
  });

  it('should navigate to room when playAgain is called', () => {
    component.playAgain();
    expect(routerSpy.navigate).toHaveBeenCalledWith(['/room', 'test-room']);
  });
});
