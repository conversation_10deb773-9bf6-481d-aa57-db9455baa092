<div class="winner-screen-container" @fadeIn>
  <div class="confetti-container" *ngIf="showConfetti">
    <div
      class="confetti"
      *ngFor="
        let i of [
          1,
          2,
          3,
          4,
          5,
          6,
          7,
          8,
          9,
          10,
          11,
          12,
          13,
          14,
          15,
          16,
          17,
          18,
          19,
          20,
          21,
          22,
          23,
          24,
          25,
          26,
          27,
          28,
          29,
          30,
          31,
          32,
          33,
          34,
          35,
          36,
          37,
          38,
          39,
          40
        ]
      "
      [ngClass]="{
        'confetti-thin': i % 3 === 0,
        'confetti-tiny': i % 5 === 0,
        'confetti-rect': i % 2 === 0,
        'confetti-line': i % 7 === 0,
        'confetti-skewed': i % 4 === 0,
        'confetti-curved': i % 6 === 0
      }"
    ></div>
  </div>

  <div class="winner-header" @bounceIn>
    <!-- Solo player announcement -->
    <div class="winner-announcement" *ngIf="isSoloPlayer" @pulse>
      <h2>Solo Victory</h2>
      <div class="winner-emoji">🤷‍♂️</div>
      <div class="winner-comment">{{ soloPlayerComment }}</div>
    </div>

    <!-- Normal winner announcement -->
    <div
      class="winner-announcement"
      *ngIf="winnerIds.length === 1 && !isSoloPlayer"
      @pulse
    >
      <h2>Winner: {{ getWinnerName() }}</h2>
      <div class="winner-emoji">🏆</div>
      <div class="winner-comment">{{ winnerComment }}</div>
    </div>

    <!-- Tie announcement -->
    <div
      class="winner-announcement"
      *ngIf="winnerIds.length > 1 && !isSoloPlayer"
      @pulse
    >
      <h2>It's a tie!</h2>
      <div class="winner-emoji">🏆</div>
      <div class="winner-comment">{{ winnerComment }}</div>
    </div>

    <!-- Negative score announcement -->
    <div
      class="negative-score-announcement"
      *ngIf="hasNegativeScorer && !isSoloPlayer"
      @shakeIn
    >
      <h3 *ngIf="getNegativeScorers()">
        {{ getNegativeScorers() }}
        {{ getNegativeScorers().includes(",") ? "have" : "has" }} a negative
        score!
      </h3>
      <div class="negative-emoji">💩</div>
      <div class="negative-comment">{{ negativeScoreComment }}</div>
    </div>
    <!-- Fun Facts Quote (single line) -->
    <div class="fun-facts-quote" *ngIf="funFacts.length > 0">
      <div class="quote-content">
        <div class="quote-icon">
          <mat-icon>format_quote</mat-icon>
        </div>
        <div class="quote-text-container">
          <p @factCycle *ngIf="funFacts.length > 0">
            {{ funFacts[currentFactIndex] }}
          </p>
        </div>
        <div class="quote-controls">
          <div class="quote-counter">
            {{ currentFactIndex + 1 }}/{{ funFacts.length }}
          </div>
          <mat-progress-spinner
            class="fact-progress"
            [diameter]="16"
            mode="determinate"
            [value]="factProgressValue"
          ></mat-progress-spinner>
          <button
            mat-icon-button
            (click)="nextFact()"
            aria-label="Next quote"
            class="small-button"
          >
            <mat-icon>skip_next</mat-icon>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Content container with all sections visible -->
  <div class="content-container">
    <!-- Leaderboard Card -->
    <mat-card class="leaderboard-card">
      <mat-card-header>
        <mat-card-title
          >Leaderboard
          <mat-icon
            class="info-icon"
            matTooltip="Final player rankings and scores"
            >emoji_events</mat-icon
          ></mat-card-title
        >
      </mat-card-header>
      <mat-card-content>
        <div class="leaderboard">
          <div class="leaderboard-header">
            <div class="rank-column">Rank</div>
            <div class="player-column">Player</div>
            <div class="score-column">Score</div>
          </div>
          <mat-divider></mat-divider>
          <div
            *ngFor="let player of playerStats"
            class="player-row"
            [class.winner-row]="player.isWinner"
            [class.loser-row]="player.isLoser"
            [class.current-user-row]="player.isCurrentUser"
          >
            <div class="rank-column">
              <div
                class="rank-badge"
                [class.winner-badge]="player.rank === 1"
                [class.loser-badge]="player.isLoser"
              >
                {{ player.rank }}
              </div>
            </div>
            <div class="player-column">
              <div class="player-info">
                <img
                  *ngIf="player.photoUrl"
                  [src]="player.photoUrl"
                  class="player-avatar"
                  alt="Player avatar"
                />
                <div *ngIf="!player.photoUrl" class="player-avatar-placeholder">
                  <mat-icon>person</mat-icon>
                </div>
                <span class="player-name">{{ player.name }}</span>
                <mat-icon *ngIf="player.isWinner" class="trophy-icon"
                  >emoji_events</mat-icon
                >
              </div>
            </div>
            <div class="score-column">{{ player.score }}</div>
            <div
              class="loser-comment"
              *ngIf="player.isLoser && loserComment && winnerIds.length === 1"
            >
              <div class="icon-container">
                <mat-icon class="loser-icon"
                  >sentiment_very_dissatisfied</mat-icon
                >
              </div>
              <span>{{ loserComment }}</span>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Memorable Questions Card with Auto-Cycling -->
    <mat-card
      class="interesting-questions-card"
      *ngIf="memorableQuestions.length > 0"
    >
      <mat-card-header>
        <mat-card-title class="card-title"
          >Memorable Questions
          <mat-icon
            class="info-icon"
            matTooltip="Interesting questions from this game"
            >psychology</mat-icon
          ></mat-card-title
        >
        <div class="card-controls">
          <!-- Progress spinner for questions (moved to title bar) -->
          <mat-progress-spinner
            class="question-progress"
            [diameter]="24"
            mode="determinate"
            [value]="questionProgressValue"
          ></mat-progress-spinner>

          <button
            mat-icon-button
            (click)="nextQuestion()"
            aria-label="Next question"
          >
            <mat-icon>skip_next</mat-icon>
          </button>
        </div>
      </mat-card-header>

      <mat-card-content>
        <div class="questions-container">
          <!-- Only render the active question -->
          <div
            class="question-item"
            *ngIf="memorableQuestions.length > 0"
            @factCycle
          >
            <div class="question-category">
              {{ memorableQuestions[currentQuestionIndex].category }}
            </div>
            <div
              class="question-text"
              [innerHTML]="memorableQuestions[currentQuestionIndex].question"
            ></div>
            <div class="question-answer">
              <strong>Answer:</strong>
              {{ memorableQuestions[currentQuestionIndex].answer }}
            </div>
            <div
              class="question-reason"
              *ngIf="memorableQuestions[currentQuestionIndex].reason"
            >
              <em>{{ memorableQuestions[currentQuestionIndex].reason }}</em>
            </div>
            <div class="question-counter">
              {{ currentQuestionIndex + 1 }} / {{ memorableQuestions.length }}
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Fun Facts Card removed - now displayed as a quote above -->
  </div>

  <div class="action-buttons" @bounceIn>
    <button
      mat-raised-button
      color="primary"
      (click)="returnToLobby()"
      *ngIf="!isCast"
    >
      <mat-icon>home</mat-icon>
      Return to Lobby
    </button>

    <!-- Return to Game Board button (only visible to host) -->
    <button
      *ngIf="isHost && originalRoomState"
      mat-raised-button
      color="accent"
      (click)="returnToGameBoard()"
      matTooltip="Return to the game board (only visible to host)"
    >
      <mat-icon>replay</mat-icon>
      Return to Game Board
    </button>
  </div>
</div>
