import {
  ChangeDetectorRef,
  Component,
  inject,
  NgZone,
  OnInit
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { WaitingRoomComponent } from '../waiting-room/waiting-room.component';
import { Room, Round } from '../../../functions/src/resources';
import { distinctUntilChanged, map, Subscription } from 'rxjs';
import { CommonModule } from '@angular/common';
import { PlayingRoomComponent } from '../playing-room/playing-room.component';
import { ChatRoomComponent } from '../chat-room/chat-room.component';
import { FirebaseFirestore } from '@capacitor-firebase/firestore';
import { AuthService } from '../services/auth.service';
import { TopBarComponent } from '../top-bar/top-bar.component';
import { WinnerScreenComponent } from '../winner-screen/winner-screen.component';

@Component({
  selector: 'app-room',
  imports: [
    WaitingRoomComponent,
    CommonModule,
    PlayingRoomComponent,
    Chat<PERSON>oomComponent,
    TopBarComponent,
    WinnerScreenComponent
  ],
  templateUrl: './room.component.html',
  styleUrl: './room.component.css'
})
export class RoomComponent implements OnInit {
  room?: Room;
  currentRound?: Round;
  authService = inject(AuthService);
  route: ActivatedRoute = inject(ActivatedRoute);

  cdr = inject(ChangeDetectorRef);
  callbackId?: string;
  isCast: boolean = false;
  private routeSub: Subscription;
  zone = inject(NgZone); // Inject NgZone

  ngOnDestroy(): void {
    this.routeSub.unsubscribe(); // IMPORTANT: Prevent memory leaks
  }
  constructor() {
    this.routeSub = this.route.data.subscribe(data => {
      this.isCast = data['isCast']; // Access the data
    });
  }
  ngOnInit() {
    this.route.params
      .pipe(
        map(params => params['roomId']),
        distinctUntilChanged()
      )
      .subscribe(async (roomId?: string) => {
        // Unsubscribe from the previous subscription if it exists
        if (this.callbackId) {
          FirebaseFirestore.removeSnapshotListener({
            callbackId: this.callbackId
          });
          this.callbackId = undefined;
        }

        // Handle the case where roomId is undefined (optional)
        if (!roomId) {
          this.room = undefined;
          // Optionally redirect to a different route or display an error
          return;
        }
        const roomRef = `rooms/${roomId}`;
        this.callbackId = await FirebaseFirestore.addDocumentSnapshotListener(
          { reference: roomRef },
          (event, error) => {
            this.zone.run(() => {
              if (error) {
                console.log(error);
              } else if (event?.snapshot.data == null) {
                this.room = undefined;
              } else {
                this.room = event.snapshot.data as Room;
                const newRoomData = this.room; // Get new data first
                console.log(
                  'New room type:',
                  newRoomData.gameState.gameProgress.type
                ); // <-- Log the type
                console.log(
                  'Current room type before update:',
                  this.room?.gameState.gameProgress.type
                ); // Log old type

                this.cdr.markForCheck();
                console.log('Room updated, markForCheck called.');
                console.log('Detecting changes');
              }
            });
          }
        );
      });
  }

  amIPartOfRoom(): boolean {
    return (
      this.room?.playerIds.some(id => id === this.authService.getUser()?.uid) ||
      false
    );
  }
}
