import {
  Component,
  ElementRef,
  HostListener,
  inject,
  OnInit,
  ViewChild
} from '@angular/core';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';

import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterOutlet
} from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { AuthService } from './services/auth.service';
import { filter, Subscription } from 'rxjs';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { SettingsComponent } from './settings/settings.component';
import { User } from '@capacitor-firebase/authentication';

import { SettingsService } from './services/settings.service';
import { CastComponent } from './cast/cast.component';

@Component({
  selector: 'app-root',
  imports: [
    RouterOutlet,
    MatToolbarModule,
    MatIconModule,
    CommonModule,
    FormsModule,
    MatSlideToggleModule,
    MatButtonModule,
    MatCardModule,
    MatButtonModule
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  standalone: true
})
export class AppComponent implements OnInit {
  title = 'jeopardy-gpt';
  isLightTheme = false;
  auth = inject(AuthService);
  router = inject(Router);
  currentRoutePath = '';
  private authStateSubscription?: Subscription;
  private routerEventsSubscription?: Subscription;

  ngOnInit() {
    this.routerEventsSubscription = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        this.currentRoutePath = event.urlAfterRedirects; // Get the URL after redirects
        this.authStateSubscription = this.auth.getAuthState().subscribe(
          user => {
            if (
              !user &&
              !this.currentRoutePath.startsWith('/login') &&
              !this.currentRoutePath.startsWith('/test') &&
              !this.currentRoutePath.startsWith('/castRoom')
            ) {
              // Navigate to /login?redirect=<current_url_path>
              this.router.navigate(['/login'], {
                queryParams: { redirect: this.currentRoutePath }
              });
            }
          },
          error => {
            console.error('Auth State Error:', error);
          }
        );
      });
  }
}
