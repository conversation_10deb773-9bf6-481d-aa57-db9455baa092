.cast-container {
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--mat-sys-tertiary-container) !important;
  color: var(--mat-sys-on-tertiary-container) !important;
  height: 100%;
  width: 100%;
  aspect-ratio: 1/1;
}

/* Adjust size for different screen sizes */
@media screen and (max-width: 600px) {
  .cast-container {
    transform: scale(0.85);
    width: 36px !important;
    height: 36px !important;
  }
}
.cast {
  --connected-color: var(--mat-sys-on-tertiary-container);
  --disconnected-color: var(--mat-sys-on-tertiary-container);
  width: 70%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cast-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.hide {
  display: none;
}
