import { CommonModule } from '@angular/common';
import {
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  ElementRef,
  inject,
  Input,
  OnDestroy,
  OnInit,
  Renderer2,
  ViewChild
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { CastService } from '../services/cast.service';
import { Subscription } from 'rxjs';

declare var cast: any; // Declare the cast variable to avoid TypeScript errors
declare var window: any; // Declare the cast variable to avoid TypeScript errors

@Component({
  selector: 'app-cast',
  imports: [MatButtonModule, CommonModule],
  templateUrl: './cast.component.html',
  styleUrl: './cast.component.css',
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class CastComponent implements OnInit, OnDestroy {
  @Input() url?: string;
  @ViewChild('castContainer') castContainer?: ElementRef;
  renderer = inject(Renderer2);
  castContext: any; // Will hold the CastContext instance
  castSession: any; // Will hold the current CastSession instance
  castAvailable = false;
  componentInitialized = false;
  castService = inject(CastService);
  sessionStateSubscription?: Subscription;

  ngOnInit() {
    this.sessionStateSubscription = this.castService.sessionStateEventData$.subscribe(
      async sessionStateEventData => {
        if (!sessionStateEventData) return;
        console.log('Cast Session State:', sessionStateEventData);

        switch (sessionStateEventData.sessionState) {
          case cast.framework.SessionState.NO_SESSION:
            break;
          case cast.framework.SessionState.SESSION_STARTING:
            break;
          case cast.framework.SessionState.SESSION_START_FAILED:
            break;
          case cast.framework.SessionState.SESSION_ENDING:
            break;
          case cast.framework.SessionState.SESSION_ENDED:
            break;
          case cast.framework.SessionState.SESSION_STARTED:
          case cast.framework.SessionState.SESSION_RESUMED:
            this.castSession = sessionStateEventData.session;
            await this.castMedia();
            break;
          default:
            console.log(sessionStateEventData);
            // Handle any other unexpected state
            break;
        }
      }
    );
  }

  ngOnChanges() {
    this.castMedia();
  }

  ngOnDestroy(): void {
    if (this.sessionStateSubscription) {
      this.sessionStateSubscription.unsubscribe();
    }
  }
  async castMedia() {
    console.log('casting media: ', this.url);
    if (!this.castSession) {
      console.log('No Cast session available.');
      return;
    }
    if (!this.url) {
      console.log('No url available.');
      return;
    }
    try {
      const mediaStatus = await this.castSession.sendMessage(
        'urn:x-cast:es.offd.dashcast',
        {
          url: this.url,
          force: false,
          reload: false,
          reload_time: 0
        }
      );
      console.log('Load request sent successfully', mediaStatus);
    } catch (error) {
      console.error('Error sending load request:', error);
    }
  }

  shouldShowCast() {
    const cs = this.castService.getCastContext()?.getCastState();
    if (
      cs == undefined ||
      cs == cast.framework.CastState.NO_DEVICES_AVAILABLE
    ) {
      return false;
    }
    return true;
  }
}
