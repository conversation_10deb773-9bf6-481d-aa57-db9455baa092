import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  HostListener,
  ViewChild,
  ElementRef,
  AfterViewChecked
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import {
  FormsModule,
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  FormArray,
  Validators
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { LruCacheService } from '../services/lru-cache.service';
import {
  CustomRegenerateDialogComponent,
  CustomRegenerateDialogData,
  CustomRegenerateDialogResult
} from './custom-regenerate-dialog/custom-regenerate-dialog.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { moveItemInArray } from '@angular/cdk/drag-drop';
import { Subscription, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { GameService } from '../services/game.service';
import { AuthService } from '../services/auth.service';
import { Room, Round, Category, Clue } from '../../../functions/src/resources';

@Component({
  selector: 'app-question-manager',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatTabsModule,
    MatExpansionModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule,
    MatTooltipModule,
    MatChipsModule
  ],
  templateUrl: './question-manager.component.html',
  styleUrls: ['./question-manager.component.css']
})
export class QuestionManagerComponent
  implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('previewWrapper') previewWrapper!: ElementRef<HTMLDivElement>;
  @ViewChild('previewContent') previewContent!: ElementRef<HTMLDivElement>;

  // Properties to hold the preview HTML for stable template binding
  readOnlyPreviewHtml!: SafeHtml;
  livePreviewHtml!: SafeHtml;

  roomId: string = '';
  room: Room | null = null;
  round: Round | null = null;
  loading = true;
  saving = false;
  regenerating = false;
  regeneratingQuestionKey: string | null = null;
  isAdmin = false;

  questionForm: FormGroup;
  selectedCategoryIndex = 0;
  selectedQuestionIndex = 0;
  editingQuestion: { categoryIdx: number; clueIdx: number } | null = null;

  private localChanges: Map<string, Clue> = new Map();
  private hasUnsavedChanges = false;
  private hasReorderingChanges = false;

  private subscription: Subscription = new Subscription();
  private lastMeasuredHeight = 0;
  private destroy$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private gameService: GameService,
    private authService: AuthService,
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private sanitizer: DomSanitizer,
    private lruCacheService: LruCacheService
  ) {
    this.questionForm = this.fb.group({
      categories: this.fb.array([])
    });
  }

  ngOnInit() {
    this.roomId = this.route.snapshot.paramMap.get('roomId') || '';
    this.loadRoom();
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngAfterViewChecked(): void {
    this.resizePreview();
  }

  // --- NEW: The missing method is now defined ---
  private updatePreviewHtml(): void {
    // Logic for the read-only view
    const readOnlyHtmlString = this.getCurrentQuestion()?.questionHTML || '';
    this.readOnlyPreviewHtml = this.sanitizer.bypassSecurityTrustHtml(
      readOnlyHtmlString
    );

    // Logic for the live-editing preview
    if (
      this.isQuestionEditing(
        this.selectedCategoryIndex,
        this.selectedQuestionIndex
      )
    ) {
      const clueFormGroup = this.getClueFormGroup(
        this.selectedCategoryIndex,
        this.selectedQuestionIndex
      );
      const questionControl = clueFormGroup.get('questionHTML');
      const currentValue = questionControl?.value || '';
      if (!currentValue.trim()) {
        this.livePreviewHtml = this.sanitizer.bypassSecurityTrustHtml(
          '<em style="color: #666;">Start typing to see live preview...</em>'
        );
      } else {
        this.livePreviewHtml = this.sanitizer.bypassSecurityTrustHtml(
          currentValue
        );
      }
    } else {
      // When not editing, the live preview shows the same as the read-only view
      this.livePreviewHtml = this.readOnlyPreviewHtml;
    }
  }

  @HostListener('window:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    if (this.editingQuestion) return;
    switch (event.key) {
      case 'ArrowUp':
        event.preventDefault();
        this.selectPreviousQuestion();
        break;
      case 'ArrowDown':
        event.preventDefault();
        this.selectNextQuestion();
        break;
      case 'ArrowLeft':
        event.preventDefault();
        if (this.selectedCategoryIndex > 0) {
          this.selectCategory(this.selectedCategoryIndex - 1);
        }
        break;
      case 'ArrowRight':
        event.preventDefault();
        if (this.selectedCategoryIndex < this.round!.categories.length - 1) {
          this.selectCategory(this.selectedCategoryIndex + 1);
        }
        break;
      case 'Enter':
        event.preventDefault();
        this.startEditing(
          this.selectedCategoryIndex,
          this.selectedQuestionIndex
        );
        break;
      case 'Escape':
        event.preventDefault();
        if (this.editingQuestion) {
          this.cancelEditing();
        }
        break;
    }
  }

  async loadRoom() {
    try {
      this.loading = true;
      this.subscription.add(
        this.gameService.subscribeToRoom(this.roomId).subscribe(room => {
          if (room) {
            this.room = room;
            this.isAdmin = this.authService.getUser()?.uid === room.host;
            if (!this.isAdmin) {
              this.router.navigate(['/room', this.roomId]);
              return;
            }
            this.loadQuestions();
          }
        })
      );
    } catch (error) {
      console.error('Error loading room:', error);
      this.snackBar.open('Error loading room', 'Close', { duration: 3000 });
    }
  }

  async loadQuestions() {
    try {
      const roundData = await this.gameService.getRoundData(this.roomId, 0);
      if (roundData) {
        this.round = roundData;
        this.initializeForm();
        this.updatePreviewHtml();
      }
    } catch (error) {
      console.error('Error loading questions:', error);
      this.snackBar.open('Error loading questions', 'Close', {
        duration: 3000
      });
    } finally {
      this.loading = false;
    }
  }

  initializeForm() {
    if (!this.round) return;
    const categoriesArray = this.fb.array(
      this.round.categories.map(category =>
        this.createCategoryFormGroup(category)
      )
    );
    this.questionForm.setControl('categories', categoriesArray);
  }

  createCategoryFormGroup(category: Category): FormGroup {
    return this.fb.group({
      categoryTitle: [category.categoryTitle],
      categoryDescription: [category.categoryDescription || ''],
      clues: this.fb.array(
        category.clues.map(clue => this.createClueFormGroup(clue))
      )
    });
  }

  createClueFormGroup(clue: Clue): FormGroup {
    return this.fb.group({
      questionHTML: [
        clue.questionHTML,
        [Validators.required, Validators.minLength(10)]
      ],
      hint: [clue.hint],
      answer: [clue.answer, [Validators.required, Validators.minLength(1)]],
      detailedFactsAboutAnswer: this.fb.array(
        clue.detailedFactsAboutAnswer.map(fact => this.fb.control(fact))
      )
    });
  }

  get categoriesFormArray(): FormArray {
    return this.questionForm.get('categories') as FormArray;
  }

  getCategoryFormGroup(categoryIndex: number): FormGroup {
    return this.categoriesFormArray.at(categoryIndex) as FormGroup;
  }

  getCluesFormArray(categoryIndex: number): FormArray {
    return this.getCategoryFormGroup(categoryIndex).get('clues') as FormArray;
  }

  getClueFormGroup(categoryIndex: number, clueIndex: number): FormGroup {
    return this.getCluesFormArray(categoryIndex).at(clueIndex) as FormGroup;
  }

  selectCategory(index: number) {
    this.selectedCategoryIndex = index;
    this.selectedQuestionIndex = 0;
    this.editingQuestion = null;
    this.updatePreviewHtml();
  }

  selectQuestion(categoryIdx: number, clueIdx: number) {
    this.selectedCategoryIndex = categoryIdx;
    this.selectedQuestionIndex = clueIdx;
    this.editingQuestion = null;
    this.updatePreviewHtml();
  }

  startEditing(categoryIdx: number, clueIdx: number) {
    this.editingQuestion = { categoryIdx, clueIdx };
    this.updatePreviewHtml();
    const clueFormGroup = this.getClueFormGroup(categoryIdx, clueIdx);
    clueFormGroup
      .get('questionHTML')
      ?.valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updatePreviewHtml();
      });
  }

  cancelEditing() {
    this.editingQuestion = null;
    this.initializeForm();
    this.updatePreviewHtml();
  }

  async saveQuestion(categoryIdx: number, clueIdx: number) {
    try {
      this.saving = true;
      const clueFormGroup = this.getClueFormGroup(categoryIdx, clueIdx);
      if (clueFormGroup.invalid) {
        clueFormGroup.markAllAsTouched();
        this.snackBar.open(
          'Please fix validation errors before saving',
          'Close',
          { duration: 3000 }
        );
        return;
      }
      const updatedClue = clueFormGroup.value;
      const clueKey = `${categoryIdx}-${clueIdx}`;
      const currentClue = this.round!.categories[categoryIdx].clues[clueIdx];
      const newClue: Clue = {
        ...currentClue,
        questionHTML: updatedClue.questionHTML,
        hint: updatedClue.hint,
        answer: updatedClue.answer,
        detailedFactsAboutAnswer: updatedClue.detailedFactsAboutAnswer || []
      };
      if (this.round) {
        this.round.categories[categoryIdx].clues[clueIdx] = newClue;
      }
      this.localChanges.set(clueKey, newClue);
      this.hasUnsavedChanges = true;
      await this.gameService.call('updateQuestion', {
        roomId: this.roomId,
        roundIdx: 0,
        categoryIdx,
        clueIdx,
        updatedClue: {
          questionHTML: newClue.questionHTML,
          hint: newClue.hint,
          answer: newClue.answer,
          detailedFactsAboutAnswer: newClue.detailedFactsAboutAnswer,
          value: newClue.value
        }
      });
      this.localChanges.delete(clueKey);
      if (this.localChanges.size === 0 && !this.hasReorderingChanges) {
        this.hasUnsavedChanges = false;
      }
      this.editingQuestion = null;
      this.updatePreviewHtml();
      this.snackBar.open('Question published successfully', 'Close', {
        duration: 2000
      });
    } catch (error) {
      console.error('Error saving question:', error);
      this.snackBar.open('Error publishing question', 'Close', {
        duration: 3000
      });
      const clueKey = `${categoryIdx}-${clueIdx}`;
      this.localChanges.delete(clueKey);
      await this.loadQuestions();
    } finally {
      this.saving = false;
    }
  }

  async regenerateQuestion(categoryIdx: number, clueIdx: number) {
    try {
      this.regenerating = true;
      this.regeneratingQuestionKey = `${categoryIdx}-${clueIdx}`;
      const category = this.round?.categories[categoryIdx];
      const clue = category?.clues[clueIdx];
      if (!category || !clue) return;
      await this.gameService.call('regenerateClue', {
        roomId: this.roomId,
        roundIdx: 0,
        categoryIdx,
        clueIdx,
        categoryTitle: category.categoryTitle,
        points: clue.value
      });
      await this.loadQuestions();
      this.snackBar.open('Question regenerated successfully', 'Close', {
        duration: 3000
      });
    } catch (error) {
      console.error('Error regenerating question:', error);
      this.snackBar.open('Error regenerating question', 'Close', {
        duration: 3000
      });
    } finally {
      this.regenerating = false;
      this.regeneratingQuestionKey = null;
    }
  }

  async regenerateCategory(categoryIdx: number) {
    if (!this.round || !this.round.categories[categoryIdx]) return;
    const category = this.round.categories[categoryIdx];
    const confirmed = confirm(
      `Are you sure you want to regenerate all ${category.clues.length} questions in "${category.categoryTitle}"? This cannot be undone.`
    );
    if (!confirmed) return;
    try {
      this.saving = true;
      const promises = category.clues.map((clue, clueIdx) =>
        this.gameService.call('regenerateClue', {
          roomId: this.roomId,
          roundIdx: 0,
          categoryIdx,
          clueIdx,
          categoryTitle: category.categoryTitle,
          points: clue.value
        })
      );
      await Promise.all(promises);
      await this.loadQuestions();
      this.snackBar.open(
        `All questions in "${category.categoryTitle}" regenerated successfully`,
        'Close',
        { duration: 3000 }
      );
    } catch (error) {
      console.error('Error regenerating category:', error);
      this.snackBar.open('Error regenerating category questions', 'Close', {
        duration: 3000
      });
    } finally {
      this.saving = false;
    }
  }

  async regenerateAllQuestions() {
    if (!this.round) return;
    const totalQuestions = this.getTotalQuestions();
    const confirmed = confirm(
      `Are you sure you want to regenerate ALL ${totalQuestions} questions in the game? This cannot be undone and may take several minutes.`
    );
    if (!confirmed) return;
    try {
      this.saving = true;
      const promises: Promise<any>[] = [];
      this.round.categories.forEach((category, categoryIdx) => {
        category.clues.forEach((clue, clueIdx) => {
          promises.push(
            this.gameService.call('regenerateClue', {
              roomId: this.roomId,
              roundIdx: 0,
              categoryIdx,
              clueIdx,
              categoryTitle: category.categoryTitle,
              points: clue.value
            })
          );
        });
      });
      await Promise.all(promises);
      await this.loadQuestions();
      this.snackBar.open(
        `All ${totalQuestions} questions regenerated successfully`,
        'Close',
        { duration: 4000 }
      );
    } catch (error) {
      console.error('Error regenerating all questions:', error);
      this.snackBar.open('Error regenerating questions', 'Close', {
        duration: 3000
      });
    } finally {
      this.saving = false;
    }
  }

  moveQuestionLeft(categoryIdx: number, questionIdx: number) {
    if (!this.round || questionIdx === 0) return;
    this.moveQuestion(categoryIdx, questionIdx, questionIdx - 1);
  }

  moveQuestionRight(categoryIdx: number, questionIdx: number) {
    if (!this.round) return;
    const category = this.round.categories[categoryIdx];
    if (questionIdx === category.clues.length - 1) return;
    this.moveQuestion(categoryIdx, questionIdx, questionIdx + 1);
  }

  private moveQuestion(
    categoryIdx: number,
    fromIndex: number,
    toIndex: number
  ) {
    if (!this.round) return;
    const category = this.round.categories[categoryIdx];
    const clues = [...category.clues];
    moveItemInArray(clues, fromIndex, toIndex);
    const pointValues = [100, 200, 300, 400, 500];
    clues.forEach((clue, index) => {
      if (index < pointValues.length) {
        clue.value = pointValues[index];
      } else {
        clue.value = (index + 1) * 100;
      }
    });
    this.round.categories[categoryIdx].clues = clues;
    this.hasUnsavedChanges = true;
    this.hasReorderingChanges = true;
    this.initializeForm();
    if (fromIndex === this.selectedQuestionIndex) {
      this.selectedQuestionIndex = toIndex;
    } else if (
      fromIndex < this.selectedQuestionIndex &&
      toIndex >= this.selectedQuestionIndex
    ) {
      this.selectedQuestionIndex--;
    } else if (
      fromIndex > this.selectedQuestionIndex &&
      toIndex <= this.selectedQuestionIndex
    ) {
      this.selectedQuestionIndex++;
    }
    if (
      this.editingQuestion &&
      this.editingQuestion.categoryIdx === categoryIdx &&
      this.editingQuestion.clueIdx === fromIndex
    ) {
      this.editingQuestion.clueIdx = toIndex;
    } else if (
      this.editingQuestion &&
      this.editingQuestion.categoryIdx === categoryIdx
    ) {
      if (
        fromIndex < this.editingQuestion.clueIdx &&
        toIndex >= this.editingQuestion.clueIdx
      ) {
        this.editingQuestion.clueIdx--;
      } else if (
        fromIndex > this.editingQuestion.clueIdx &&
        toIndex <= this.editingQuestion.clueIdx
      ) {
        this.editingQuestion.clueIdx++;
      }
    }
    this.snackBar.open(
      'Question moved. Click "Publish All" to save changes.',
      'Close',
      { duration: 2000 }
    );
  }

  goBackToWaitingRoom() {
    this.router.navigate(['/room', this.roomId]);
  }

  isQuestionEditing(categoryIdx: number, clueIdx: number): boolean {
    return (
      this.editingQuestion?.categoryIdx === categoryIdx &&
      this.editingQuestion?.clueIdx === clueIdx
    );
  }

  getCurrentQuestion(): Clue | null {
    if (!this.round || !this.round.categories[this.selectedCategoryIndex]) {
      return null;
    }
    return (
      this.round.categories[this.selectedCategoryIndex].clues[
        this.selectedQuestionIndex
      ] || null
    );
  }

  getCurrentCategory(): Category | null {
    if (!this.round || !this.round.categories[this.selectedCategoryIndex]) {
      return null;
    }
    return this.round.categories[this.selectedCategoryIndex];
  }

  selectNextQuestion() {
    const currentCategory = this.getCurrentCategory();
    if (!currentCategory) return;
    if (this.selectedQuestionIndex < currentCategory.clues.length - 1) {
      this.selectedQuestionIndex++;
    } else if (this.selectedCategoryIndex < this.round!.categories.length - 1) {
      this.selectedCategoryIndex++;
      this.selectedQuestionIndex = 0;
    }
    this.editingQuestion = null;
    this.updatePreviewHtml();
  }

  selectPreviousQuestion() {
    if (this.selectedQuestionIndex > 0) {
      this.selectedQuestionIndex--;
    } else if (this.selectedCategoryIndex > 0) {
      this.selectedCategoryIndex--;
      const prevCategory = this.getCurrentCategory();
      if (prevCategory) {
        this.selectedQuestionIndex = prevCategory.clues.length - 1;
      }
    }
    this.editingQuestion = null;
    this.updatePreviewHtml();
  }

  canNavigateNext(): boolean {
    const currentCategory = this.getCurrentCategory();
    if (!currentCategory) return false;
    return (
      this.selectedQuestionIndex < currentCategory.clues.length - 1 ||
      this.selectedCategoryIndex < this.round!.categories.length - 1
    );
  }

  canNavigatePrevious(): boolean {
    return this.selectedQuestionIndex > 0 || this.selectedCategoryIndex > 0;
  }

  getTotalQuestions(): number {
    if (!this.round) return 0;
    return this.round.categories.reduce(
      (total, category) => total + category.clues.length,
      0
    );
  }

  getCurrentQuestionNumber(): number {
    if (!this.round) return 0;
    let questionNumber = 1;
    for (let i = 0; i < this.selectedCategoryIndex; i++) {
      questionNumber += this.round.categories[i].clues.length;
    }
    questionNumber += this.selectedQuestionIndex;
    return questionNumber;
  }

  isFieldInvalid(
    categoryIdx: number,
    clueIdx: number,
    fieldName: string
  ): boolean {
    const clueFormGroup = this.getClueFormGroup(categoryIdx, clueIdx);
    const field = clueFormGroup.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(
    categoryIdx: number,
    clueIdx: number,
    fieldName: string
  ): string {
    const clueFormGroup = this.getClueFormGroup(categoryIdx, clueIdx);
    const field = clueFormGroup.get(fieldName);
    if (!field || !field.errors) return '';
    if (field.errors['required']) {
      return `${fieldName} is required`;
    }
    if (field.errors['minlength']) {
      const requiredLength = field.errors['minlength'].requiredLength;
      return `${fieldName} must be at least ${requiredLength} characters`;
    }
    if (field.errors['min']) {
      const min = field.errors['min'].min;
      return `Value must be at least ${min}`;
    }
    if (field.errors['max']) {
      const max = field.errors['max'].max;
      return `Value must be at most ${max}`;
    }
    return 'Invalid value';
  }

  canSaveQuestion(categoryIdx: number, clueIdx: number): boolean {
    const clueFormGroup = this.getClueFormGroup(categoryIdx, clueIdx);
    return clueFormGroup.valid;
  }

  hasLocalChanges(): boolean {
    return this.hasUnsavedChanges || this.hasReorderingChanges;
  }

  isQuestionRegenerating(categoryIdx: number, clueIdx: number): boolean {
    return (
      this.regenerating &&
      this.regeneratingQuestionKey === `${categoryIdx}-${clueIdx}`
    );
  }

  insertMarkup(type: string) {
    if (!this.editingQuestion) return;
    const clueFormGroup = this.getClueFormGroup(
      this.editingQuestion.categoryIdx,
      this.editingQuestion.clueIdx
    );
    const questionControl = clueFormGroup.get('questionHTML');
    if (!questionControl) return;
    let markup = '';
    switch (type) {
      case 'image':
        markup =
          '<img src="https://example.com/image.jpg" alt="Description" style="max-width: 300px;">';
        break;
      case 'video':
        markup =
          '<video src="https://example.com/video.mp4" controls style="max-width: 300px;"></video>';
        break;
      case 'youtube':
        markup =
          '<iframe src="https://www.youtube.com/embed/VIDEO_ID?start=0&end=30" width="300" height="169" frameborder="0" allowfullscreen></iframe>';
        break;
      case 'audio':
        markup = '<audio src="https://example.com/audio.mp3" controls></audio>';
        break;
      case 'bold':
        markup = '<b>Bold text</b>';
        break;
      case 'italic':
        markup = '<i>Italic text</i>';
        break;
      case 'underline':
        markup = '<u>Underlined text</u>';
        break;
    }
    markup =
      '<div style="width:100%;display:flex;justify-content:center;">\n' +
      markup +
      '\n</div>';
    const currentValue = questionControl.value || '';
    const newValue = currentValue + markup;
    questionControl.setValue(newValue);
    questionControl.markAsDirty();
    this.snackBar.open(
      `${type.charAt(0).toUpperCase() + type.slice(1)} markup inserted`,
      'Close',
      {
        duration: 2000
      }
    );
  }

  isQuestionAnswered(categoryIdx: number, clueIdx: number): boolean {
    if (
      !this.room?.gameState?.gameProgress ||
      this.room.gameState.gameProgress.type !== 'RoomState'
    ) {
      return false;
    }
    const roomState = this.room.gameState.gameProgress as any;
    const roundState = roomState.roundStates?.[0];
    if (!roundState?.categoryStates?.[categoryIdx]?.clueStates?.[clueIdx]) {
      return false;
    }
    const clueState =
      roundState.categoryStates[categoryIdx].clueStates[clueIdx];
    return (
      clueState.answeredByPlayerId != null || clueState.clueComplete === true
    );
  }

  async resetQuestion(categoryIdx: number, clueIdx: number) {
    if (!this.room || !this.isQuestionAnswered(categoryIdx, clueIdx)) {
      return;
    }
    const confirmed = confirm(
      'This will reset the question state and revert any points awarded. Are you sure?'
    );
    if (!confirmed) return;
    try {
      this.saving = true;
      await this.gameService.call('resetQuestion', {
        roomId: this.roomId,
        roundIdx: 0,
        categoryIdx,
        clueIdx
      });
      const clueSelectedCache = this.lruCacheService.getOrCreateCache(
        'clueSelectedCache'
      );
      const clueKey = `${this.roomId}-${categoryIdx}-${clueIdx}`;
      clueSelectedCache.set(clueKey, undefined);
      this.snackBar.open('Question reset successfully', 'Close', {
        duration: 2000
      });
      this.updatePreviewHtml();
    } catch (error) {
      console.error('Error resetting question:', error);
      this.snackBar.open('Error resetting question', 'Close', {
        duration: 3000
      });
    } finally {
      this.saving = false;
    }
  }

  getUnsavedChangesCount(): number {
    let count = this.localChanges.size;
    if (this.hasReorderingChanges) {
      count += 1;
    }
    return count;
  }

  async publishAllChanges() {
    if (!this.hasUnsavedChanges || !this.round) return;
    try {
      this.saving = true;
      const promises: Promise<any>[] = [];
      promises.push(
        this.gameService.call('reorderQuestionsWithAnswerStates', {
          roomId: this.roomId,
          roundIdx: 0,
          reorderedCategories: this.round.categories.map(
            (category, categoryIdx) => ({
              categoryIdx,
              clues: category.clues
            })
          )
        })
      );
      for (const [clueKey, clue] of this.localChanges.entries()) {
        const [categoryIdx, clueIdx] = clueKey.split('-').map(Number);
        promises.push(
          this.gameService.call('updateQuestion', {
            roomId: this.roomId,
            roundIdx: 0,
            categoryIdx,
            clueIdx,
            updatedClue: {
              questionHTML: clue.questionHTML,
              hint: clue.hint,
              answer: clue.answer,
              detailedFactsAboutAnswer: clue.detailedFactsAboutAnswer || []
            }
          })
        );
      }
      await Promise.all(promises);
      this.localChanges.clear();
      this.hasUnsavedChanges = false;
      this.hasReorderingChanges = false;
      this.snackBar.open('All changes published successfully', 'Close', {
        duration: 2000
      });
    } catch (error) {
      console.error('Error publishing all changes:', error);
      this.snackBar.open('Error publishing some changes', 'Close', {
        duration: 3000
      });
    } finally {
      this.saving = false;
    }
  }

  exportQuestions() {
    if (!this.round) return;
    const exportData = {
      version: '1.0',
      exportDate: new Date().toISOString(),
      roomId: this.roomId,
      round: this.round
    };
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `questions-${this.roomId}-${
      new Date().toISOString().split('T')[0]
    }.json`;
    link.click();
    this.snackBar.open('Questions exported successfully', 'Close', {
      duration: 2000
    });
  }

  onImportFile(event: Event) {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) return;
    const file = input.files[0];
    const reader = new FileReader();
    reader.onload = e => {
      try {
        const result = e.target?.result as string;
        const importData = JSON.parse(result);
        if (!importData.round || !importData.round.categories) {
          throw new Error('Invalid file format');
        }
        this.importQuestions(importData.round);
      } catch (error) {
        console.error('Error importing questions:', error);
        this.snackBar.open(
          'Error importing questions: Invalid file format',
          'Close',
          {
            duration: 3000
          }
        );
      }
    };
    reader.readAsText(file);
  }

  private async importQuestions(importedRound: Round) {
    const confirmed = confirm(
      'This will replace all current questions. Are you sure you want to continue?'
    );
    if (!confirmed) return;
    try {
      this.saving = true;
      this.round = importedRound;
      this.initializeForm();
      this.selectedCategoryIndex = 0;
      this.selectedQuestionIndex = 0;
      this.editingQuestion = null;
      this.updatePreviewHtml();
      this.snackBar.open('Questions imported successfully', 'Close', {
        duration: 3000
      });
    } catch (error) {
      console.error('Error importing questions:', error);
      this.snackBar.open('Error importing questions', 'Close', {
        duration: 3000
      });
    } finally {
      this.saving = false;
    }
  }

  validateAllQuestions() {
    if (!this.round) return;
    const errors: string[] = [];
    let totalQuestions = 0;
    this.round.categories.forEach((category, categoryIdx) => {
      if (!category.categoryTitle.trim()) {
        errors.push(`Category ${categoryIdx + 1}: Missing title`);
      }
      category.clues.forEach((clue, clueIdx) => {
        totalQuestions++;
        const questionNum = `Category "${
          category.categoryTitle
        }", Question ${clueIdx + 1}`;
        if (!clue.questionHTML.trim()) {
          errors.push(`${questionNum}: Missing question text`);
        }
        if (!clue.answer.trim()) {
          errors.push(`${questionNum}: Missing answer`);
        }
        if (clue.value < 100 || clue.value > 2000) {
          errors.push(`${questionNum}: Invalid point value (${clue.value})`);
        }
        if (clue.questionHTML.length < 10) {
          errors.push(`${questionNum}: Question text too short`);
        }
      });
    });
    if (errors.length === 0) {
      this.snackBar.open(
        `✅ All ${totalQuestions} questions are valid!`,
        'Close',
        { duration: 3000 }
      );
    } else {
      const errorMessage = `Found ${
        errors.length
      } validation errors:\n\n${errors.slice(0, 5).join('\n')}${
        errors.length > 5 ? `\n... and ${errors.length - 5} more` : ''
      }`;
      alert(errorMessage);
    }
  }

  openCustomRegenerateDialog(categoryIdx: number, clueIdx: number) {
    const category = this.round?.categories[categoryIdx];
    const clue = category?.clues[clueIdx];
    if (!category || !clue) return;
    const dialogData: CustomRegenerateDialogData = {
      categoryTitle: category.categoryTitle,
      questionHTML: clue.questionHTML,
      answer: clue.answer,
      hint: clue.hint,
      pointValue: clue.value
    };
    const dialogRef = this.dialog.open(CustomRegenerateDialogComponent, {
      width: '600px',
      maxWidth: '90vw',
      data: dialogData,
      disableClose: false
    });
    dialogRef
      .afterClosed()
      .subscribe((result: CustomRegenerateDialogResult) => {
        if (result && result.instructions) {
          this.regenerateQuestionWithInstructions(
            categoryIdx,
            clueIdx,
            result.instructions
          );
        }
      });
  }

  async regenerateQuestionWithInstructions(
    categoryIdx: number,
    clueIdx: number,
    instructions: string
  ) {
    try {
      this.regenerating = true;
      this.regeneratingQuestionKey = `${categoryIdx}-${clueIdx}`;
      const category = this.round?.categories[categoryIdx];
      const clue = category?.clues[clueIdx];
      if (!category || !clue) return;
      await this.gameService.call('regenerateClueWithInstructions', {
        roomId: this.roomId,
        roundIdx: 0,
        categoryIdx,
        clueIdx,
        categoryTitle: category.categoryTitle,
        points: clue.value,
        currentQuestion: clue.questionHTML,
        currentAnswer: clue.answer,
        instructions: instructions,
        llmAgent: 'gemini-1.5-flash'
      });
      await this.loadQuestions();
      this.snackBar.open(
        'Question regenerated with your instructions!',
        'Close',
        {
          duration: 3000
        }
      );
    } catch (error) {
      console.error('Error regenerating question with instructions:', error);
      this.snackBar.open(
        'Error regenerating question. Please try again.',
        'Close',
        {
          duration: 3000
        }
      );
    } finally {
      this.regenerating = false;
      this.regeneratingQuestionKey = null;
    }
  }

  private resizePreview(): void {
    if (!this.previewWrapper || !this.previewContent) {
      return;
    }

    const contentEl = this.previewContent.nativeElement;
    const wrapperEl = this.previewWrapper.nativeElement;
    const contentNaturalHeight = contentEl.scrollHeight;

    if (this.lastMeasuredHeight !== contentNaturalHeight) {
      this.lastMeasuredHeight = contentNaturalHeight;
      const scaleFactor = 0.5;
      setTimeout(() => {
        wrapperEl.style.height = contentNaturalHeight * scaleFactor + 'px';
      }, 0);
    }
  }
}
