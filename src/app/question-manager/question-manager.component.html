<div class="question-manager-container">
  <!-- Header -->
  <div class="header-section">
    <div class="header-content">
      <button
        mat-icon-button
        (click)="goBackToWaitingRoom()"
        class="back-button"
      >
        <mat-icon>arrow_back</mat-icon>
      </button>
      <h1 class="page-title">
        Question Manager
        <span class="unsaved-indicator" *ngIf="hasLocalChanges()">
          ({{ getUnsavedChangesCount() }} unsaved)
        </span>
      </h1>
      <div class="header-actions">
        <button
          mat-raised-button
          color="accent"
          (click)="publishAllChanges()"
          [disabled]="saving || !hasLocalChanges()"
          class="publish-all-button"
          *ngIf="hasLocalChanges()"
        >
          <mat-icon>publish</mat-icon>
          Publish All ({{ getUnsavedChangesCount() }})
        </button>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-progress-spinner mode="indeterminate"></mat-progress-spinner>
    <p>Loading questions...</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!loading && round" class="main-content">
    <div class="content-layout">
      <!-- Left Sidebar - Category Navigation -->
      <div class="sidebar">
        <mat-card class="categories-card">
          <mat-card-header>
            <mat-card-title>Categories</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="category-list">
              <div
                *ngFor="let category of round.categories; let i = index"
                class="category-item"
                [class.selected]="selectedCategoryIndex === i"
                (click)="selectCategory(i)"
              >
                <div class="category-info">
                  <div class="category-title">{{ category.categoryTitle }}</div>
                  <div class="category-meta">
                    {{ category.clues.length }} questions
                  </div>
                </div>
                <mat-icon class="category-arrow">chevron_right</mat-icon>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Quick Navigation -->
        <mat-card class="navigation-card">
          <mat-card-header>
            <mat-card-title>Quick Navigation</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="navigation-buttons">
              <button
                mat-stroked-button
                (click)="selectPreviousQuestion()"
                [disabled]="!canNavigatePrevious()"
                matTooltip="Previous question"
              >
                <mat-icon>keyboard_arrow_up</mat-icon>
                Previous
              </button>
              <button
                mat-stroked-button
                (click)="selectNextQuestion()"
                [disabled]="!canNavigateNext()"
                matTooltip="Next question"
              >
                <mat-icon>keyboard_arrow_down</mat-icon>
                Next
              </button>
            </div>
            <div class="question-counter">
              Question {{ getCurrentQuestionNumber() }} of
              {{ getTotalQuestions() }}
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Bulk Actions -->
        <mat-card class="bulk-actions-card">
          <mat-card-header>
            <mat-card-title>Bulk Actions</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="bulk-buttons">
              <button
                mat-stroked-button
                color="accent"
                (click)="regenerateCategory(selectedCategoryIndex)"
                [disabled]="saving"
                matTooltip="Regenerate all questions in current category"
              >
                <mat-icon>refresh</mat-icon>
                Regenerate Category
              </button>
              <button
                mat-stroked-button
                color="warn"
                (click)="regenerateAllQuestions()"
                [disabled]="saving"
                matTooltip="Regenerate all questions in the game"
              >
                <mat-icon>autorenew</mat-icon>
                Regenerate All
              </button>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Import/Export -->
        <mat-card class="import-export-card">
          <mat-card-header>
            <mat-card-title>Import/Export</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="import-export-buttons">
              <input
                type="file"
                #importInput
                accept=".json"
                (change)="onImportFile($event)"
                style="display: none"
              />
              <button
                mat-stroked-button
                (click)="importInput.click()"
                [disabled]="saving"
                matTooltip="Import questions from JSON file"
              >
                <mat-icon>upload</mat-icon>
                Import
              </button>
              <button
                mat-stroked-button
                (click)="exportQuestions()"
                [disabled]="saving"
                matTooltip="Export questions to JSON file"
              >
                <mat-icon>download</mat-icon>
                Export
              </button>
              <button
                mat-stroked-button
                color="primary"
                (click)="validateAllQuestions()"
                [disabled]="saving"
                matTooltip="Validate all questions for errors"
              >
                <mat-icon>check_circle</mat-icon>
                Validate
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Center - Compact Question Bar -->
      <div class="question-bar-section">
        <div class="question-bar-header">
          <h2>{{ getCurrentCategory()?.categoryTitle || "Questions" }}</h2>
        </div>
        <div class="question-bar">
          <div
            *ngFor="let clue of getCurrentCategory()?.clues; let j = index"
            class="question-chip"
            [class.selected]="selectedQuestionIndex === j"
            (click)="selectQuestion(selectedCategoryIndex, j)"
          >
            <div class="question-chip-content">
              <div class="question-value">${{ clue.value }}</div>
              <div class="question-answer">({{ clue.answer }})</div>
            </div>

            <!-- Regenerating indicator -->
            <div
              class="regenerating-indicator"
              *ngIf="isQuestionRegenerating(selectedCategoryIndex, j)"
            >
              <mat-spinner diameter="16"></mat-spinner>
            </div>

            <div class="question-controls" *ngIf="selectedQuestionIndex === j">
              <button
                mat-icon-button
                size="small"
                (click)="
                  moveQuestionLeft(selectedCategoryIndex, j);
                  $event.stopPropagation()
                "
                [disabled]="j === 0"
                matTooltip="Move left"
              >
                <mat-icon>chevron_left</mat-icon>
              </button>
              <button
                mat-icon-button
                size="small"
                (click)="
                  moveQuestionRight(selectedCategoryIndex, j);
                  $event.stopPropagation()
                "
                [disabled]="j === getCurrentCategory()!.clues.length - 1"
                matTooltip="Move right"
              >
                <mat-icon>chevron_right</mat-icon>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel - Question Editor -->
      <div class="editor-section">
        <mat-card class="editor-card" *ngIf="getCurrentQuestion()">
          <mat-card-header>
            <mat-card-title>Question Editor</mat-card-title>
            <mat-card-subtitle>
              {{ getCurrentCategory()?.categoryTitle }} - ${{
                getCurrentQuestion()?.value
              }}
            </mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <form
              [formGroup]="questionForm"
              *ngIf="
                !isQuestionEditing(selectedCategoryIndex, selectedQuestionIndex)
              "
            >
              <!-- Read-only view -->
              <div class="question-display">
                <div class="field-group">
                  <label>Question:</label>
                  <div
                    class="field-content"
                    [innerHTML]="getCurrentQuestion()?.questionHTML"
                  ></div>
                </div>

                <div class="field-group">
                  <label>Answer:</label>
                  <div class="field-content">
                    {{ getCurrentQuestion()?.answer }}
                  </div>
                </div>

                <div class="field-group" *ngIf="getCurrentQuestion()?.hint">
                  <label>Hint:</label>
                  <div class="field-content">
                    {{ getCurrentQuestion()?.hint }}
                  </div>
                </div>

                <div
                  class="field-group"
                  *ngIf="getCurrentQuestion()?.detailedFactsAboutAnswer?.length"
                >
                  <label>Facts:</label>
                  <ul class="facts-list">
                    <li
                      *ngFor="
                        let fact of getCurrentQuestion()
                          ?.detailedFactsAboutAnswer
                      "
                    >
                      <span>{{ fact }}</span>
                    </li>
                  </ul>
                </div>

                <div class="editor-actions">
                  <button
                    mat-raised-button
                    color="primary"
                    (click)="
                      startEditing(selectedCategoryIndex, selectedQuestionIndex)
                    "
                  >
                    <mat-icon>edit</mat-icon>
                    Edit Question
                  </button>
                  <button
                    mat-raised-button
                    color="accent"
                    (click)="
                      regenerateQuestion(
                        selectedCategoryIndex,
                        selectedQuestionIndex
                      )
                    "
                    [disabled]="
                      saving ||
                      isQuestionRegenerating(
                        selectedCategoryIndex,
                        selectedQuestionIndex
                      )
                    "
                  >
                    <mat-spinner
                      *ngIf="
                        isQuestionRegenerating(
                          selectedCategoryIndex,
                          selectedQuestionIndex
                        )
                      "
                      diameter="16"
                    >
                    </mat-spinner>
                    <mat-icon
                      *ngIf="
                        !isQuestionRegenerating(
                          selectedCategoryIndex,
                          selectedQuestionIndex
                        )
                      "
                      >refresh</mat-icon
                    >
                    {{
                      isQuestionRegenerating(
                        selectedCategoryIndex,
                        selectedQuestionIndex
                      )
                        ? "Regenerating..."
                        : "Regenerate"
                    }}
                  </button>
                  <button
                    mat-raised-button
                    color="warn"
                    (click)="
                      openCustomRegenerateDialog(
                        selectedCategoryIndex,
                        selectedQuestionIndex
                      )
                    "
                    [disabled]="
                      saving ||
                      isQuestionRegenerating(
                        selectedCategoryIndex,
                        selectedQuestionIndex
                      )
                    "
                  >
                    <mat-spinner
                      *ngIf="
                        isQuestionRegenerating(
                          selectedCategoryIndex,
                          selectedQuestionIndex
                        )
                      "
                      diameter="16"
                    >
                    </mat-spinner>
                    <mat-icon
                      *ngIf="
                        !isQuestionRegenerating(
                          selectedCategoryIndex,
                          selectedQuestionIndex
                        )
                      "
                      >auto_fix_high</mat-icon
                    >
                    {{
                      isQuestionRegenerating(
                        selectedCategoryIndex,
                        selectedQuestionIndex
                      )
                        ? "Regenerating..."
                        : "Custom Regenerate"
                    }}
                  </button>
                </div>
              </div>
            </form>

            <!-- Edit mode -->
            <form
              [formGroup]="
                getClueFormGroup(selectedCategoryIndex, selectedQuestionIndex)
              "
              *ngIf="
                isQuestionEditing(selectedCategoryIndex, selectedQuestionIndex)
              "
              class="edit-form"
            >
              <!-- Question Field with Markup Toolbar -->
              <div class="question-field-container">
                <mat-label class="field-label">Question (HTML)</mat-label>

                <!-- Markup Toolbar -->
                <div class="markup-toolbar">
                  <button
                    mat-icon-button
                    type="button"
                    (click)="insertMarkup('bold')"
                    matTooltip="Bold text"
                    class="toolbar-button"
                  >
                    <mat-icon>format_bold</mat-icon>
                  </button>
                  <button
                    mat-icon-button
                    type="button"
                    (click)="insertMarkup('italic')"
                    matTooltip="Italic text"
                    class="toolbar-button"
                  >
                    <mat-icon>format_italic</mat-icon>
                  </button>
                  <button
                    mat-icon-button
                    type="button"
                    (click)="insertMarkup('underline')"
                    matTooltip="Underlined text"
                    class="toolbar-button"
                  >
                    <mat-icon>format_underlined</mat-icon>
                  </button>
                  <div class="toolbar-divider"></div>
                  <button
                    mat-icon-button
                    type="button"
                    (click)="insertMarkup('image')"
                    matTooltip="Insert image"
                    class="toolbar-button"
                  >
                    <mat-icon>image</mat-icon>
                  </button>
                  <button
                    mat-icon-button
                    type="button"
                    (click)="insertMarkup('video')"
                    matTooltip="Insert video"
                    class="toolbar-button"
                  >
                    <mat-icon>videocam</mat-icon>
                  </button>
                  <button
                    mat-icon-button
                    type="button"
                    (click)="insertMarkup('audio')"
                    matTooltip="Insert audio"
                    class="toolbar-button"
                  >
                    <mat-icon>audiotrack</mat-icon>
                  </button>
                </div>

                <mat-form-field class="full-width">
                  <textarea
                    matInput
                    formControlName="questionHTML"
                    rows="10"
                    placeholder="Enter the question text (HTML supported)"
                  >
                  </textarea>
                  <mat-error
                    *ngIf="
                      isFieldInvalid(
                        selectedCategoryIndex,
                        selectedQuestionIndex,
                        'questionHTML'
                      )
                    "
                  >
                    {{
                      getFieldError(
                        selectedCategoryIndex,
                        selectedQuestionIndex,
                        "questionHTML"
                      )
                    }}
                  </mat-error>
                </mat-form-field>

                <!-- Live Preview -->
                <div class="live-preview-section">
                  <div class="preview-header">
                    <mat-icon>visibility</mat-icon>
                    <span>Live Preview</span>
                  </div>
                  <div
                    class="preview-content"
                    [innerHTML]="
                      getQuestionPreview(
                        selectedCategoryIndex,
                        selectedQuestionIndex
                      )
                    "
                  ></div>
                </div>
              </div>

              <mat-form-field class="full-width">
                <mat-label>Answer</mat-label>
                <input
                  matInput
                  formControlName="answer"
                  placeholder="Enter the correct answer"
                />
                <mat-error
                  *ngIf="
                    isFieldInvalid(
                      selectedCategoryIndex,
                      selectedQuestionIndex,
                      'answer'
                    )
                  "
                >
                  {{
                    getFieldError(
                      selectedCategoryIndex,
                      selectedQuestionIndex,
                      "answer"
                    )
                  }}
                </mat-error>
              </mat-form-field>

              <mat-form-field class="full-width">
                <mat-label>Hint</mat-label>
                <input
                  matInput
                  formControlName="hint"
                  placeholder="Enter a hint (optional)"
                />
              </mat-form-field>

              <div class="edit-actions">
                <button
                  mat-raised-button
                  color="primary"
                  (click)="
                    saveQuestion(selectedCategoryIndex, selectedQuestionIndex)
                  "
                  [disabled]="
                    saving ||
                    !canSaveQuestion(
                      selectedCategoryIndex,
                      selectedQuestionIndex
                    )
                  "
                >
                  <mat-icon>publish</mat-icon>
                  Publish Changes
                </button>
                <button mat-button (click)="cancelEditing()">
                  Cancel
                </button>
              </div>
            </form>
          </mat-card-content>
        </mat-card>

        <!-- Placeholder when no question selected -->
        <mat-card class="editor-card" *ngIf="!getCurrentQuestion()">
          <mat-card-content>
            <div class="no-selection">
              <mat-icon>quiz</mat-icon>
              <p>Select a question to view or edit</p>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="!loading && !round" class="error-container">
    <mat-icon>error</mat-icon>
    <p>No questions found. Please generate questions first.</p>
    <button mat-raised-button color="primary" (click)="goBackToWaitingRoom()">
      Go Back to Waiting Room
    </button>
  </div>
</div>
