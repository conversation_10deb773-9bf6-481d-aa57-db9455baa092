import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';

export interface CustomRegenerateDialogData {
  categoryTitle: string;
  questionHTML: string;
  answer: string;
  hint?: string;
  pointValue: number;
}

export interface CustomRegenerateDialogResult {
  instructions: string;
}

@Component({
  selector: 'app-custom-regenerate-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatChipsModule
  ],
  templateUrl: './custom-regenerate-dialog.component.html',
  styleUrl: './custom-regenerate-dialog.component.css'
})
export class CustomRegenerateDialogComponent {
  instructions = '';
  
  // Common instruction templates
  instructionTemplates = [
    'Make this question more challenging',
    'Make this question clearer and easier to understand',
    'Focus on a different aspect of this topic',
    'Add more visual or multimedia elements',
    'Make it more engaging and fun',
    'The question is too obscure, make it more mainstream',
    'The answer is too obvious, make it require more thinking'
  ];

  constructor(
    public dialogRef: MatDialogRef<CustomRegenerateDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: CustomRegenerateDialogData
  ) {}

  onCancel(): void {
    this.dialogRef.close();
  }

  onSubmit(): void {
    if (this.instructions.trim()) {
      this.dialogRef.close({ instructions: this.instructions.trim() });
    }
  }

  useTemplate(template: string): void {
    this.instructions = template;
  }

  addTemplate(template: string): void {
    if (this.instructions.trim()) {
      this.instructions += '. ' + template;
    } else {
      this.instructions = template;
    }
  }

  // Strip HTML tags for display
  getCleanQuestionText(): string {
    return this.data.questionHTML.replace(/<[^>]*>/g, '');
  }
}
