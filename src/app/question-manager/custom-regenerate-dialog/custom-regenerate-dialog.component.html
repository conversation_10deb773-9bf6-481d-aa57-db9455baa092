<div class="dialog-container">
  <h2 mat-dialog-title>
    <mat-icon>auto_fix_high</mat-icon>
    Custom Regenerate Question
  </h2>

  <mat-dialog-content class="dialog-content">
    <!-- Current Question Display -->
    <div class="current-question-section">
      <h3>Current Question</h3>
      <div class="question-card">
        <div class="question-header">
          <span class="category">{{ data.categoryTitle }}</span>
          <span class="points">${{ data.pointValue }}</span>
        </div>
        <div class="question-text">{{ getCleanQuestionText() }}</div>
        <div class="answer-text">
          <strong>Answer:</strong> {{ data.answer }}
        </div>
        <div class="hint-text" *ngIf="data.hint">
          <strong>Hint:</strong> {{ data.hint }}
        </div>
      </div>
    </div>

    <!-- Instructions Section -->
    <div class="instructions-section">
      <h3>What would you like to improve?</h3>
      <p class="instructions-help">
        Be specific about what you don't like and how you want the new question to be different.
      </p>

      <!-- Quick Templates -->
      <div class="templates-section">
        <h4>Quick suggestions:</h4>
        <div class="template-chips">
          <mat-chip-set>
            <mat-chip 
              *ngFor="let template of instructionTemplates"
              (click)="useTemplate(template)"
              class="template-chip">
              {{ template }}
            </mat-chip>
          </mat-chip-set>
        </div>
      </div>

      <!-- Custom Instructions Input -->
      <mat-form-field class="full-width instructions-field">
        <mat-label>Your specific instructions</mat-label>
        <textarea
          matInput
          [(ngModel)]="instructions"
          rows="4"
          placeholder="Example: This question is too easy. Make it more challenging by asking about a specific detail or requiring more knowledge about the topic."
          maxlength="500">
        </textarea>
        <mat-hint>{{ instructions.length }}/500 characters</mat-hint>
      </mat-form-field>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions class="dialog-actions">
    <button mat-button (click)="onCancel()">
      Cancel
    </button>
    <button 
      mat-raised-button 
      color="primary" 
      (click)="onSubmit()"
      [disabled]="!instructions.trim()">
      <mat-icon>auto_fix_high</mat-icon>
      Regenerate with Gemini Flash
    </button>
  </mat-dialog-actions>
</div>
