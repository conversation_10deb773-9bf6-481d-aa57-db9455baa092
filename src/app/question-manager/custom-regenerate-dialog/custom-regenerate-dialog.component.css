.dialog-container {
  min-width: 500px;
  max-width: 700px;
}

h2[mat-dialog-title] {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1rem 0;
  font: var(--mat-sys-headline-small);
  color: var(--mat-sys-primary);
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-height: 70vh;
  overflow-y: auto;
}

/* Current Question Section */
.current-question-section h3 {
  margin: 0 0 0.75rem 0;
  font: var(--mat-sys-title-medium);
  color: var(--mat-sys-on-surface);
}

.question-card {
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: var(--mat-sys-surface-container-low);
  border: 1px solid var(--mat-sys-outline-variant);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.category {
  font: var(--mat-sys-title-small);
  color: var(--mat-sys-primary);
  font-weight: 500;
}

.points {
  font: var(--mat-sys-title-medium);
  font-weight: bold;
  color: var(--mat-sys-secondary);
  background-color: var(--mat-sys-secondary-container);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.question-text {
  font: var(--mat-sys-body-large);
  line-height: 1.5;
  margin-bottom: 0.75rem;
  color: var(--mat-sys-on-surface);
}

.answer-text,
.hint-text {
  font: var(--mat-sys-body-medium);
  color: var(--mat-sys-on-surface-variant);
  margin-bottom: 0.5rem;
}

.answer-text:last-child,
.hint-text:last-child {
  margin-bottom: 0;
}

/* Instructions Section */
.instructions-section h3 {
  margin: 0 0 0.5rem 0;
  font: var(--mat-sys-title-medium);
  color: var(--mat-sys-on-surface);
}

.instructions-help {
  margin: 0 0 1rem 0;
  font: var(--mat-sys-body-medium);
  color: var(--mat-sys-on-surface-variant);
}

.templates-section {
  margin-bottom: 1rem;
}

.templates-section h4 {
  margin: 0 0 0.5rem 0;
  font: var(--mat-sys-title-small);
  color: var(--mat-sys-on-surface);
}

.template-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.template-chip {
  cursor: pointer;
  transition: all 0.2s ease;
}

.template-chip:hover {
  background-color: var(--mat-sys-primary-container);
  color: var(--mat-sys-on-primary-container);
}

.instructions-field {
  width: 100%;
}

.full-width {
  width: 100%;
}

/* Dialog Actions */
.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 1rem 0 0 0;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 600px) {
  .dialog-container {
    min-width: 90vw;
    max-width: 90vw;
  }
  
  .question-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .template-chips {
    flex-direction: column;
  }
  
  .template-chip {
    width: 100%;
    justify-content: flex-start;
  }
}
