.question-manager-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--mat-sys-surface);
  color: var(--mat-sys-on-surface);
}

/* Header */
.header-section {
  background-color: var(--mat-sys-primary);
  color: var(--mat-sys-on-primary);
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  max-width: 1400px;
  margin: 0 auto;
}

.back-button {
  color: var(--mat-sys-on-primary);
}

.page-title {
  margin: 0;
  flex: 1;
  font: var(--mat-sys-headline-medium);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.unsaved-indicator {
  font: var(--mat-sys-body-medium);
  color: var(--mat-sys-error);
  background-color: var(--mat-sys-error-container);
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.publish-all-button {
  background-color: var(--mat-sys-tertiary);
  color: var(--mat-sys-on-tertiary);
  animation: pulse 2s infinite;
}

.save-all-button {
  background-color: var(--mat-sys-secondary);
  color: var(--mat-sys-on-secondary);
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: 1rem;
  text-align: center;
}

.loading-container mat-icon,
.error-container mat-icon {
  font-size: 3rem;
  width: 3rem;
  height: 3rem;
  color: var(--mat-sys-primary);
}

/* Main Content Layout */
.main-content {
  flex: 1;
  overflow: hidden;
  padding: 1rem;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  grid-template-rows: auto 1fr;
  gap: 1rem;
  height: 100%;
  max-width: 1400px;
  margin: 0 auto;
}

.sidebar {
  grid-row: 1 / -1;
}

.question-bar-section {
  grid-column: 2;
  grid-row: 1;
}

.editor-section {
  grid-column: 2;
  grid-row: 2;
}

/* Sidebar - Categories */
.sidebar {
  overflow-y: auto;
}

.categories-card,
.navigation-card,
.bulk-actions-card,
.import-export-card {
  height: fit-content;
  max-height: 100%;
  margin-bottom: 1rem;
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.category-item:hover {
  background-color: var(--mat-sys-surface-variant);
}

.category-item.selected {
  background-color: var(--mat-sys-primary-container);
  color: var(--mat-sys-on-primary-container);
  border-color: var(--mat-sys-primary);
}

.category-info {
  flex: 1;
}

.category-title {
  font: var(--mat-sys-title-medium);
  margin-bottom: 0.25rem;
}

.category-meta {
  font: var(--mat-sys-body-small);
  color: var(--mat-sys-on-surface-variant);
}

.category-arrow {
  color: var(--mat-sys-on-surface-variant);
}

/* Navigation Card */
.navigation-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.navigation-buttons button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: flex-start;
}

.question-counter {
  text-align: center;
  font: var(--mat-sys-body-medium);
  color: var(--mat-sys-on-surface-variant);
  padding: 0.5rem;
  background-color: var(--mat-sys-surface-container-low);
  border-radius: 0.5rem;
  border: 1px solid var(--mat-sys-outline-variant);
}

/* Bulk Actions Card */
.bulk-buttons,
.import-export-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.bulk-buttons button,
.import-export-buttons button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: flex-start;
}

/* Question Bar */
.question-bar-section {
  margin-bottom: 1rem;
}

.question-bar-header {
  margin-bottom: 0.75rem;
}

.question-bar-header h2 {
  margin: 0;
  font: var(--mat-sys-headline-small);
  color: var(--mat-sys-primary);
}

.question-bar {
  display: flex;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: var(--mat-sys-surface-container-low);
  border-radius: 0.5rem;
  border: 1px solid var(--mat-sys-outline-variant);
  overflow-x: auto;
  min-height: 60px;
}

.question-chip {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 2rem;
  border: 1px solid var(--mat-sys-outline-variant);
  background-color: var(--mat-sys-surface-container);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: fit-content;
}

.question-chip:hover {
  background-color: var(--mat-sys-surface-container-high);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.question-chip.selected {
  background-color: var(--mat-sys-primary-container);
  color: var(--mat-sys-on-primary-container);
  border-color: var(--mat-sys-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.question-chip-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.question-value {
  font: var(--mat-sys-title-small);
  font-weight: bold;
  color: var(--mat-sys-primary);
}

.question-chip.selected .question-value {
  color: var(--mat-sys-on-primary-container);
}

.question-answer {
  font: var(--mat-sys-body-small);
  color: var(--mat-sys-on-surface-variant);
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.question-chip.selected .question-answer {
  color: var(--mat-sys-on-primary-container);
  opacity: 0.8;
}

.drag-handle {
  display: flex;
  align-items: center;
  color: var(--mat-sys-on-surface-variant);
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
}

.question-chip.selected .drag-handle {
  color: var(--mat-sys-on-primary-container);
}

/* Editor Panel */
.editor-section {
  overflow-y: auto;
}

.editor-card {
  height: fit-content;
  max-height: 100%;
}

.question-display {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.field-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-group label {
  font: var(--mat-sys-title-small);
  color: var(--mat-sys-primary);
  font-weight: 500;
}

.field-content {
  font: var(--mat-sys-body-medium);
  padding: 0.75rem;
  background-color: var(--mat-sys-surface-container-low);
  border-radius: 0.5rem;
  border: 1px solid var(--mat-sys-outline-variant);
  line-height: 1.4;
}

.facts-list {
  margin: 0;
  padding-left: 1.5rem;
}

.facts-list li {
  margin-bottom: 0.5rem;
}

.editor-actions,
.edit-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.edit-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.full-width {
  width: 100%;
}

.no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem;
  text-align: center;
  color: var(--mat-sys-on-surface-variant);
}

.no-selection mat-icon {
  font-size: 3rem;
  width: 3rem;
  height: 3rem;
}

/* HTML Help Section */
.html-help-section {
  margin-top: 1rem;
  padding: 1rem;
  border: 1px solid var(--mat-sys-outline-variant);
  border-radius: 0.5rem;
  background-color: var(--mat-sys-surface-container-low);
}

.html-help-section h3 {
  margin: 0 0 1rem 0;
  font: var(--mat-sys-title-medium);
  color: var(--mat-sys-primary);
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.help-content p {
  margin: 0;
  font: var(--mat-sys-body-medium);
  color: var(--mat-sys-on-surface);
}

.help-content code {
  display: block;
  padding: 0.5rem;
  background-color: var(--mat-sys-surface-container);
  border: 1px solid var(--mat-sys-outline-variant);
  border-radius: 0.25rem;
  font-family: "Courier New", monospace;
  font-size: 0.875rem;
  color: var(--mat-sys-on-surface-variant);
  word-break: break-all;
  margin-top: 0.25rem;
}

/* Drag and Drop Styles */
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 0.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.cdk-drag-placeholder {
  opacity: 0.4;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.question-list.cdk-drop-list-dragging
  .question-item:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .content-layout {
    grid-template-columns: 250px 1fr;
  }
}

@media (max-width: 900px) {
  .content-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr;
  }

  .sidebar {
    grid-row: 1;
    max-height: 300px;
    overflow-y: auto;
  }

  .question-bar-section {
    grid-column: 1;
    grid-row: 2;
  }

  .editor-section {
    grid-column: 1;
    grid-row: 3;
  }

  .question-bar {
    overflow-x: auto;
    padding: 0.5rem;
  }

  .question-chip {
    min-width: 100px;
  }
}

@media (max-width: 600px) {
  .header-content {
    padding: 0.5rem;
  }

  .page-title {
    font: var(--mat-sys-title-medium);
  }

  .main-content {
    padding: 0.5rem;
  }
}
