import {
  Component,
  Input,
  <PERSON><PERSON><PERSON><PERSON>,
  inject,
  OnC<PERSON><PERSON>,
  ViewChild,
  ElementRef,
  ChangeDetectorRef
} from '@angular/core';
import { trigger, transition, style, animate } from '@angular/animations';
import { CommonModule } from '@angular/common';
import {
  Clue,
  ClueState,
  GameMode,
  Room,
  Round,
  RoundState,
  RoomState,
  Category
} from '../../../../functions/src/resources';

import { GameService } from '../../services/game.service';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatOption } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
// Animation imports are used in the component decorator
import { TimerChipComponent } from '../../timer-chip/timer-chip.component';
import { MatDividerModule } from '@angular/material/divider';
import { AuthService } from '../../services/auth.service';
import { serverTimestamp } from '@angular/fire/firestore';
import { LRUCache, LruCacheService } from '../../services/lru-cache.service';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TtsService } from '../../services/tts.service';
import { FirebaseFirestore } from '@capacitor-firebase/firestore';
import {
  GameMasterActionsRequest,
  SkipShowAnswerRequest
} from '../../../../functions/src/services';
// No longer need SnarkService as we're using server-side snarky comments
import { SnarkyCommentaryComponent } from '../snarky-commentary/snarky-commentary.component';
import { CountdownTimerComponent } from '../../countdown-timer/countdown-timer.component';
import { Router } from '@angular/router';

@Component({
  selector: 'app-clue',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDividerModule,
    TimerChipComponent,
    MatButtonModule,
    MatFormFieldModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
    MatInputModule,
    MatOption,
    MatSelectModule,
    MatIconModule,
    MatTooltipModule,
    SnarkyCommentaryComponent,
    CountdownTimerComponent
  ],
  templateUrl: './clue.component.html',
  styleUrls: ['./clue.component.scss'],
  animations: [
    trigger('fadeInOut', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(10px)' }),
        animate(
          '300ms ease-out',
          style({ opacity: 1, transform: 'translateY(0)' })
        )
      ]),
      transition(':leave', [
        animate(
          '200ms ease-in',
          style({ opacity: 0, transform: 'translateY(10px)' })
        )
      ])
    ])
  ]
})
export class ClueComponent implements OnChanges, OnDestroy {
  @Input() round?: Round;
  @Input() room?: Room;
  @Input() isCast: boolean = false;

  authService = inject(AuthService);
  gameService: GameService = inject(GameService);
  cdr = inject(ChangeDetectorRef);
  router = inject(Router);
  currentClue?: Clue;
  clueState?: ClueState;
  roundState?: RoundState;
  roomState?: RoomState;
  currentCategory?: Category;

  guess?: string;
  intervalId: any;
  guessEval: boolean = false;
  passClicked: boolean = false;
  isGamemasterMode: boolean = false;
  isBuzzerMode: boolean = false;
  teamNames: string[] = [];
  selectedTeam: string | null = 'none';
  isAdminView: boolean = false;
  isPingInflight: boolean = false;
  hintOpened: boolean = false;
  answerOpened: boolean = false;
  lruCacheService = inject(LruCacheService);
  lruBuzzCache?: LRUCache<any>;
  ttsService = inject(TtsService);
  inputFocused = false;
  toggleHintPressed = false;
  toggleAnswerPressed = false;
  hasSkippedClue = false; // Local variable to track if user has skipped the current clue
  lastLogMessage: string | null = null; // Track the last log message
  previousLogCount: number = 0; // Track the number of logs to detect changes
  showCommentary: boolean = true; // Whether to show the snarky commentary
  @ViewChild('guessInput') guessInput!: ElementRef;
  @ViewChild('castCountdownTimer') castCountdownTimer?: CountdownTimerComponent; // Use '!' to indicate it will be initialized later

  async speak(text: string) {
    if (this.isCast) return;
    return await this.ttsService.speak(text);
  }

  constructor() {
    this.lruBuzzCache = this.lruCacheService.getOrCreateCache('buzzCache');
  }

  getHintBlanks() {
    const spacedString = this.currentClue!.hintBlanks!.replace(/ /g, '   ');

    // 2. Split into individual characters and add spaces between them
    const formattedString = spacedString.split('').join(' ');
    return formattedString;
  }
  haveISkipped() {
    // Use the local variable to determine if the user has skipped
    return this.hasSkippedClue;
  }

  async skipShowAnswer() {
    if (this.hasSkippedClue) {
      return; // Don't allow skipping again
    }

    // Set the local variable immediately for instant UI feedback
    this.hasSkippedClue = true;

    try {
      // Call the API
      await this.gameService.call('skipShowAnswer', {
        roomId: this.room!.roomId,
        roundIdx: this.roomState?.roundIdx,
        categoryIdx: this.roundState?.currentCategoryIdx,
        clueIdx: this.roundState?.currentClueIdx,
        playerId: this.authService.getUser()?.uid
      } as SkipShowAnswerRequest);
    } catch (error) {
      // If the API call fails, revert the local state
      console.error('Error skipping clue:', error);
      this.hasSkippedClue = false;
    }
  }
  lastAnswerExplanation() {
    if (!this.clueState?.answerExplanations) return undefined;

    const len = this.clueState.answerExplanations.length;
    return this.clueState.answerExplanations[len - 1];
  }

  updateLastLogMessage() {
    if (
      !this.clueState?.clueLog?.logs ||
      this.clueState.clueLog.logs.length === 0
    ) {
      this.lastLogMessage = null;
      this.previousLogCount = 0;
      return;
    }

    // Check if there's a new log message
    const currentLogCount = this.clueState.clueLog.logs.length;
    console.log(currentLogCount, this.previousLogCount);

    // Only update if the log count has changed
    if (currentLogCount > this.previousLogCount) {
      // Get the latest log message
      const originalMessage = this.clueState.clueLog.logs[currentLogCount - 1];

      // We're now using server-side snarky comments, so we just use the original message
      this.lastLogMessage = null;
      this.cdr.markForCheck();
      this.cdr.detectChanges();
      this.lastLogMessage = originalMessage;

      this.cdr.markForCheck();
      this.cdr.detectChanges();

      // Update the log count
      this.previousLogCount = currentLogCount;

      // Reset the banner after a few seconds
      setTimeout(() => {
        // Only clear if we're still showing a message for the same log entry
        // (prevents clearing a newer message)
        if (this.previousLogCount === currentLogCount) {
          console.log('Last message was: ', this.lastLogMessage);
          console.log('Setting lastLogMessage to null');
          this.lastLogMessage = null;
        }
      }, 6000); // Increased from 4000 to 6000 to give more time to read the snarky comment
    }
  }

  stringifyHtmlForTts(htmlString: string) {
    // 1. Create a temporary DOM element (doesn't need to be in the actual document)
    const tempDiv = document.createElement('div');

    // 2. Set the innerHTML of the temporary element to your HTML string
    tempDiv.innerHTML = htmlString;

    // 3. Extract the text content using either innerText or textContent
    //    - innerText:  Respects CSS styling (e.g., hidden elements won't be included).  Better for TTS.
    //    - textContent:  Gets *all* text, including hidden elements and script content.
    return tempDiv.innerText; // Or tempDiv.textContent, but innerText is usually better for TTS
  }

  async ngOnChanges() {
    if (!this.room || !this.round) return;
    this.isGamemasterMode =
      this.room!.roomSettings?.gameMode == GameMode.GAMEMASTER;
    this.isBuzzerMode = this.room?.roomSettings?.gameMode == GameMode.BUZZER;
    this.isAdminView = this.authService.getUser()?.uid === this.room?.host;
    this.teamNames = this.room?.playerIds!;
    this.roomState = this.room?.gameState.gameProgress as RoomState;
    this.roundState = this.roomState.roundStates[this.roomState.roundIdx];

    // Reset the hasSkippedClue flag when a new clue is loaded or when the component is initialized
    // Store the current clue identifiers to detect changes
    const currentCategoryIdx = this.roundState?.currentCategoryIdx;
    const currentClueIdx = this.roundState?.currentClueIdx;

    // If we don't have a current clue or the clue has changed, reset the skipped flag
    const clueChanged =
      !this.currentClue ||
      currentCategoryIdx !== this.roundState?.currentCategoryIdx ||
      currentClueIdx !== this.roundState?.currentClueIdx;

    if (clueChanged) {
      // Reset state for a new clue
      this.hasSkippedClue = false;
      this.lastLogMessage = null; // Reset the last log message when a new clue is loaded
      this.previousLogCount = 0; // Reset the log count when a new clue is loaded
      this.guessEval = false; // Make sure the guess evaluation state is reset
      this.guess = ''; // Clear any previous guess

      // Auto-start the countdown timer for gamemaster mode
      if (this.isGamemasterMode && this.isAdminView) {
        // Use setTimeout to ensure the clueState is properly initialized
        setTimeout(() => {
          // Start a new countdown with default duration (60 seconds)
          this.startCountdown(60);
        }, 500);
      }
    }

    if (this.roundState?.currentCategoryIdx == undefined) return;
    this.currentCategory = this.round?.categories[
      this.roundState.currentCategoryIdx!
    ];
    if (!this.currentClue) {
      this.currentClue = this.currentCategory?.clues[
        this.roundState.currentClueIdx!
      ];
      this.speak(this.stringifyHtmlForTts(this.currentClue!.questionHTML));
    }
    if (!this.inputFocused && this.shouldIAnswer()) {
      this.inputFocused = true;
      this.guessInput.nativeElement.focus();
    }
    if (!this.hintOpened && this.clueState?.hintOpened) {
      this.hintOpened = true;
      this.inputFocused = false;
      this.speak(this.currentClue!.hint);

      // Reset the countdown timer when hint is automatically shown
      if (
        this.isGamemasterMode &&
        this.isAdminView &&
        this.clueState?.countdownActive
      ) {
        // Use setTimeout to ensure state is properly updated
        setTimeout(() => {
          this.resetCountdown();
        }, 100);
      }
    }
    if (!this.answerOpened && this.clueState?.showAnswerStartTime) {
      this.answerOpened = true;
      // Use the original message for TTS, not the snarky version
      const originalMessage = this.clueState.clueLog.logs[
        this.clueState.clueLog.logs.length - 1
      ];
      this.speak(originalMessage);
    }
    this.clueState = this.roundState.categoryStates[
      this.roundState.currentCategoryIdx ?? -1
    ]?.clueStates[this.roundState.currentClueIdx ?? -1];

    // Update the last log message whenever the clue state changes
    // this.updateLastLogMessage();
    // Set up interval for pinging the room in both gamemaster and multiplayer modes
    // In gamemaster mode, we only need to ping when showAnswerStartTime is set
    const playerIdx = this.room?.playerIds.findIndex(
      p => p == this.authService.getUser()?.uid
    );
    const isHost = this.authService.getUser()?.uid === this.room?.host;
    console.log('playerIdx', playerIdx, 'isHost', isHost);
    // Allow interval setup if user is a player OR if user is the host in gamemaster mode
    if (
      (playerIdx != -1 || (this.isGamemasterMode && isHost)) &&
      !this.intervalId
    ) {
      this.intervalId = {};
      // Use a default delay of 10 if playerIdx is -1
      const delay =
        playerIdx !== undefined && playerIdx !== -1 ? playerIdx * 1000 : 0;
      setTimeout(() => {
        console.log('creating intervalId');
        this.intervalId = setInterval(
          () => {
            if (this.isPingInflight) {
              console.log('Skipping ping. Ping is inflight');
              return;
            }
            const buzzInTimerExpired =
              this.clueState?.buzzInTimerStartTime! +
                this.room?.roomSettings?.buzzInTimerDurationMillis! +
                10 <
              Date.now();
            let answerTimerExpired = true;

            if (this.clueState?.answerStartTime) {
              answerTimerExpired = this.getAnswerTargetTime() + 10 < Date.now();
            }
            let showAnswerTimeExpired = false;
            if (this.clueState?.showAnswerStartTime) {
              showAnswerTimeExpired =
                this.clueState?.showAnswerStartTime +
                  this.room?.roomSettings?.showAnswerDurationMillis! +
                  10 <
                Date.now();
            }

            // For gamemaster mode, we only need to check if the show answer timer has expired
            // For multiplayer mode, we need to check all timers
            if (
              (this.isGamemasterMode && showAnswerTimeExpired) ||
              (!this.isGamemasterMode &&
                ((answerTimerExpired &&
                  (buzzInTimerExpired || this.clueState?.answerStartTime) &&
                  this.clueState?.showAnswerStartTime == undefined) ||
                  showAnswerTimeExpired))
            ) {
              console.log('Pinging Room ', Date.now());
              this.isPingInflight = true;
              this.gameService
                .call('pingRoom', {
                  roomId: this.room?.roomId
                })
                .finally(() => {
                  this.isPingInflight = false;
                });
            }
          },
          this.isGamemasterMode ? 1000 : this.room?.playerIds.length! * 1000
        );
      }, delay);
    }
  }
  ngOnDestroy() {
    if (this.intervalId) {
      console.log('Clearing interval');
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
  }

  shouldIAnswer() {
    return (
      this.clueState?.buzzedInPlayerQueue != undefined &&
      this.clueState?.buzzedInPlayerQueue.length >
        this.clueState?.queueAnswerTurnIdx! &&
      this.clueState?.buzzedInPlayerQueue[
        this.clueState?.queueAnswerTurnIdx
      ] === this.authService.getUser()?.uid &&
      this.getAnswerTargetTime() >= Date.now()
    );
  }
  getBuzzInTargetTime() {
    return (
      this.clueState!.buzzInTimerStartTime! +
      this.room!.roomSettings!.buzzInTimerDurationMillis
    );
  }

  getAnswerTargetTime() {
    let answerExpirationTime =
      this.clueState?.answerStartTime! +
      this.room!.roomSettings!.answerDurationMillis;
    if (this.clueState!.queueAnswerTurnIdx == 0) {
      answerExpirationTime += this.room!.roomSettings!
        .initialThinkingDurationMillis;
    }
    return answerExpirationTime;
  }

  shouldShowBuzzTimer() {
    return (
      !this.shouldShowAnswer() &&
      this.room!.roomSettings?.gameMode == GameMode.BUZZER
    );
  }

  haveIPassed() {
    return this.clueState?.passedPlayers.some(
      p => p === this.authService.getUser()?.uid
    );
  }
  canPassTurn() {
    if (this.authService.getUser() == undefined) return false;
    // User can pass in the following scenarios -
    // 1. Game Master Mode - Never
    // 2. Buzzer Mode
    //    - if user hasn't buzzed in or passed yet.
    //    - if user has buzzed in, is user's turn and not passed.
    // 3. Turn based mode
    if (this.guessEval) {
      return false;
    }
    if (this.room?.mode == GameMode.GAMEMASTER) return false;
    if (this.haveIPassed()) {
      return false;
    }
    const hasBuzzed = this.haveIBuzzed();

    if (hasBuzzed && !this.shouldIAnswer()) {
      return false;
    }
    return true;
  }

  passTurn() {
    this.guessEval = true;

    // We'll let the updateLastLogMessage method handle showing the banner
    // when the server updates the log

    this.gameService
      .call('submitGuess', {
        roomId: this.room!.roomId!,
        roundIdx: this.roomState?.roundIdx!,
        categoryIdx: this.roundState?.currentCategoryIdx!,
        clueIdx: this.roundState?.currentClueIdx!,
        guess: '<PASSING>'
      })
      .then(() => {
        // Wait a short time for the server to update the clue state
        setTimeout(() => {
          // Force update the last log message
          this.updateLastLogMessage();
        }, 500);
      })
      .catch(error => {
        console.error('Error passing turn:', error);
      })
      .finally(() => {
        setTimeout(() => {
          this.guessEval = false;
        }, 1000);
      });
  }

  submitGuess() {
    this.guessEval = true;
    const myGuess = this.guess!; // Store the guess locally

    // We'll let the updateLastLogMessage method handle showing the banner
    // when the server updates the log

    this.gameService
      .call('submitGuess', {
        roomId: this.room!.roomId!,
        roundIdx: this.roomState?.roundIdx!,
        categoryIdx: this.roundState?.currentCategoryIdx!,
        clueIdx: this.roundState?.currentClueIdx!,
        guess: myGuess
      })
      .then(() => {
        // Wait a short time for the server to update the clue state
        setTimeout(() => {
          // Force update the last log message
          this.updateLastLogMessage();
        }, 500);
      })
      .catch(error => {
        console.error('Error submitting guess:', error);
        // We won't set a message here, as it would be cleared by the timeout
      })
      .finally(() => {
        setTimeout(() => {
          this.guessEval = false;
          this.guess = '';
        }, 1000); // Delay to show the spinner for a moment
      });
  }

  shouldShowAnswer() {
    const res =
      this.clueState?.showAnswerStartTime != undefined ||
      (this.isGamemasterMode && this.isAdminView) ||
      this.clueState?.showAnswer;
    return res;
  }

  getBuzzKey() {
    return `${this.room?.roomId!}-${this.roomState?.roundIdx}-${
      this.roundState?.currentCategoryIdx
    }-${this.roundState?.currentClueIdx}-${this.clueState?.hintOpened}`;
  }

  canBuzzIn() {
    if (this.authService.getUser() == undefined) return false;
    if (this.guessEval || this.haveIBuzzed() || this.haveIPassed()) {
      return false;
    }

    return this.isBuzzerMode && !this.haveIBuzzed();
  }
  haveIBuzzed() {
    return (
      this.lruBuzzCache!.get(this.getBuzzKey()) != null ||
      this.clueState?.buzzedInPlayerQueue?.some(
        p => p === this.authService.getUser()?.uid
      )
    );
  }
  async buzzIn() {
    this.lruBuzzCache!.set(this.getBuzzKey(), {});
    const buzzMessage = {
      playerId: this.authService.getUser()!.uid,
      buzzTime: serverTimestamp()
    };
    await FirebaseFirestore.setDocument({
      reference: `rooms/${this.room!.roomId}/rounds/${this.roomState!
        .roundIdx!}/categories/${this.roundState
        ?.currentCategoryIdx!}/clues/${this.roundState!
        .currentClueIdx!}/hintOpened/${this.clueState
        ?.hintOpened!}/buzzerQueue/${this.authService.getUser()!.uid}`,
      data: buzzMessage,
      merge: false
    });
    setTimeout(() => {
      this.gameService.call('buzzIn', {
        roomId: this.room!.roomId!,
        roundIdx: this.roomState?.roundIdx!,
        categoryIdx: this.roundState?.currentCategoryIdx!,
        clueIdx: this.roundState?.currentClueIdx!,
        hintOpened: this.clueState?.hintOpened ?? false
      });
    }, 30);
  }

  assignPoints() {
    if (!this.isGamemasterMode || this.selectedTeam === undefined) return;

    this.gameService
      .call('assignPoints', {
        roomId: this.room!.roomId!,
        roundIdx: this.roomState?.roundIdx!,
        categoryIdx: this.roundState?.currentCategoryIdx!,
        clueIdx: this.roundState?.currentClueIdx!,
        team: this.selectedTeam
      })
      .then(() => {
        console.log('Points assigned successfully');
      })
      .catch(error => {
        console.log(error);
      });
  }

  takeGameMaterAction(
    action:
      | 'toggleHint'
      | 'toggleAnswer'
      | 'END_GAME'
      | 'startCountdown'
      | 'resetCountdown'
      | 'pauseCountdown',
    _buttonPressed: boolean,
    countdownDurationSeconds?: number
  ) {
    const request: GameMasterActionsRequest = {
      roomId: this.room?.roomId!,
      roundIdx: this.roomState?.roundIdx,
      categoryIdx: this.roundState?.currentCategoryIdx,
      clueIdx: this.roundState?.currentClueIdx,
      action: action
    };

    // Add countdown duration if provided
    if (countdownDurationSeconds !== undefined) {
      request.countdownDurationSeconds = countdownDurationSeconds;
    }

    this.gameService
      .call('gameMasterActions', request)
      .then(updatedClue => {
        console.log('gameMaster action completed successfully', updatedClue);
        // this.clue = updatedClue;
      })
      .catch(error => {
        console.error('Error: ', error);
      })
      .finally(() => {
        // No need to set button state here
      });
  }
  toggleHint() {
    this.takeGameMaterAction('toggleHint', this.toggleHintPressed);

    // Reset the countdown timer when hint is shown
    if (
      this.isGamemasterMode &&
      this.isAdminView &&
      this.clueState?.countdownActive
    ) {
      // Use setTimeout to ensure the hint toggle action completes first
      setTimeout(() => {
        this.resetCountdown();
      }, 100);
    }
  }
  toggleAnswer() {
    this.takeGameMaterAction('toggleAnswer', this.toggleAnswerPressed);
  }

  // Countdown timer methods
  startCountdown(durationSeconds: number = 60) {
    this.takeGameMaterAction('startCountdown', false, durationSeconds);
  }

  resetCountdown() {
    this.takeGameMaterAction('resetCountdown', false);
  }

  pauseCountdown() {
    this.takeGameMaterAction('pauseCountdown', false);
  }

  addCountdownTime(seconds: number) {
    // First get the current remaining time
    if (
      !this.clueState?.countdownActive ||
      !this.clueState?.countdownStartTime ||
      !this.clueState?.countdownDurationSeconds
    ) {
      return; // Can't adjust time if timer isn't active
    }

    const now = Date.now();
    const elapsedSeconds = Math.floor(
      (now - this.clueState.countdownStartTime) / 1000
    );
    const remainingSeconds =
      this.clueState.countdownDurationSeconds - elapsedSeconds;

    // Calculate new duration by adding seconds to the remaining time
    const newDurationSeconds = remainingSeconds + seconds;
    if (newDurationSeconds <= 0) {
      // If the new duration would be negative or zero, just stop the timer
      this.pauseCountdown();
      return;
    }

    // Start a new countdown with the adjusted duration
    this.takeGameMaterAction('startCountdown', false, newDurationSeconds);
  }

  subtractCountdownTime(seconds: number) {
    // Reuse the addCountdownTime method with negative seconds
    this.addCountdownTime(-seconds);
  }
  regenerateQuestion() {
    this.gameService
      .call('regenerateClue', {
        roomId: this.room?.roomId,
        roundIdx: this.roomState?.roundIdx,
        categoryIdx: this.roundState?.currentCategoryIdx,
        clueIdx: this.roundState?.currentClueIdx,
        categoryTitle: this.round?.categories[
          this.roundState?.currentCategoryIdx!
        ].categoryTitle,
        points: this.currentClue?.value
      })
      .then(updatedClue => {
        console.log('Question regenerated successfully', updatedClue);
        // this.clue = updatedClue;
      })
      .catch(error => {
        console.error('Error regenerating question:', error);
      });
  }

  /**
   * Gets the team name from the player ID
   * @param playerId The player ID to look up
   * @returns The team name
   */
  getTeamName(playerId?: string): string {
    if (!playerId || !this.room) return 'Unknown';

    // In gamemaster mode, the player ID is the team name
    if (this.isGamemasterMode) {
      return playerId;
    }

    // In other modes, look up the player name
    const playerIdx = this.room.playerIds.findIndex(p => p === playerId);
    if (playerIdx !== -1 && this.room.playerInfos[playerIdx].userName) {
      return this.room.playerInfos[playerIdx].userName!;
    }

    return 'Unknown';
  }

  goToQuestionManager() {
    if (!this.room || !this.isAdminView) return;
    this.router.navigate(['/questions', this.room.roomId]);
  }
}
