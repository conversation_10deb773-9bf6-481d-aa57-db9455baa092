<!-- Answer result notification -->
<!-- <div
  *ngIf="lastLogMessage"
  class="answer-result"
  [class.correct]="lastLogMessage.toLowerCase().includes('correct!')"
  [class.incorrect]="lastLogMessage.toLowerCase().includes('incorrect')"
  [class.passed]="lastLogMessage.toLowerCase().includes('passed')"
  [class.neutral]="
    !lastLogMessage.toLowerCase().includes('correct!') &&
    !lastLogMessage.toLowerCase().includes('incorrect') &&
    !lastLogMessage.toLowerCase().includes('passed')
  "
>
  <mat-icon>{{
    lastLogMessage.toLowerCase().includes("correct!")
      ? "check_circle"
      : lastLogMessage.toLowerCase().includes("passed")
      ? "skip_next"
      : lastLogMessage.toLowerCase().includes("incorrect")
      ? "cancel"
      : "info"
  }}</mat-icon>
  <span>{{ lastLogMessage }}</span>
</div> -->

<div
  class="clue-container small-gap"
  [ngClass]="{ isGamemasterMode: isGamemasterMode, 'is-cast': isCast }"
>
  <div class="clue-title-and-skip-button">
    <div class="clue-title">
      {{ currentCategory?.categoryTitle }} ({{ currentClue?.value }})
      <mat-icon
        class="info-icon"
        matTooltip="Current clue category and point value"
        >help_outline</mat-icon
      >
    </div>
    <button
      *ngIf="shouldShowAnswer() && !isGamemasterMode"
      mat-raised-button
      class="skip-show-answer-button"
      [class.disabled]="haveISkipped()"
      [disabled]="haveISkipped()"
      (click)="skipShowAnswer()"
    >
      {{ haveISkipped() ? "Skipped" : "Skip" }}
    </button>
  </div>
  <div class="clue-body small-gap question-answer">
    <div class="question-and-answer-control">
      <div
        class="button-container gamemaster-regen-buttons"
        *ngIf="isGamemasterMode"
      >
        <!-- <button
        mat-raised-button
        *ngIf="isGamemasterMode && isAdminView"
        color="primary"
        (click)="closeClue()"
        title="Close Clue"
      >
        Close Clue
      </button> -->
        <button
          mat-raised-button
          *ngIf="isGamemasterMode && isAdminView"
          color="primary"
          (click)="goToQuestionManager()"
          matTooltip="Open question manager to edit all questions"
        >
          Manage Questions
        </button>
        <button
          mat-raised-button
          *ngIf="isGamemasterMode && isAdminView"
          color="warn"
          (click)="regenerateQuestion()"
          matTooltip="Generate a new question for this category and value"
        >
          Regenerate Question
        </button>
        <button
          mat-raised-button
          *ngIf="isGamemasterMode && isAdminView"
          color="warn"
          (click)="toggleHint()"
          matTooltip="Show or hide the hint for this clue"
        >
          {{ this.clueState?.hintOpened == true ? "Hide" : "Show" }} Hint
        </button>
      </div>
      <div class="vertical-divs small-gap question-and-hint">
        <div class="vertical-divs">
          <div
            class="question"
            [innerHTML]="getSanitizedQuestionHTML()"
            appMediaRegister
          ></div>
          <div
            class="on-clue-container"
            *ngIf="
              (clueState?.hintOpened && !shouldShowAnswer()) ||
              (isGamemasterMode && isAdminView)
            "
          >
            <div class="hints"><b>Hint: </b>{{ currentClue?.hint }}</div>
            <div>
              <div class="hints hint-blanks">
                <b>Answer: </b>{{ getHintBlanks() }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="cast-buzz-in-timer"
        *ngIf="
          isCast &&
          !clueState?.showAnswerStartTime &&
          !isGamemasterMode &&
          this.clueState?.buzzInTimerStartTime
        "
        style="width:100%"
      >
        <app-timer-chip
          class="buzz-in-timer-remaining"
          [initialTime]="this.clueState!.buzzInTimerStartTime!"
          [targetTime]="getBuzzInTargetTime()"
          text="BuzzIn Timer"
        ></app-timer-chip>
      </div>

      <!-- Countdown timer for cast view in gamemaster mode
           Note: We check for countdownStartTime instead of countdownActive to ensure
           the timer remains visible even when paused -->
      <div
        class="cast-countdown-timer"
        *ngIf="
          isCast &&
          isGamemasterMode &&
          clueState?.countdownStartTime &&
          !clueState?.showAnswerStartTime
        "
        style="width:100%"
      >
        <app-countdown-timer
          #castCountdownTimer
          [startTime]="clueState?.countdownStartTime"
          [durationSeconds]="clueState?.countdownDurationSeconds || 60"
          [active]="clueState?.countdownActive || false"
          [showControls]="false"
          [horizontal]="true"
        ></app-countdown-timer>
      </div>
      <div
        class="pass-or-buzz-button small-gap"
        *ngIf="!clueState?.showAnswerStartTime && !isGamemasterMode && !isCast"
      >
        <button
          *ngIf="isBuzzerMode"
          mat-raised-button
          class="buzz-in-button"
          (click)="buzzIn()"
          [disabled]="!canBuzzIn()"
        >
          <app-timer-chip
            *ngIf="this.clueState?.buzzInTimerStartTime"
            class="buzz-in-timer-remaining"
            [initialTime]="this.clueState!.buzzInTimerStartTime!"
            [targetTime]="getBuzzInTargetTime()"
          ></app-timer-chip>
          <span *ngIf="!haveIBuzzed()">Buzz</span>
          <span *ngIf="haveIBuzzed()">Buzzed</span>
        </button>
        <button
          mat-raised-button
          [disabled]="!canPassTurn()"
          (click)="passTurn()"
          class="pass-turn-button"
        >
          <app-timer-chip
            *ngIf="this.clueState?.buzzInTimerStartTime"
            class="buzz-in-timer-remaining"
            style="opacity:0"
            [initialTime]="this.clueState!.buzzInTimerStartTime!"
            [targetTime]="getBuzzInTargetTime()"
          ></app-timer-chip>
          <span>Pass Turn</span>
        </button>
      </div>

      <div class="vertical-divs large-gap answer-container">
        <mat-progress-bar
          *ngIf="
            !shouldIAnswer() &&
            haveIBuzzed() &&
            isBuzzerMode &&
            !shouldShowAnswer()
          "
          class="tertiary-colors"
          mode="indeterminate"
        ></mat-progress-bar>
        <div
          class="on-clue-container vertical-divs small-gap"
          *ngIf="shouldIAnswer()"
        >
          <app-timer-chip
            class="answer-timer-remaining"
            [initialTime]="this.clueState!.answerStartTime!"
            [targetTime]="getAnswerTargetTime()"
          ></app-timer-chip>

          <div class="vertical-divs">
            <mat-form-field>
              <input
                matInput
                required="required"
                [disabled]="guessEval"
                placeholder="Enter your answer"
                [(ngModel)]="guess"
                color="tertiary"
                (keydown.enter)="submitGuess()"
                #guessInput
              />
              <mat-icon *ngIf="guessEval" matSuffix class="input-spinner">
                <mat-spinner diameter="20"></mat-spinner>
              </mat-icon>
            </mat-form-field>
            <button
              mat-raised-button
              color="primary"
              [disabled]="guessEval || guess == undefined || guess == ''"
              (click)="submitGuess()"
              matTooltip="Submit your answer"
            >
              <span *ngIf="!guessEval">Submit Guess</span>
              <span *ngIf="guessEval">Checking...</span>
            </button>
          </div>
        </div>
        <!-- <div class="vertical-divider" *ngIf="!shouldShowAnswer()"></div> -->
        <div
          class="on-clue-container vertical-divs spread-vertical small-gap"
          *ngIf="shouldShowAnswer()"
        >
          <div style="width:100%">
            <app-timer-chip
              *ngIf="this.clueState?.showAnswerStartTime"
              class="show-answer-timer-remaining"
              [initialTime]="this.clueState!.showAnswerStartTime!"
              [targetTime]="
                this.clueState?.showAnswerStartTime! +
                this.room?.roomSettings?.showAnswerDurationMillis!
              "
            ></app-timer-chip>
            <div style="width:100%">
              <div class="answer" style="width:100%">
                <b>Answer:</b> {{ currentClue?.answer }}
              </div>
              <i *ngIf="lastAnswerExplanation()?.isCorrect">
                {{ lastAnswerExplanation()?.explanation }}
              </i>
            </div>
          </div>
          <div>
            <p><b>Explanation:</b></p>
            <ol>
              <li
                *ngFor="let fact of currentClue?.detailedFactsAboutAnswer"
                [innerHTML]="fact"
              ></li>
            </ol>
          </div>
        </div>
      </div>
      <div class="on-clue-container" *ngIf="isGamemasterMode && isAdminView">
        <div class="gamemaster-controls-container">
          <div class="points-container">
            <h2>Assign Points</h2>
            <mat-form-field>
              <mat-select [(ngModel)]="selectedTeam">
                <mat-option *ngFor="let team of teamNames" [value]="team">
                  {{ team }}
                </mat-option>
                <mat-option value="none">None</mat-option>
              </mat-select>
            </mat-form-field>
            <button
              mat-raised-button
              (click)="assignPoints()"
              matTooltip="Award points to the selected team"
            >
              Assign Points
            </button>
          </div>

          <div class="countdown-container">
            <h2>Countdown Timer</h2>
            <app-countdown-timer
              [startTime]="clueState?.countdownStartTime"
              [durationSeconds]="clueState?.countdownDurationSeconds || 60"
              [active]="clueState?.countdownActive || false"
              [showControls]="true"
              (startTimer)="startCountdown($event)"
              (resetTimer)="resetCountdown()"
              (pauseTimer)="pauseCountdown()"
              (addTime)="addCountdownTime($event)"
              (subtractTime)="subtractCountdownTime($event)"
            ></app-countdown-timer>
          </div>
        </div>
      </div>
    </div>
    <!-- Snarky Commentary Component -->
  </div>
  <app-snarky-commentary
    *ngIf="
      (!isGamemasterMode && clueState?.clueLog) ||
      (isGamemasterMode && clueState?.showAnswerStartTime)
    "
    [clueState]="clueState"
    [ngClass]="{ 'gamemaster-commentary': isGamemasterMode }"
    @fadeInOut
  ></app-snarky-commentary>
</div>
