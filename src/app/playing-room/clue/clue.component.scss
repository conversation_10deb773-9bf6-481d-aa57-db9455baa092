.clue-container {
  top: 0;
  left: 0;
  padding-bottom: 0.1rem;
  /* Ensure the clue is on top */
  z-index: 100; /* Higher z-index to ensure it's on top */
  background-color: var(--mat-sys-surface); /* Use Material surface color */
  color: var(--mat-sys-on-surface);
  width: 98%; /* Width of the container */
  height: 98%; /* Fixed height based on viewport height */
  border-radius: 1.5rem;
  font-family: "Roboto";
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: -5px 5px 5px
      var(--mat-sys-primary-container, rgba(-1, 100, 256, 0.2)),
    5px 5px 5px var(--mat-sys-primary-container, rgba(0, 100, 255, 0.2));

  /* Add gradient border */
  background-clip: padding-box;
  /* Add subtle pattern background that works in both light and dark modes */
  background-image: radial-gradient(
    color-mix(in srgb, var(--mat-sys-on-primary-container) 20%, transparent),
    transparent 2px
  );
  background-size: 0.75rem 0.75rem;
  /* Add 3D effect properties */
  transform-style: preserve-3d;
  backface-visibility: hidden;
  will-change: transform, opacity;

  /* Center the container */
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  overflow-y: auto;
  overflow-x: hidden;
}

.clue-body {
  padding: 0.25rem 0.25rem 0.2rem 0.25rem; /* Reduced padding */
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  justify-content: flex-start; /* Align content to the top */
  flex-grow: 1;
  overflow: auto; /* Prevent scrolling on TV */
  height: 100%;
  background-color: var(--mat-sys-surface); /* Use Material surface color */
  animation: fadeIn 0.5s ease-out; /* Fade in the body content */
  /* Add a very subtle dot pattern to the clue body */
  background-image: radial-gradient(
    color-mix(in srgb, var(--mat-sys-on-primary-container) 5%, transparent),
    transparent 2px
  );
  background-size: 0.75rem 0.75rem;
}

.answer-container {
  width: 100%;
}
.small-gap {
  gap: 3px;
}
.mid-gap {
  gap: 6px;
}
.large-gap {
  gap: 10px;
}
.huge-gap {
  gap: 8px; /* Further reduced */
}

.content-section {
  flex-grow: 1; /* Allow content section to take up available space */
  display: flex;
  flex-direction: column;
  align-items: center;
}

.clue-container h1 {
  color: var(--mat-sys-on-primary-container);
  font-size: 1.6rem;
  margin-bottom: 6px;
  text-align: center;
}

.clue-container p {
  color: var(--mat-sys-on-secondary-container);
  font-size: 1.1rem;
  text-align: center;
  margin: 0;
}

.hint-container {
  margin: 10px;
}

.timers-container {
  display: flex;
  gap: 20px; /* Space between timers */
  align-self: flex-end; /* Align to the end of the cross axis (right in this case) */
  margin-top: auto; /* Push the timers to the bottom */
}

.on-clue-container {
  width: 100%;
  background-color: var(--mat-sys-surface-container-high);
  color: var(--mat-sys-on-surface-variant);
  padding: 8px 8px;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* Add a subtle shadow to inner containers */
  text-align: center;
  border-left: 3px solid var(--mat-sys-primary);
}

.actually-answer {
  display: flex;
  width: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.answer-timer-remaining {
  width: 100%;
}

.clue-container .guess-container mat-form-field {
  width: 100%;
}

.clue-container .history-container {
  background-color: var(--mat-sys-surface-container);
  width: 100%; /* Adjust width as needed */
  max-height: 95%; /* Set maximum height for scrollability */
  overflow-y: auto; /* Enable vertical scrolling */
  border: 1px solid var(--mat-sys-outline); /* Add a border */
  border-radius: 10px; /* Match the rounded corners */
}

.clue-container .history-container p {
  color: var(--mat-sys-on-surface);
  font-size: 0.9rem;
  padding: 2px 2px; /* Add padding to log entries */
  border-radius: 5px; /* Rounded corners for log entries */
}

.history-container {
  font: var(--mat-sys-body-large);
}

/* Alternate line colors for chat log effect */
.clue-container .history-container p:nth-child(even) {
  background-color: var(
    --mat-sys-surface-container-highest
  ); /* Darker background for even lines */
}

.clue-container .history-container p:nth-child(odd) {
  background-color: var(
    --mat-sys-surface-container-high
  ); /* Slightly lighter background for odd lines */
}
.buzz-in-timer-remaining {
  --timer-track-color: var(--mat-sys-secondary-container);
  --timer-stroke-color: var(--mat-sys-on-secondary-container);
}

.buzzed-indicator {
  display: none; /* Hidden by default, shown only when button is disabled */
}

.show-answer-timer-remaining {
  --timer-track-color: var(--mat-sys-secondary-container);
  --timer-stroke-color: var(--mat-sys-on-secondary-container);
  width: 100%;
}

.buzz-in-button-div {
  flex-grow: 1;
  display: contents;
}

.buzz-in-button {
  --mdc-protected-button-container-height: 100%;
  width: 70%;
  flex-grow: 1;
  border-radius: 1rem;
  font: var(--mat-sys-headline-large); /* Smaller font */
  display: inline;
  padding: 0.25rem 0.75rem; /* Reduced padding */
  /* No fixed height - let content determine size */
  background: linear-gradient(
    45deg,
    var(--mat-sys-primary),
    var(--mat-sys-primary-container)
  );
  color: var(
    --mat-sys-on-primary-container
  ); /* Ensure text has good contrast */
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
  box-shadow: 0 4px 15px
    color-mix(in srgb, var(--mat-sys-shadow) 30%, transparent);
  transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1),
    box-shadow 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;

  /* Add a pseudo-element for the background animation */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      45deg,
      var(--mat-sys-primary),
      var(--mat-sys-primary-container)
    );
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
    z-index: 0;
    opacity: 0.7;
  }

  &:hover:not(:disabled) {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 6px 20px
      color-mix(in srgb, var(--mat-sys-shadow) 40%, transparent);
  }

  &:active:not(:disabled) {
    transform: translateY(1px) scale(0.98);
    box-shadow: 0 2px 10px
      color-mix(in srgb, var(--mat-sys-shadow) 30%, transparent);
  }

  /* Custom styling for disabled state */
  &:disabled {
    background: color-mix(
      in srgb,
      var(--mat-sys-error) 30%,
      var(--mat-sys-surface-container-high)
    );
    color: var(--mat-sys-on-surface-variant);
    box-shadow: none;
    border: 1px solid var(--mat-sys-outline-variant);
    cursor: not-allowed;
    opacity: 0.9;
    position: relative;

    /* Stop the gradient animation when disabled */
    animation: none;

    /* Add a visual indicator that the button is disabled */
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(var(--mat-sys-error-rgb), 0.1) 10px,
        rgba(var(--mat-sys-error-rgb), 0.1) 20px
      );
      z-index: 1;
      animation: none;
    }

    /* Ensure the timer remains visible */
    app-timer-chip {
      opacity: 1;
      z-index: 2;
    }

    /* Hide the original text when disabled */
    > span:not(.buzzed-indicator) {
      opacity: 0;
    }

    /* Show the buzzed indicator when disabled */
    .buzzed-indicator {
      display: block !important;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font: var(--mat-sys-title-medium);
      color: var(--mat-sys-on-surface-variant);
      z-index: 2;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      font-weight: 500;
      animation: fadeIn 0.3s ease-out;
    }
  }

  /* Add ripple effect */
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
  }

  &:focus:not(:active)::after {
    animation: ripple 1s ease-out;
  }

  @keyframes ripple {
    0% {
      transform: scale(0, 0) translate(-50%, -50%);
      opacity: 0.5;
    }
    100% {
      transform: scale(20, 20) translate(-50%, -50%);
      opacity: 0;
    }
  }
}

.hint-blanks {
  white-space: pre-wrap;
}

mat-form-field {
  width: 100%;
}

.spread-vertical {
  justify-content: unset;
  flex-grow: 1;
}

.button-container {
  display: flex;
  gap: 10px;
}

.pass-or-buzz-button {
  display: flex;
  width: 100%;
  padding-top: 1%;
  align-items: stretch; /* Ensure children stretch to fill the container height */
  gap: 10px; /* Consistent gap between buttons */
}
.pass-button {
  flex-grow: 1;
  width: 70%;
  height: 100%;
}

.question {
  font: var(--mat-sys-title-large); /* Smaller font */
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  line-height: 1.3; /* Reduced line height */
  margin: 0.25rem 0 0.25rem 0;
  padding: 0.5rem;
  padding-left: 1rem;
  background-color: var(
    --mat-sys-surface-container-low
  ); /* Subtle background */
  border-radius: 8px; /* Subtle rounded corners */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* Very subtle shadow */
  animation: fadeIn 0.5s ease-out;
  letter-spacing: 0.02em;
  border-left: 3px solid var(--mat-sys-primary);
  width: 100%; /* Take full width of container */
  color: var(--mat-sys-on-surface); /* Use Material text color */
}

/* Constrain media elements within questions to prevent huge display */
.question img,
.question video,
.question audio,
.question iframe {
  max-width: 100% !important;
  max-height: 300px !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain;
  border-radius: 0.25rem;
  margin: 0.5rem 0;
}

/* Ensure YouTube iframes maintain aspect ratio */
.question iframe[src*="youtube.com"] {
  aspect-ratio: 16/9;
  width: 100% !important;
  max-width: 400px !important;
  height: auto !important;
}

/* Override any inline styles that might make content huge */
.question * {
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Media query for mobile phones */
@media screen and (max-width: 600px) {
  .question {
    font: var(--mat-sys-title-medium); /* Smaller font for mobile */
    line-height: 1.2; /* Further reduced line height for mobile */
    padding: 0.4rem;
    padding-left: 0.75rem;
  }
}

/* Media query for TV screens (16:9 aspect ratio) */
@media screen and (min-aspect-ratio: 16/9) and (max-aspect-ratio: 16/9) {
  .clue-container {
    max-width: 1200px;
    max-height: 650px;
  }

  .question {
    font: var(--mat-sys-title-large);
    line-height: 1.3;
  }

  .clue-title {
    font: var(--mat-sys-headline-small);
    padding: 0.5rem 1rem;
  }

  .hints {
    font: var(--mat-sys-title-medium);
    line-height: 1.3;
    padding: 0.5rem 0.75rem;
  }
}

/* Additional cast mode optimizations */
:host-context(.is-cast) {
  --cast-card-padding: 0.4rem; /* Reduced padding */
  --cast-border-radius: 1rem;
  --cast-gap: 0.3rem; /* Reduced gap */
}

:host-context(.is-cast) .clue-container {
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

:host-context(.is-cast) .clue-body {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: var(--cast-card-padding);
  padding-top: 0.2rem; /* Further reduced top padding */
  padding-bottom: 0.2rem; /* Further reduced bottom padding */
}

:host-context(.is-cast) .clue-title {
  font-size: clamp(0.9rem, 2.2vw, 1.3rem); /* Reduced font size */
  padding: 0.3rem 0.8rem; /* Reduced padding */
  text-align: center;
  flex: 0 0 auto;
  min-height: 2rem; /* Ensure minimum height */
}

:host-context(.is-cast) .question {
  font-size: clamp(1rem, 2.5vw, 1.7rem); /* Reduced font size */
  line-height: 1.2; /* Reduced line height */
  padding: 0.3rem 0.6rem; /* Reduced padding */
  text-align: center;
  flex: 1 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0.1rem 0; /* Reduced margin */
}

/* Cast mode media constraints */
:host-context(.is-cast) .question img,
:host-context(.is-cast) .question video,
:host-context(.is-cast) .question audio,
:host-context(.is-cast) .question iframe {
  max-width: 90vw !important;
  max-height: 40vh !important;
  width: auto !important;
  height: auto !important;
}

/* Cast mode YouTube constraints */
:host-context(.is-cast) .question iframe[src*="youtube.com"] {
  max-width: 60vw !important;
  max-height: 35vh !important;
}

/* Plyr player styling */
.question .plyr {
  max-width: 100% !important;
  max-height: 300px !important;
  border-radius: 0.25rem;
  margin: 0.5rem 0;
}

/* Cast mode Plyr constraints */
:host-context(.is-cast) .question .plyr {
  max-width: 90vw !important;
  max-height: 40vh !important;
}

/* Plyr YouTube specific styling */
.question .plyr--youtube {
  aspect-ratio: 16/9;
  max-width: 400px !important;
}

:host-context(.is-cast) .question .plyr--youtube {
  max-width: 60vw !important;
  max-height: 35vh !important;
}

:host-context(.is-cast) .hints {
  font-size: clamp(0.9rem, 2vw, 1.2rem);
  line-height: 1.3;
  padding: 0.5rem 1rem;
  text-align: center;
  flex: 0 0 auto;
}

:host-context(.is-cast) .answer {
  font-size: clamp(1rem, 2.5vw, 1.5rem);
  text-align: center;
  flex: 0 0 auto;
}

:host-context(.is-cast) .vertical-divs {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: var(--cast-gap);
  flex: 1 1 auto;
  margin-bottom: 0.1rem; /* Reduced bottom margin */
}

:host-context(.is-cast) .small-gap {
  gap: calc(var(--cast-gap) * 0.4); /* Further reduced gap */
}

:host-context(.is-cast) .mid-gap {
  gap: var(--cast-gap);
}

:host-context(.is-cast) .large-gap {
  gap: calc(var(--cast-gap) * 1.5);
}

:host-context(.is-cast) .huge-gap {
  gap: calc(var(--cast-gap) * 2);
}

/* Fix for Material icons in cast mode */
:host-context(.is-cast) .mat-icon {
  height: auto !important;
  width: auto !important;
  font-size: 1em !important;
  line-height: 1 !important;
  overflow: visible !important;
}

.hints {
  line-height: 1.5;
  font: var(--mat-sys-title-large);
  padding: 0.75rem 1rem;
  background-color: var(--mat-sys-surface-container-low);
  border-radius: 0.75rem;
  margin: 0.5rem 0;
  animation: slideInUp 0.4s ease-out;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border-left: 3px solid var(--mat-sys-tertiary);
  width: 100%;
  box-sizing: border-box;
  text-align: left;
}

/* Media query for hints text on mobile phones */
@media screen and (max-width: 600px) {
  .hints {
    font: var(--mat-sys-title-medium); /* Smaller font for mobile */
    line-height: 1.3;
    padding: 0.5rem 0.75rem;
  }
}

.clue-title {
  font: var(--mat-sys-title-large); /* Even smaller font */
  background: linear-gradient(
    135deg,
    var(--mat-sys-secondary),
    var(--mat-sys-tertiary)
  );
  color: var(--mat-sys-on-secondary);
  width: 100%;
  text-align: center;
  padding: 0; /* Increased padding for more height */
  min-height: 2.5rem; /* Ensure minimum height */
  border-radius: 1.5rem 1.5rem 0 0;
  box-shadow: 0 2px 10px
    color-mix(in srgb, var(--mat-sys-shadow) 20%, transparent);
  position: relative;
  text-shadow: 0 1px 2px
    color-mix(in srgb, var(--mat-sys-shadow) 30%, transparent);
  display: flex;
  align-items: center;
  justify-content: center;

  /* Add subtle animated background */
  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 100%;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    animation: shimmer 3s infinite linear;
    z-index: 1;
  }

  /* Make sure text is above the shimmer effect */
  & > * {
    position: relative;
    z-index: 2;
  }
}

.clue-title-and-skip-button {
  width: 100%;
  position: relative;
}

.skip-show-answer-button {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100; /* Higher z-index */
  font-size: 0.8rem;
  padding: 0.15rem 0.5rem;
  min-width: auto;
  height: 2rem;
  background-color: var(--mat-sys-error); /* Error color for visibility */
  color: var(--mat-sys-on-error); /* Text color for contrast */
  border: none;
  box-shadow: 0 2px 5px
    color-mix(in srgb, var(--mat-sys-shadow) 30%, transparent);
  transition: all 0.3s ease;
  line-height: 1;
  margin: 0 !important; /* Prevent margin changes */
  --mdc-protected-button-container-height: 2rem;
}

.skip-show-answer-button:hover {
  box-shadow: 0 4px 8px
    color-mix(in srgb, var(--mat-sys-shadow) 40%, transparent);
  background-color: color-mix(in srgb, var(--mat-sys-error) 90%, black);
  transform: translateY(-50%) scale(1.05);
}

.skip-show-answer-button:active {
  transform: translateY(-50%) scale(0.95);
  box-shadow: 0 2px 4px
    color-mix(in srgb, var(--mat-sys-shadow) 20%, transparent);
}

.skip-show-answer-button.disabled {
  background-color: color-mix(
    in srgb,
    var(--mat-sys-error) 50%,
    var(--mat-sys-surface)
  );
  color: color-mix(
    in srgb,
    var(--mat-sys-on-error) 70%,
    var(--mat-sys-surface)
  );
  cursor: not-allowed;
  box-shadow: none;
  opacity: 0.7;
}

.skip-show-answer-button.disabled:hover {
  transform: translateY(-50%);
  box-shadow: none;
  background-color: color-mix(
    in srgb,
    var(--mat-sys-error) 50%,
    var(--mat-sys-surface)
  );
}
.question-and-hint {
  width: 100%;
}
.question-and-answer-control {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
  width: 100%;
  gap: 0.25rem;
}
.pass-turn-button {
  --mdc-protected-button-container-height: 100%;
  width: 70%;
  flex-grow: 1;
  border-radius: 1rem;
  font: var(--mat-sys-headline-large);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.75rem;
  /* No fixed height - let content determine size */
  background: linear-gradient(
    45deg,
    var(--mat-sys-tertiary),
    var(--mat-sys-tertiary-container)
  );
  color: var(--mat-sys-on-secondary); /* Ensure text has good contrast */
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
  box-shadow: 0 4px 15px
    color-mix(in srgb, var(--mat-sys-shadow) 10%, transparent);
  transition: transform 0.1s cubic-bezier(0.25, 0.8, 0.25, 1),
    box-shadow 0.1s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;

  /* Add a pseudo-element for the background animation */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      45deg,
      var(--mat-sys-tertiary),
      var(--mat-sys-tertiary-container)
    );
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
    z-index: 0;
    opacity: 0.7;
  }

  &:hover:not(:disabled) {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 6px 20px
      color-mix(in srgb, var(--mat-sys-shadow) 40%, transparent);
  }

  &:active:not(:disabled) {
    transform: translateY(1px) scale(0.98);
    box-shadow: 0 2px 10px
      color-mix(in srgb, var(--mat-sys-shadow) 30%, transparent);
  }

  /* Add ripple effect */
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
  }

  &:focus:not(:active)::after {
    animation: ripple 1s ease-out;
  }

  /* Custom styling for disabled state */
  &:disabled {
    background: color-mix(
      in srgb,
      var(--mat-sys-error) 30%,
      var(--mat-sys-surface-container-high)
    );
    color: var(--mat-sys-on-surface-variant);
    box-shadow: none;
    border: 1px solid var(--mat-sys-outline-variant);
    cursor: not-allowed;
    opacity: 0.9;
    position: relative;

    /* Stop the gradient animation when disabled */
    animation: none;

    /* Add a visual indicator that the button is disabled */
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(var(--mat-sys-error-rgb), 0.1) 10px,
        rgba(var(--mat-sys-error-rgb), 0.1) 20px
      );
      z-index: 1;
      animation: none;
    }
  }
}

.answer {
  font: var(--mat-sys-display-small);
}

.points-awarded {
  margin: 10px 0;
  font-size: 1.1em;
  color: var(--mat-sys-color-primary);
  font-weight: 500;
  padding: 8px;
  background-color: var(--mat-sys-surface-container-low);
  border-radius: 8px;
  border-left: 3px solid var(--mat-sys-primary);
  animation: fadeIn 0.5s ease-out;
}

/* Media query for answer text on mobile phones */
@media screen and (max-width: 600px) {
  .answer {
    font: var(--mat-sys-headline-small); /* Smaller font for mobile */
  }

  .points-awarded {
    font-size: 1em;
  }
}

.no-transform {
  transform: none !important;
  &:hover {
    transform: none !important;
  }
}

.question-answer {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

app-snarky-commentary {
  width: 100%;
  max-height: 25%;
}

/* Shorter snarky commentary for gamemaster mode */
.isGamemasterMode app-snarky-commentary,
app-snarky-commentary.gamemaster-commentary {
  max-height: 10%;
}

/* Gamemaster controls container */
.gamemaster-controls-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  gap: 2rem;
  margin-top: 1rem;
}

.points-container,
.countdown-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 1rem;
  background-color: rgba(var(--mat-sys-primary-rgb), 0.05);
  border-radius: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Countdown timer container styling */
.cast-countdown-timer {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 0.5rem 0;
  padding: 0.5rem;
}

/* Style the horizontal countdown timer for the TV display */
.cast-countdown-timer app-countdown-timer {
  width: 100%;
}
/* Animations */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0, 0) translate(-50%, -50%);
    opacity: 0.5;
  }
  100% {
    transform: scale(20, 20) translate(-50%, -50%);
    opacity: 0;
  }
}

/* Animation for the close button */
@keyframes rotateOut {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
  100% {
    transform: rotate(90deg) scale(0.5);
    opacity: 0;
  }
}

/* Answer result indicators */
.answer-result {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0.1rem;
  text-align: center;
  font-weight: bold;
  z-index: 1000;
  animation: slideUp 1s ease-out forwards, slideDown 1s ease-out 2.5s forwards;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
  border-bottom: 2px solid rgba(0, 0, 0, 0.1);

  border-radius: 0.5rem;
  /* Add subtle pattern background that works in both light and dark modes */
  background-image: radial-gradient(
    color-mix(in srgb, var(--mat-sys-on-surface) 5%, transparent),
    transparent 1px
  );
  background-size: 0.5rem 0.5rem;
  font: var(--mat-sys-body-small);
  mat-icon {
    font-size: 1rem;
    height: 1rem;
    width: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.answer-result.correct {
  background-color: #c8e6c9; /* Light green */
  color: #2e7d32; /* Dark green text for contrast */
}

.answer-result.incorrect {
  background-color: #ffcdd2; /* Light red */
  color: #c62828; /* Dark red text for contrast */
}

.answer-result.passed {
  background-color: #e1f5fe; /* Light blue */
  color: #0277bd; /* Dark blue text for contrast */
}

.answer-result.neutral {
  background-color: #f5f5f5; /* Light gray */
  color: #616161; /* Dark gray text for contrast */
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }

  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
/* Spinner for answer submission */
.input-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--mat-sys-primary);
}
