<div class="scoreboard-container" [class.is-cast]="isCast">
  <div class="scoreboard">
    <div class="scoreboard-header">
      <div class="scoreboard-title">
        Scoreboard
        <mat-icon class="info-icon" matTooltip="Current player scores"
          >leaderboard</mat-icon
        >
      </div>
    </div>
    <div class="player-list">
      <div
        class="player-bar"
        *ngFor="let playerId of room?.playerIds; let pIdx = index"
        [class.current-player]="
          playerId == currentPlayerId && !isGamemasterMode
        "
        [matTooltip]="
          playerId == authService.getUser()?.uid ? 'This is you' : ''
        "
      >
        <div class="player-info">
          <div class="player-name-and-icon">
            <img
              *ngIf="room!.playerInfos[pIdx].photoUrl"
              class="player-icon"
              [src]="room!.playerInfos[pIdx].photoUrl"
            />
            <span
              class="player-name"
              [class.my-player]="playerId == authService.getUser()?.uid"
              >{{ room!.playerInfos[pIdx].userName }}</span
            >
          </div>
          <span class="player-score">{{ room!.playerInfos[pIdx].score }}</span>
        </div>
        <div class="player-status">
          <span
            class="buzzed-in"
            matTooltip="This player has buzzed in"
            *ngIf="
              clueState?.buzzedInPlayerQueue &&
              clueState!.buzzedInPlayerQueue!.includes(playerId)
            "
            >Buzzed In</span
          >
          <app-timer-chip
            class="answering-chip"
            *ngIf="playerId == currentAnswerPlayerId"
            [text]="'Answering'"
            [initialTime]="answerStartTime!"
            [targetTime]="answerTargetTime!"
          ></app-timer-chip>
        </div>
      </div>
    </div>
  </div>
</div>
