.scoreboard-container {
  display: flex;
  width: 100%;
  justify-content: center;
  background-color: transparent;
  z-index: 10;
  flex-shrink: 0;
  max-height: 120px; /* Limit height for TV screens */
}

.scoreboard {
  display: flex;
  flex-direction: column;
  width: 100%;
  /* Reduced max width */
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  /* Remove rounded corners */
  box-shadow: 0px -2px 4px -1px rgba(0, 0, 0, 0.2),
    0px -4px 5px 0px rgba(0, 0, 0, 0.14), 0px -1px 10px 0px rgba(0, 0, 0, 0.12); /* Add shadow */
}

.scoreboard-header {
  background: linear-gradient(
    135deg,
    var(--mat-sys-primary),
    var(--mat-sys-tertiary)
  );

  color: var(--mat-sys-on-primary);
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}

.scoreboard-title {
  text-align: center;
  font: var(--mat-sys-title-medium);
  padding: 0.25rem 0;
}

.player-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  grid-template-rows: auto;
  gap: 0.2rem;
  padding: 0.2rem;
  background-color: rgba(var(--mat-sys-surface-rgb), 0.8);
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
  justify-items: center;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.5s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-height: 90px; /* Limit height for TV screens */
  overflow: hidden;
}

.player-bar {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 250px;
  background: linear-gradient(
    135deg,
    rgba(var(--mat-sys-primary-container-rgb), 1),
    rgba(var(--mat-sys-primary-container-rgb), 0.8)
  );
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1), background 0.3s ease;
  cursor: pointer;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-sizing: border-box; /* Ensure border is included in width/height calculations */
  padding: 2px; /* Further reduced padding */

  /* Add subtle shine effect */
  &::after {
    content: "";
    position: absolute;
    top: -100%;
    left: -100%;
    width: 300%;
    height: 300%;
    background: linear-gradient(
      to bottom right,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.15) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(30deg) translateY(100%);
    transition: transform 0.7s ease-out;
    z-index: 1;
    pointer-events: none;
  }

  /* Make sure content is above the shine effect and overlay */
  & > * {
    position: relative;
    z-index: 3;
  }
}

.player-bar:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.player-bar:hover::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  opacity: 0.1;
  z-index: 1;
  border-radius: 0.75rem;
}

.player-bar:hover::after {
  transform: rotate(30deg) translateY(-100%);
}

/* The highlighted scorecard indicates the current player whose turn it is */
.current-player {
  background: linear-gradient(
    135deg,
    var(--mat-sys-primary),
    var(--mat-sys-tertiary)
  );
  color: var(--mat-sys-on-primary);
  animation: subtlePulse 2s infinite ease-in-out;
  z-index: 10; /* Ensure it appears above other cards */
}

/* Pulsating animation for current player */
@keyframes subtlePulse {
  0% {
    box-shadow: 0 5px 15px rgba(var(--mat-sys-primary-rgb), 0.3);
    transform: translateY(-1px) scale(1);
  }
  33% {
    box-shadow: 0 8px 25px rgba(var(--mat-sys-primary-rgb), 0.6);
    transform: translateY(-2px) scale(1);
  }
  66% {
    box-shadow: 0 5px 15px rgba(var(--mat-sys-primary-rgb), 0.3);
    transform: translateY(-3px) scale(1);
  }
  100% {
    box-shadow: 0 5px 15px rgba(var(--mat-sys-primary-rgb), 0.3);
    transform: translateY(-2px) scale(1);
  }
}

.player-info {
  display: flex;
  justify-content: space-between;
  padding-left: 10%;
  padding-right: 10%;
  width: 100%;
}

.player-name {
  font: var(--mat-sys-title-medium); /* Smaller font */
  color: var(--mat-sys-on-primary-container); /* Use Material variable */
  transition: color 0.1s ease; /* Faster transition for text */
  font-weight: 500;
}

.player-score {
  font: var(--mat-sys-title-medium); /* Smaller font */
  color: var(--mat-sys-on-primary-container); /* Use Material variable */
  transition: color 0.1s ease; /* Faster transition for text */
  font-weight: 500;
}

.current-player .player-name,
.current-player .player-score,
.current-player .my-player {
  color: var(--mat-sys-on-primary); /* Use Material variable */
  font-weight: 600;
}

.player-bar:hover .player-name,
.player-bar:hover .player-score,
.player-bar:hover .my-player {
  color: black;
  font-weight: 900;
  text-shadow: 0 0 3px white, 0 0 5px white;
  opacity: 1; /* Ensure text is fully opaque */
}
.player-name-and-icon {
  display: flex;
  align-items: center;
  position: relative;
}
.player-icon {
  height: 2em;
  vertical-align: middle;
  border-radius: 100%;
  margin-right: 5%;
}
.player-status {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  font: var(--mat-sys-body-small);
  min-height: 20px;
}

.buzzed-in {
  background-color: var(--mat-sys-on-primary);
  color: var(--mat-sys-primary);
  font: var(--mat-sys-body-small);
  margin: 2px;
  padding: 2px 6px;
  border-radius: 4px;
}
.answering-chip {
  --timer-track-color: var(--mat-sys-error-container);
  --timer-stroke-color: var(--mat-sys-error);
  --timer-foreground-color: var(--mat-sys-on-error);
}

/* The indicator for the current user (you) */
.my-player {
  position: relative;
  font-weight: 600;
  color: var(--mat-sys-on-primary-container); /* Match scorecard text color */
}

/* Add a small dot after the name to indicate 'you' */
.my-player::after {
  content: "•"; /* Bullet character */
  color: currentColor; /* Use same color as the text */
  font-size: 1.2em;
  line-height: 0;
  position: relative;
  top: 0.1em;
  margin-left: 0.2em;
}

/* TV-specific styles */
@media screen and (min-aspect-ratio: 16/9) and (max-aspect-ratio: 16/9) {
  .scoreboard-container {
    display: flex;
    flex-direction: column;
  }

  .player-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .player-bar {
    max-width: 200px;
  }

  .player-name,
  .player-score {
    font: var(--mat-sys-title-small);
  }

  .player-status {
    min-height: 16px;
  }

  .buzzed-in {
    font: var(--mat-sys-body-small);
    padding: 1px 4px;
  }
}

/* Cast mode specific styles */
:host-context(.is-cast) .scoreboard-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

:host-context(.is-cast) .scoreboard {
  display: flex;
  flex-direction: column;
  width: 100%;
}

:host-context(.is-cast) .scoreboard-title {
  font-size: clamp(0.9rem, 2vw, 1.2rem);
  padding: 0.25rem 0;
  flex: 0 0 auto;
}

:host-context(.is-cast) .player-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.25rem;
  padding: 0.25rem;
  width: 100%;
  overflow: hidden;
}

:host-context(.is-cast) .player-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 0.25rem;
}

:host-context(.is-cast) .player-name,
:host-context(.is-cast) .player-score {
  font-size: clamp(0.8rem, 1.5vw, 1rem);
  text-align: center;
}

:host-context(.is-cast) .player-status {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: clamp(0.7rem, 1.2vw, 0.9rem);
}

:host-context(.is-cast) .buzzed-in {
  padding: 0.1rem 0.3rem;
  border-radius: 0.25rem;
  font-size: clamp(0.7rem, 1.2vw, 0.9rem);
}

/* Fix for Material icons in cast mode */
:host-context(.is-cast) .mat-icon {
  height: auto !important;
  width: auto !important;
  font-size: 1em !important;
  line-height: 1 !important;
  overflow: visible !important;
}
