<div class="game-and-scoreboard-container" [class.is-cast]="isCast">
  <div class="game-container">
    <div class="game-controls" *ngIf="isHost">
      <button
        mat-mini-fab
        color="primary"
        (click)="goToQuestionManager()"
        class="question-manager-button"
        matTooltip="Manage Questions"
      >
        <mat-icon>quiz</mat-icon>
      </button>
      <button
        mat-mini-fab
        color="warn"
        (click)="endGame()"
        class="end-game-button"
        matTooltip="End the game and show results"
      >
        <mat-icon>emoji_events</mat-icon>
      </button>
    </div>

    <app-board
      *ngIf="this.room"
      [room]="this.room"
      [round]="this.currentRound"
      [isCast]="isCast"
    ></app-board>

    <app-clue
      @clueEnterLeaveAnimation
      (animationDone)="onClueAnimationComplete($event)"
      *ngIf="this.showClue"
      [room]="this.room"
      [round]="this.currentRound"
      [isCast]="isCast"
    ></app-clue>
  </div>
  <div class="scoreboard-and-actions">
    <app-scoreboard [room]="this.room" [isCast]="isCast"></app-scoreboard>
  </div>
</div>
