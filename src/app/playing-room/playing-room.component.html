<div class="game-and-scoreboard-container" [class.is-cast]="isCast">
  <div class="game-container">
    <button
      *ngIf="isHost"
      mat-mini-fab
      color="warn"
      (click)="endGame()"
      class="end-game-button"
      matTooltip="End the game and show results"
    >
      <mat-icon>emoji_events</mat-icon>
    </button>
    <app-board
      *ngIf="this.room"
      [room]="this.room"
      [round]="this.currentRound"
      [isCast]="isCast"
    ></app-board>

    <app-clue
      @clueEnterLeaveAnimation
      (animationDone)="onClueAnimationComplete($event)"
      *ngIf="this.showClue"
      [room]="this.room"
      [round]="this.currentRound"
      [isCast]="isCast"
    ></app-clue>
  </div>
  <div class="scoreboard-and-actions">
    <app-scoreboard [room]="this.room" [isCast]="isCast"></app-scoreboard>
  </div>
</div>
