import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  inject,
  Input,
  NgZone,
  OnChanges,
  OnDestroy,
  SimpleChanges
} from '@angular/core';
import { BoardComponent } from './board/board.component';
import {
  Room,
  RoomState,
  Round,
  RoundState
} from '../../../functions/src/resources';
import { Router } from '@angular/router';
import { ClueComponent } from './clue/clue.component';
import { CommonModule } from '@angular/common';
import { ScoreboardComponent } from './scoreboard/scoreboard.component';
import { CategorySelectorComponent } from '../category-selector/category-selector.component';
import { FirebaseFirestore } from '@capacitor-firebase/firestore';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MediaControlsComponent } from '../components/media-controls/media-controls.component';
import { PlyrControlsComponent } from '../components/plyr-controls/plyr-controls.component';
import { GameEndService } from '../services/game-end.service';
import { AuthService } from '../services/auth.service';
import { CastMediaControlService } from '../services/cast-media-control.service';
import { PlyrService } from '../services/plyr.service';
import { GameMode } from '../../../functions/src/resources';
import { animate, style, transition, trigger } from '@angular/animations';

@Component({
  selector: 'app-playing-room',
  imports: [
    BoardComponent,
    ClueComponent,
    CommonModule,
    ScoreboardComponent,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MediaControlsComponent,
    PlyrControlsComponent
  ],
  templateUrl: './playing-room.component.html',
  styleUrl: './playing-room.component.css',
  standalone: true,
  animations: [
    trigger('clueEnterLeaveAnimation', [
      // Enter animation - card flips in from the side with a bounce effect
      transition(':enter', [
        style({
          opacity: 0,
          transform: 'scale(0.8) rotateZ(5deg) translateY(100px)', // Start small, rotated clockwise, and down
          transformOrigin: 'center center' // Good practice to define origin
        }),
        // Animate to the final, stable state
        animate(
          '1s cubic-bezier(0.175, 0.885, 0.32, 1.275)', // Using your preferred 'bouncy' enter easing
          style({
            opacity: 1,
            transform: 'scale(1)' // Animate to normal size and position
            // You could also use 'transform: 'none'' here
          })
        )
      ]),
      // Leave animation - card zooms out with a spin
      transition(':leave', [
        style({
          opacity: 1,
          transform: 'scale(1)'
        }),
        animate(
          '1s cubic-bezier(0.6, -0.28, 0.735, 0.045)',
          style({
            opacity: 0,
            transform: 'scale(0.8) rotateZ(5deg) translateY(100px)'
          })
        )
      ])
    ])
  ]
})
export class PlayingRoomComponent implements OnChanges, OnDestroy {
  @Input() room?: Room;
  @Input() isCast: boolean = false;
  currentRound?: Round;
  roomState?: RoomState;
  currentRoundState?: RoundState;
  isHost: boolean = false;

  zone = inject(NgZone); // Inject NgZone
  categoryIdx?: number;
  clueIdx?: number;
  roundIdx?: number;
  roomId?: string;
  router: Router = inject(Router);
  cdr = inject(ChangeDetectorRef);
  callbackId?: string;
  authService = inject(AuthService);
  gameEndService = inject(GameEndService);
  castMediaControlService = inject(CastMediaControlService);
  plyrService = inject(PlyrService);
  showClue = false;
  isGamemasterMode = false;
  async ngOnChanges(changes: SimpleChanges) {
    if (this.callbackId) {
      FirebaseFirestore.removeSnapshotListener({
        callbackId: this.callbackId
      });
      this.callbackId = undefined;
    }
    // Check if the current user is the host
    if (this.room) {
      this.isHost = this.room.host === this.authService.getUser()?.uid;
      this.isGamemasterMode =
        this.room.roomSettings?.gameMode === GameMode.GAMEMASTER;

      // Subscribe to media commands for cast room
      if (this.isCast && this.room.roomId) {
        this.subscribeToMediaCommands(this.room.roomId);
      }
    }

    this.roomState = this.room?.gameState.gameProgress as RoomState;
    this.callbackId = await FirebaseFirestore.addDocumentSnapshotListener(
      {
        reference: `rooms/${this.room!.roomId}/roomRoundData/${
          this.roomState.roundIdx
        }`
      },
      (event, error) => {
        this.zone.run(() => {
          if (error) {
            console.log(error);
          } else if (event?.snapshot.data == null) {
            this.currentRound = undefined;
          } else {
            this.currentRound = event.snapshot.data! as Round;
            this.cdr.markForCheck();
          }
        });
      }
    );
    this.currentRoundState = this.roomState?.roundStates[
      this.roomState.roundIdx
    ];
    const currentClue = this.roomState?.roundStates[this.roomState.roundIdx]
      .currentClueIdx;
    this.showClue = currentClue != undefined;
  }
  ngOnDestroy(): void {
    if (this.callbackId) {
      this.currentRound = undefined;
      FirebaseFirestore.removeSnapshotListener({
        callbackId: this.callbackId
      });
    }

    // Unsubscribe from media commands
    this.castMediaControlService.unsubscribeFromCommands();
  }

  private async subscribeToMediaCommands(roomId: string): Promise<void> {
    // Subscribe to media commands
    await this.castMediaControlService.subscribeToCommands(roomId);

    // Listen for commands and execute them
    this.castMediaControlService.mediaCommands$.subscribe(command => {
      if (command) {
        console.log('Cast room executing media command:', command);
        this.plyrService.executeCastCommand(command);
      }
    });
  }

  /**
   * Navigate to the question manager
   */
  goToQuestionManager(): void {
    if (!this.room) return;
    this.router.navigate(['/questions', this.room.roomId]);
  }

  /**
   * End the current game and show the winner's screen
   */
  async endGame(): Promise<void> {
    if (!this.room) return;

    try {
      await this.gameEndService.endGame(this.room.roomId);
    } catch (error) {
      console.error('Error ending game:', error);
    }
  }

  /**
   * Handle animation events for the clue component
   */
  onClueAnimationComplete(event: any): void {
    this.roundIdx = this.roomState?.roundIdx;
    this.categoryIdx = this.roomState?.roundStates[
      this.roomState.roundIdx
    ].currentCategoryIdx;
    this.clueIdx = this.roomState?.roundStates[
      this.roomState.roundIdx
    ].currentClueIdx;
  }
}
