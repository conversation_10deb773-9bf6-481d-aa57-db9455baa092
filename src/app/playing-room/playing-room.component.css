.game-and-scoreboard-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 100vw;
  height: 100%;
  max-height: 100vh;
  overflow: auto; /* Prevent scrolling on TV */
  -webkit-overflow-scrolling: touch; /* Improve scrolling on iOS */
}

.scoreboard-and-actions {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.game-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
  display: flex;
  gap: 0.5rem;
}

.question-manager-button {
  background-color: var(--mat-sys-secondary-container);
  color: var(--mat-sys-on-secondary-container);
}

.end-game-button {
  /* Styles already defined by Material Design */
}

.game-container {
  position: relative;
  width: 100%;
  max-width: 100vw;
  justify-content: center; /* Center the board horizontally */
  flex-grow: 1;
  display: flex;
  height: calc(100vh - 150px); /* Adjust height to leave room for scoreboard */
  padding: 0; /* Remove any padding */
  margin: 0; /* Remove any margin */
  box-sizing: border-box; /* Include padding in width calculation */
}

app-board {
  width: 100%;
  max-width: 100vw;
  flex-grow: 1; /* Make app-board stretch */
  display: flex;
  justify-content: center; /* Center the board horizontally */
  overflow: auto; /* Prevent scrolling on TV */
}

app-clue {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  max-width: 100vw;
  height: 100%;
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center; /* Center vertically for better TV viewing */
  padding: 0; /* Remove any padding */
  margin: 0; /* Remove any margin */
  box-sizing: border-box; /* Include padding in width calculation */
  overflow: hidden; /* Hide overflow completely */
}

/* Perspective container for 3D effects */
.clue-perspective-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  perspective: 2000px;
  z-index: 100;
}

:host {
  flex-grow: 1;
  display: flex;
  width: 100%;
  max-width: 100vw;
  height: 100%;
  max-height: 100vh;
  overflow: hidden;
}

/* TV-specific styles for cast mode */
:host-context(.is-cast) {
  --cast-card-margin: 0.25rem;
  --cast-card-padding: 0.5rem;
  --cast-border-radius: 0.5rem;
  --cast-gap: 0.25rem;
}

:host-context(.is-cast) .game-and-scoreboard-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
}

:host-context(.is-cast) .game-container {
  flex: 1 1 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

:host-context(.is-cast) .scoreboard-and-actions {
  flex: 0 0 auto;
}

/* Hide tooltips in cast mode */
:host-context(.is-cast) .mat-tooltip {
  display: none !important;
}

/* Remove unnecessary padding in cast mode */
:host-context(.is-cast) .board-container {
  padding: var(--cast-card-padding);
}

:host-context(.is-cast) .game-controls {
  transform: scale(0.9);
  top: 0.5rem;
  right: 0.5rem;
}

/* Fix for Material icons in cast mode */
:host-context(.is-cast) .mat-icon {
  height: auto !important;
  width: auto !important;
  font-size: 1em !important;
  line-height: 1 !important;
  overflow: visible !important;
}

/* Media query specifically for 16:9 aspect ratio screens */
@media screen and (min-aspect-ratio: 16/9) and (max-aspect-ratio: 16/9) {
  .game-container {
    height: calc(100vh - 120px);
  }

  .scoreboard-and-actions {
    max-height: 120px;
  }
}
