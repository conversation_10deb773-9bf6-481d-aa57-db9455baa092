import {
  Component,
  Input,
  OnChanges,
  SimpleChanges,
  ViewChild,
  ElementRef,
  AfterViewChecked,
  On<PERSON>estroy,
  OnInit
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
// No longer need SnarkService as we're using server-side snarky comments
import { ClueState } from '../../../../functions/src/resources';
import { LogEntry } from '../../../../functions/src/structured_log';
import {
  trigger,
  transition,
  style,
  animate,
  query,
  stagger
} from '@angular/animations';

@Component({
  selector: 'app-snarky-commentary',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatIconModule, MatTooltipModule],
  templateUrl: './snarky-commentary.component.html',
  styleUrls: ['./snarky-commentary.component.scss'],
  animations: [
    trigger('listAnimation', [
      transition('* => *', [
        // When items are added
        query(
          ':enter',
          [
            // Start with new items invisible and slightly above their final position
            style({
              opacity: 0,
              transform: 'translateY(-15px)',
              height: 0,
              overflow: 'hidden'
            }),
            // Stagger the animations so they don't all happen at once
            stagger('120ms', [
              // Animate to final state with a smooth easing function
              animate(
                '350ms cubic-bezier(0.35, 0, 0.25, 1)',
                style({
                  opacity: 1,
                  transform: 'translateY(0)',
                  height: '*',
                  overflow: 'visible'
                })
              )
            ])
          ],
          { optional: true }
        ),
        // When items are removed
        query(
          ':leave',
          [
            // Animate out with a smooth fade and slight movement
            animate(
              '250ms cubic-bezier(0.35, 0, 0.25, 1)',
              style({
                opacity: 0,
                transform: 'translateY(10px)',
                height: 0,
                overflow: 'hidden'
              })
            )
          ],
          { optional: true }
        )
      ])
    ])
  ]
})
export class SnarkyCommentaryComponent
  implements OnChanges, AfterViewChecked, OnInit, OnDestroy {
  @Input() clueState?: ClueState;
  @ViewChild('commentsList') private commentsListElement?: ElementRef;
  private shouldScrollToTop = false;

  // Single array of displayed comments
  displayedComments: {
    message: string;
    type: string;
    timestamp: number;
  }[] = [];

  // Index pointer to track which entries have been displayed
  private displayIndex = 0;

  // Timer for displaying comments
  private displayTimer: any = null;

  // Display interval in milliseconds
  private readonly DISPLAY_INTERVAL = 500;

  constructor() {}

  ngOnInit() {
    // We'll start timers only when needed
  }

  ngOnDestroy() {
    // Clean up any existing timer when component is destroyed
    this.stopDisplayTimer();
  }

  private startDisplayTimer() {
    // Clear any existing timer
    this.stopDisplayTimer();

    // Start a new timer that displays the next comment after the interval
    this.displayTimer = setTimeout(() => {
      this.displayNextComment();
    }, this.DISPLAY_INTERVAL);
  }

  private stopDisplayTimer() {
    if (this.displayTimer) {
      clearTimeout(this.displayTimer);
      this.displayTimer = null;
    }
  }

  ngAfterViewChecked() {
    // Scroll to top when new comments are added
    if (this.shouldScrollToTop && this.commentsListElement) {
      this.commentsListElement.nativeElement.scrollTop = 0;
      this.shouldScrollToTop = false;
    }
  }

  ngOnChanges(_changes: SimpleChanges): void {
    // Check if there are new comments to display
    this.checkForNewComments();
  }

  /**
   * Checks if there are new comments to display and starts the display timer if needed
   */
  private checkForNewComments(): void {
    if (
      !this.clueState ||
      !this.clueState.structuredClueLog ||
      !this.clueState.structuredClueLog.entries
    ) {
      return;
    }

    const entries = this.clueState.structuredClueLog.entries;

    // If there are entries we haven't displayed yet
    if (this.displayIndex < entries.length) {
      // Start the timer to display the next comment
      this.startDisplayTimer();
    }
  }

  /**
   * Displays the next comment and schedules the next one if needed
   */
  private displayNextComment(): void {
    if (
      !this.clueState ||
      !this.clueState.structuredClueLog ||
      !this.clueState.structuredClueLog.entries
    ) {
      return;
    }

    const entries = this.clueState.structuredClueLog.entries;

    // If there are entries we haven't displayed yet
    if (this.displayIndex < entries.length) {
      // Get the next entry to display
      const entry = entries[this.displayIndex];

      // Skip WAITING_FOR_ANSWER logs that immediately follow a BUZZ_IN log
      // This prevents the duplicate logs when a player buzzes in
      if (entry.type === 'WAITING_FOR_ANSWER' && this.displayIndex > 0) {
        const previousEntry = entries[this.displayIndex - 1];
        if (
          previousEntry.type === 'BUZZ_IN' &&
          previousEntry.playerId === entry.playerId &&
          Math.abs(previousEntry.timestamp - entry.timestamp) < 100
        ) {
          // Within 100ms
          // Skip this entry and move to the next one
          this.displayIndex++;

          // If there are more entries, process the next one
          if (this.displayIndex < entries.length) {
            this.displayNextComment();
          }
          return;
        }
      }

      // Create a comment object from the entry
      const comment = {
        message: entry.snarkyComment || this.getDefaultSnarkyComment(entry),
        timestamp: entry.timestamp || Date.now(),
        type: entry.type
      };

      // Create a new array with the new comment at the beginning
      // This approach ensures Angular's change detection recognizes the change
      // and triggers the animation properly
      const updatedComments = [
        comment,
        ...this.displayedComments.filter(
          c =>
            !(
              c.message === comment.message && c.timestamp === comment.timestamp
            )
        )
      ];

      // Update the displayed comments array
      // Using setTimeout to ensure the DOM has time to process previous animations
      setTimeout(() => {
        this.displayedComments = updatedComments;

        // Set the scroll flag to scroll to the top
        this.shouldScrollToTop = true;

        // Increment the display index
        this.displayIndex++;

        // If there are more comments to display, schedule the next one
        if (this.displayIndex < entries.length) {
          this.startDisplayTimer();
        }
      }, 10); // Small delay to help with animation smoothness
    }
  }

  private getDefaultSnarkyComment(entry: LogEntry): string {
    // This is a fallback in case the server didn't provide a snarky comment
    switch (entry.type) {
      case 'CLUE_SELECTED':
        return `${entry.playerName} selected a clue. Let's see who pretends to know this one.`;
      case 'BUZZ_IN':
        return `${entry.playerName} buzzed in. Either they know it or they're very optimistic.`;
      case 'ANSWER_CORRECT':
        return `${entry.playerName} got it right. Even a broken clock is right twice a day.`;
      case 'ANSWER_INCORRECT':
        return `${entry.playerName} got it wrong. That answer was so off, it's in a different zip code.`;
      case 'PASS':
        return `${entry.playerName} passed. Better to remain silent and be thought a fool than to answer and remove all doubt.`;
      case 'TIMER_EXPIRED':
        return `${entry.playerName}'s time ran out. Thinking isn't supposed to take that long.`;
      case 'HINT_OPENED':
        return `Hint opened. Lowering the bar to accommodate current skill levels.`;
      case 'NO_CORRECT_ANSWER':
        return `No one got it right. Collective ignorance is still ignorance, folks.`;
      case 'WAITING_FOR_ANSWER':
        return `Waiting for ${entry.playerName} to answer. The suspense is... actually not that great.`;
      case 'SHOW_ANSWER':
        return `The answer was: ${entry.answer}. Better luck next time, geniuses.`;
      default:
        return `Something happened in the game. Not sure what, but it was probably underwhelming.`;
    }
  }

  // We no longer need these methods as we're using structured logs directly

  getCommentClass(type: string): string {
    switch (type) {
      case 'ANSWER_CORRECT':
        return 'correct-comment';
      case 'ANSWER_INCORRECT':
        return 'incorrect-comment';
      case 'PASS':
        return 'passed-comment';
      case 'BUZZ_IN':
        return 'buzz-comment';
      case 'HINT_OPENED':
        return 'hint-comment';
      case 'NO_CORRECT_ANSWER':
        return 'no-answer-comment';
      case 'WAITING_FOR_ANSWER':
        return 'waiting-comment';
      case 'CLUE_SELECTED':
        return 'clue-selected-comment';
      case 'SHOW_ANSWER':
        return 'show-answer-comment';
      case 'TIMER_EXPIRED':
        return 'timer-expired-comment';
      default:
        return 'neutral-comment';
    }
  }

  getCommentIcon(type: string): string {
    switch (type) {
      case 'ANSWER_CORRECT':
        return 'check_circle';
      case 'ANSWER_INCORRECT':
        return 'cancel';
      case 'PASS':
        return 'skip_next';
      case 'BUZZ_IN':
        return 'notifications_active';
      case 'HINT_OPENED':
        return 'lightbulb';
      case 'NO_CORRECT_ANSWER':
        return 'sentiment_very_dissatisfied';
      case 'WAITING_FOR_ANSWER':
        return 'hourglass_empty';
      case 'CLUE_SELECTED':
        return 'touch_app';
      case 'SHOW_ANSWER':
        return 'visibility';
      case 'TIMER_EXPIRED':
        return 'timer_off';
      default:
        return 'info';
    }
  }

  // Track comments by timestamp to help Angular's change detection
  trackByTimestamp(
    _index: number,
    comment: { message: string; type: string; timestamp: number }
  ): number {
    return comment.timestamp;
  }

  getCommentTooltip(type: string): string {
    switch (type) {
      case 'ANSWER_CORRECT':
        return 'Correct answer';
      case 'ANSWER_INCORRECT':
        return 'Incorrect answer';
      case 'PASS':
        return 'Player passed';
      case 'BUZZ_IN':
        return 'Player buzzed in';
      case 'HINT_OPENED':
        return 'Hint revealed';
      case 'NO_CORRECT_ANSWER':
        return 'No one answered correctly';
      case 'WAITING_FOR_ANSWER':
        return 'Waiting for player to answer';
      case 'CLUE_SELECTED':
        return 'Clue selected';
      case 'SHOW_ANSWER':
        return 'Answer revealed';
      case 'TIMER_EXPIRED':
        return 'Time ran out';
      default:
        return 'Game event';
    }
  }
}
