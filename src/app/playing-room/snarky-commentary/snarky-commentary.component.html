<div class="commentary-container" *ngIf="displayedComments.length > 0">
  <div class="comments-list" #commentsList>
    <div class="comments-wrapper" [@listAnimation]="displayedComments.length">
      <div
        *ngFor="let comment of displayedComments; trackBy: trackByTimestamp"
        class="comment-item"
        [ngClass]="getCommentClass(comment.type)"
      >
        <mat-icon
          class="comment-icon"
          [matTooltip]="getCommentTooltip(comment.type)"
          >{{ getCommentIcon(comment.type) }}</mat-icon
        >
        <span class="comment-text">{{ comment.message }}</span>
      </div>
    </div>
  </div>
</div>
