.commentary-container {
  width: 100%;
  height: 100%;
  background-color: var(--mat-sys-surface-container);
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
  overflow: auto;
  box-shadow: var(--mat-sys-level1);

  /* Add subtle pattern background that works in both light and dark modes */
  background-image: radial-gradient(
    color-mix(in srgb, var(--mat-sys-on-surface) 3%, transparent),
    transparent 1px
  );
  background-size: 0.5rem 0.5rem;
}

.commentary-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  margin: 0;
  font: var(--mat-sys-title-small);
  background-color: var(--mat-sys-tertiary-container);
  color: var(--mat-sys-on-tertiary-container);
  border-bottom: 1px solid var(--mat-sys-outline-variant);
}

.comments-list {
  padding: 0.75rem;
  max-height: 300px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0.2rem;

  /* Add smooth scrolling for better animation effect */
  scroll-behavior: smooth;

  /* Ensure new comments at the top are visible */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--mat-sys-outline-variant);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

.comments-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
  will-change: transform, opacity; /* Optimize for animations */
}

.comment-item {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  border-radius: 0.5rem;
  justify-content: center;
  font: var(--mat-sys-title-small);
  background-color: var(--mat-sys-surface-container-high);
  transition: all 0.3s cubic-bezier(0.35, 0, 0.25, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  will-change: transform, opacity; /* Optimize for animations */

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  }
}

.comment-icon {
  // font: var(--mat-sys-title-small);
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.comment-text {
  line-height: 1.4;
}

/* Comment type styles */
.correct-comment {
  border-left: 3px solid var(--mat-sys-primary);
  .comment-icon {
    color: var(--mat-sys-primary);
  }
}

.incorrect-comment {
  border-left: 3px solid var(--mat-sys-error);
  .comment-icon {
    color: var(--mat-sys-error);
  }
}

.passed-comment {
  border-left: 3px solid var(--mat-sys-tertiary);
  .comment-icon {
    color: var(--mat-sys-tertiary);
  }
}

.buzz-comment {
  border-left: 3px solid var(--mat-sys-secondary);
  .comment-icon {
    color: var(--mat-sys-secondary);
  }
}

.hint-comment {
  border-left: 3px solid var(--mat-sys-tertiary);
  .comment-icon {
    color: var(--mat-sys-tertiary);
  }
}

.no-answer-comment {
  border-left: 3px solid var(--mat-sys-error);
  .comment-icon {
    color: var(--mat-sys-error);
  }
}

.waiting-comment {
  border-left: 3px solid var(--mat-sys-secondary);
  .comment-icon {
    color: var(--mat-sys-secondary);
  }
}

.clue-selected-comment {
  border-left: 3px solid var(--mat-sys-primary);
  .comment-icon {
    color: var(--mat-sys-primary);
  }
}

.show-answer-comment {
  border-left: 3px solid var(--mat-sys-tertiary);
  .comment-icon {
    color: var(--mat-sys-tertiary);
  }
}

.timer-expired-comment {
  border-left: 3px solid var(--mat-sys-error);
  .comment-icon {
    color: var(--mat-sys-error);
  }
}

.neutral-comment {
  border-left: 3px solid var(--mat-sys-outline);
  .comment-icon {
    color: var(--mat-sys-outline);
  }
}
