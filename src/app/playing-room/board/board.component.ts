import { Component, Input, OnChanges, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  Round,
  RoundState,
  Room,
  GameMode,
  RoomState
} from '../../../../functions/src/resources';
import { GameService } from '../../services/game.service';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AuthService } from '../../services/auth.service';
import { LRUCache, LruCacheService } from '../../services/lru-cache.service';

@Component({
  selector: 'app-board',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule
  ],
  templateUrl: './board.component.html',
  styleUrls: ['./board.component.css']
})
export class BoardComponent implements OnChanges {
  @Input() room?: Room;
  @Input() round?: Round;
  @Input() isCast: boolean = false;

  gameService: GameService = inject(GameService);
  authService = inject(AuthService);

  isGamemasterMode: boolean = false;
  isAdmin: boolean = false;
  roomState?: RoomState;
  roundState?: RoundState;
  lruCacheService = inject(LruCacheService);
  clueSelectedLruCache: LRUCache<any>;
  initialRenderComplete: boolean = false;

  constructor() {
    this.clueSelectedLruCache = this.lruCacheService.getOrCreateCache(
      'selectedClues'
    );
  }

  ngOnChanges(): void {
    this.roomState = this.room!.gameState.gameProgress as RoomState;
    this.roundState = this.roomState.roundStates[this.roomState.roundIdx];
    this.isAdmin = this.authService.getUser()?.uid === this.room?.host;
    this.isGamemasterMode = this.room?.mode === GameMode.GAMEMASTER;

    // Set flag after first render to prevent animations on subsequent renders
    setTimeout(() => {
      this.initialRenderComplete = true;
    }, 1000); // Wait for animations to complete
  }

  getClueKey(categoryIdx: number, clueIdx: number) {
    return `${this.room?.roomId}-${categoryIdx}-${clueIdx}`;
  }
  isClueUnopened(categoryIdx: number, clueIdx: number): boolean {
    if (this.clueSelectedLruCache.get(this.getClueKey(categoryIdx, clueIdx))) {
      return false;
    }
    return !this.roundState?.categoryStates[categoryIdx].clueStates[clueIdx]
      .clueComplete;
  }

  async selectClue(categoryIdx: number, clueIdx: number) {
    this.clueSelectedLruCache.set(this.getClueKey(categoryIdx, clueIdx), {});
    try {
      await this.gameService.call('selectClue', {
        roomId: this.room!.roomId,
        roundIdx: this.roomState?.roundIdx!,
        categoryIdx,
        clueIdx
      });
    } catch {
      this.clueSelectedLruCache.set(
        this.getClueKey(categoryIdx, clueIdx),
        undefined
      );
    }
  }

  isPending(categoryIdx: number, clueIdx: number) {
    return (
      this.roundState?.currentCategoryIdx == categoryIdx &&
      this.roundState?.currentClueIdx == clueIdx
    );
  }
  getClueState(categoryIdx: number, clueIdx: number) {
    return this.roundState?.categoryStates[categoryIdx].clueStates[clueIdx];
  }
  getClue(categoryIdx: number, clueIdx: number) {
    return this.round?.categories[categoryIdx].clues[clueIdx];
  }

  getPlayerName(playerId: string) {
    return this.room?.playerInfos[
      this.room?.playerIds.findIndex(p => p == playerId)!
    ].userName;
  }

  getClueTooltip(categoryIdx: number, clueIdx: number): string {
    if (!this.isClueUnopened(categoryIdx, clueIdx)) {
      if (this.isPending(categoryIdx, clueIdx)) {
        return 'This clue is currently active';
      }

      const clueState = this.getClueState(categoryIdx, clueIdx);
      if (clueState?.answeredByPlayerId) {
        const playerName = this.getPlayerName(clueState.answeredByPlayerId);
        return `Answered correctly by ${playerName}`;
      } else {
        return 'No one answered this clue correctly';
      }
    }

    return `Select this ${
      this.getClue(categoryIdx, clueIdx)?.value
    } point clue`;
  }
}
