.timer-container {
  position: relative;
  width: 120px; /* Adjust as needed */
  height: 120px; /* Adjust as needed */
  margin: 0 auto; /* Center the container */
}

.time-remaining {
  font-size: 24px; /* Make the seconds bigger */
  font-weight: bold;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--timer-stroke-color, #007bff);
}
.timer-track {
  stroke: var(--timer-track-color, #eee);
}

.progress-circle {
  stroke: var(--timer-stroke-color, #007bff);
}
