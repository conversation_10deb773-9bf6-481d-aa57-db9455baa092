<div class="timer-container">
  <svg width="120" height="120" viewBox="0 0 120 120">
    <circle
      class="timer-track"
      cx="60"
      cy="60"
      r="45"
      fill="none"
      stroke-width="10"
    />
    <circle
      class="progress-circle"
      cx="60"
      cy="60"
      r="45"
      fill="none"
      stroke-width="10"
      [attr.stroke-dasharray]="circumference"
      [attr.stroke-dashoffset]="progress"
      transform="rotate(-90 60 60)"
    />
  </svg>
  <div class="time-remaining">{{ remainingTime }}</div>
</div>
