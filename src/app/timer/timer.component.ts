import {
  Component,
  Input,
  OnInit,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Changes,
  OnChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-timer',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './timer.component.html',
  styleUrls: ['./timer.component.css'],
})
export class TimerComponent implements OnInit, OnDestroy, OnChanges {
  @Input() targetTime: number | undefined;
  @Input() initialTime: number | undefined;

  remainingTime: string = '00:00'; // Use a string to display minutes:seconds
  circumference: number = 2 * Math.PI * 45;
  progress: number = 0;
  private intervalId: any;

  constructor() {}

  ngOnInit(): void {
    this.startTimer();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.targetTime) {
      this.clearTimer();
      this.startTimer();
    }
  }

  ngOnDestroy(): void {
    this.clearTimer();
  }

  private startTimer(): void {
    if (!this.targetTime) {
      return;
    }

    this.intervalId = setInterval(() => {
      this.updateProgressAndRemainingTime();
    }, 50);
  }

  private clearTimer(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  private updateProgressAndRemainingTime(): void {
    if (!this.targetTime) {
      return;
    }

    const now = new Date();
    const diff = this.targetTime - now.getTime();
    const initialDiff = this.targetTime - this.initialTime!;

    if (diff <= 0) {
      this.remainingTime = '00:00';
      this.progress = this.circumference;
      this.clearTimer();
      return;
    }

    const minutes = Math.floor(diff / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    // Format remaining time as MM:SS
    this.remainingTime = `${this.pad(minutes)}:${this.pad(seconds)}`;

    this.progress = (1 - diff / initialDiff) * this.circumference;
  }

  // Helper function to add leading zeros
  private pad(num: number): string {
    return num < 10 ? `0${num}` : `${num}`;
  }
}
