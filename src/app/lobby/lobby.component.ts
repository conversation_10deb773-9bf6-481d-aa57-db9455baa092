import { Component, inject, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { GameService } from '../services/game.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Room } from '../../../functions/src/resources';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatListModule } from '@angular/material/list';
import { LRUCache, LruCacheService } from '../services/lru-cache.service';
import { CommonModule } from '@angular/common';
import moment from 'moment';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { TopBarComponent } from '../top-bar/top-bar.component';
import { AuthService } from '../services/auth.service';

@Component({
  selector: 'app-lobby',
  imports: [
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatTabsModule,
    MatButtonModule,
    MatSelectModule,
    CommonModule,
    MatListModule,
    MatProgressBarModule,
    TopBarComponent
  ],
  standalone: true,
  templateUrl: './lobby.component.html',
  styleUrl: './lobby.component.css'
})
export class LobbyComponent implements OnInit {
  username: string = '';
  roomCode: string = '';
  selectedMode: string = 'multiplayer';
  route = inject(ActivatedRoute);
  gameService: GameService = inject(GameService);
  authService: AuthService = inject(AuthService);
  router: Router = inject(Router);
  selectedTabIndex = 0;
  lruCacheService: LruCacheService = inject(LruCacheService);
  joinedRoomCache: LRUCache<Room>;
  disableButton = false;
  showProgressBar = false;

  constructor() {
    this.joinedRoomCache = this.lruCacheService.getOrCreateCache(
      'joinedRooms'
    ) as LRUCache<Room>;
  }

  getRelativeTime(ts: number): string {
    return moment(ts).fromNow();
  }
  ngOnInit(): void {
    if (localStorage.getItem('username')) {
      this.username = localStorage.getItem('username')!;
    }
    this.route.queryParams.subscribe(params => {
      const tabIndex = params['tab'];
      if (tabIndex) {
        this.selectedTabIndex = parseInt(tabIndex, 10);
      }
    });
    this.route.params.subscribe(params => {
      this.roomCode = params['roomId'];
      if (this.roomCode) {
        this.selectedTabIndex = 1;
        if (this.username) {
          this.onJoinRoom(this.roomCode);
        }
      }
    });
  }

  updateUserName() {
    if (this.username.length > 0) {
      localStorage.setItem('username', this.username);
    }
  }

  onCreateRoom() {
    if (!this.username.trim()) {
      alert('Please enter a username');
      return;
    }
    this.showProgressBar = true;
    this.disableButton = true;
    console.log(this.authService.user);
    const payload = {
      userName: this.username,
      mode: this.selectedMode,
      photoUrl: this.authService.user?.photoUrl
    };
    this.gameService
      .call('createRoom', payload)
      .then((response: Room) => {
        const roomId = response.roomId;
        this.joinedRoomCache?.set(response.roomId, response);
        this.router.navigate(['/room', roomId]);
      })
      .catch((error: Error) => {
        console.error('Error creating room:', error);
      });
  }

  onJoinRoom(roomId: string) {
    if (!this.username.trim()) {
      alert('Please enter a username');
      return;
    }

    this.showProgressBar = true;
    this.disableButton = true;
    this.gameService
      .call('joinRoom', {
        roomId,
        userName: this.username,
        photoUrl: this.authService.user?.photoUrl
      })
      .then(response => {
        console.log(response);
        this.joinedRoomCache?.set(response.roomId, response);
        this.router.navigate(['/room', roomId]);
      })
      .catch((error: Error) => {
        console.error('Error joining room:', error);
      });
  }

  onRejoinRoom(roomId: string) {
    this.showProgressBar = true;
    this.disableButton = true;
    this.router.navigate(['/room', roomId]);
  }

  onTabChange(event: MatTabChangeEvent): void {
    this.selectedTabIndex = event.index;
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { tab: this.selectedTabIndex },
      queryParamsHandling: 'merge' // Preserve existing query params
    });
  }
}
