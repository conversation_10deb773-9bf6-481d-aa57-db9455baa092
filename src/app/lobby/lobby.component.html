<app-top-bar></app-top-bar>

<div class="page-container">
  <mat-form-field class="username-input">
    <mat-label>Enter your name</mat-label>
    <input
      matInput
      type="text"
      [(ngModel)]="username"
      (input)="updateUserName()"
      (blur)="updateUserName()"
    />
  </mat-form-field>
  <mat-progress-bar
    *ngIf="showProgressBar"
    mode="indeterminate"
  ></mat-progress-bar>

  <mat-tab-group
    class="room-tabs"
    mat-align-tabs="center"
    [selectedIndex]="selectedTabIndex"
    (selectedTabChange)="onTabChange($event)"
  >
    <mat-tab label="Create a Room">
      <div class="tab-content">
        <mat-form-field class="mode-selector">
          <mat-label>Select Mode</mat-label>
          <mat-select [(value)]="selectedMode">
            <mat-option value="multiplayer">Multiplayer Mode</mat-option>
            <mat-option value="gamemaster">Gamemaster Mode</mat-option>
          </mat-select>
        </mat-form-field>
        <button
          mat-raised-button
          color="primary"
          (click)="onCreateRoom()"
          [disabled]="disableButton"
        >
          Create Room
        </button>
      </div>
    </mat-tab>
    <mat-tab label="Join a Room">
      <div class="tab-content">
        <mat-form-field class="room-code-input">
          <mat-label>Enter Room Code</mat-label>
          <input matInput type="text" [(ngModel)]="roomCode" />
        </mat-form-field>
        <button
          mat-raised-button
          (click)="onJoinRoom(this.roomCode)"
          [disabled]="disableButton"
        >
          Join Room
        </button>
      </div>
    </mat-tab>
    <mat-tab label="Re-join a room">
      <div class="tab-content">
        <mat-action-list class="rejoin-room-list">
          <div
            *ngFor="
              let room of joinedRoomCache
                .list()
                .slice()
                .reverse()
            "
          >
            <button
              mat-list-item
              (click)="onRejoinRoom(room.roomId)"
              [disabled]="disableButton"
            >
              <div class="rejoin-room-button">
                <div>{{ room.roomId }}</div>
                <i
                  >Created {{ getRelativeTime(room.createdAt) }}
                  {{
                    room.playerInfos.length > 0
                      ? "by " + room.playerInfos.at(0)!.userName
                      : ""
                  }}</i
                >
              </div>
            </button>
            <mat-divider></mat-divider>
          </div>
        </mat-action-list>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>
