.page-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-top: 20%;
  background-color: var(--mat-sys-surface);
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.username-input {
  width: 300px; /* Adjust width as needed */
}

.room-tabs {
  width: 500px; /* Adjust width as needed */
}

.tab-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center; /* Center content horizontally within tabs */
  gap: 20px;
}

.room-code-input {
  width: 100%;
}

.action-button {
  width: 200px; /* Adjust button width as needed */
}
.rejoin-room-list {
  width: 100%;
}
.rejoin-room-button {
  display: flex;
  justify-content: space-between;
}
