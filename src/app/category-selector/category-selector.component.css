.category-selector-card {
  background-color: var(--mat-sys-secondary-container);
  width: 100%;
  overflow: hidden;
  border-radius: 1rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  /* No hover animation on the outer container as requested */
}

/* Removed hover animation */

mat-card-header {
  padding: 0.75rem 1rem 0.5rem;
  color: var(--mat-sys-on-secondary-container);
}

.title-container {
  display: flex;
  align-items: center;
  width: 100%; /* Ensure it takes full width */
  margin-bottom: 0.25rem;
}

.title-and-spinner {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-all-button {
  color: var(--mat-sys-on-secondary-container);
  opacity: 0.7;
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.refresh-all-button:hover {
  opacity: 1;
  transform: rotate(30deg);
}

mat-card-content {
  padding: 0.5rem 1rem 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Spawning categories container */
.spawning-categories-container {
  display: grid;
  grid-template-columns: repeat(
    4,
    1fr
  ); /* Reduced from 8 to 4 columns for wider chips */
  grid-auto-rows: minmax(3.5rem, auto); /* Set minimum row height */
  gap: 0.5rem; /* Gap between grid items */
  height: auto; /* Allow height to adjust based on content */
  min-height: 3.75rem; /* Minimum height */
  width: 100%; /* Fixed width to prevent container resizing */
  min-width: 100%; /* Ensure it takes full width */
  max-width: 100%; /* Prevent overflow */
  padding: 0.75rem;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 0.75rem;
  position: relative;
  overflow-x: auto; /* Allow horizontal scrolling if needed */
  overflow-y: auto; /* Allow vertical scrolling if needed */
  scrollbar-width: thin; /* Thin scrollbar for Firefox */
  scrollbar-color: rgba(var(--mat-sys-primary-rgb), 0.3) transparent; /* Scrollbar color for Firefox */
}

/* Webkit scrollbar styling */
.spawning-categories-container::-webkit-scrollbar {
  height: 4px;
}

.spawning-categories-container::-webkit-scrollbar-track {
  background: transparent;
}

.spawning-categories-container::-webkit-scrollbar-thumb {
  background-color: rgba(var(--mat-sys-primary-rgb), 0.3);
  border-radius: 20px;
}

/* Add subtle pattern background */
.spawning-categories-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 20% 30%,
      rgba(var(--mat-sys-primary-rgb), 0.05) 0%,
      transparent 8%
    ),
    radial-gradient(
      circle at 75% 20%,
      rgba(var(--mat-sys-tertiary-rgb), 0.05) 0%,
      transparent 8%
    ),
    radial-gradient(
      circle at 40% 80%,
      rgba(var(--mat-sys-primary-rgb), 0.05) 0%,
      transparent 8%
    ),
    radial-gradient(
      circle at 80% 70%,
      rgba(var(--mat-sys-tertiary-rgb), 0.05) 0%,
      transparent 8%
    );
  background-size: 150% 150%;
  animation: particleMove 15s infinite ease-in-out alternate;
  z-index: 0;
  opacity: 0.7;
}

@keyframes particleMove {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
}

.category-chip-wrapper {
  z-index: 1;
  height: auto; /* Allow height to adjust based on content */
  min-height: 3.5rem; /* Minimum height for two-line chips */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%; /* Take full width of grid cell */
  min-width: 0; /* Allow the cell to shrink if needed */
  box-sizing: border-box; /* Include padding in width calculation */
  padding: 0.25rem 0; /* Add vertical padding */
}

.category-chip {
  padding: 0.6rem 0.8rem;
  border-radius: 2rem;
  font: var(--mat-sys-label-large); /* Increased font size */
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease,
    box-shadow 0.3s ease;
  /* Remove color from transition to make text color change immediate */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between; /* Space between text and reset button */
  width: 100%; /* Take full width of wrapper */
  max-width: 100%; /* Ensure it doesn't overflow */
  min-height: 3rem; /* Increased minimum height for two lines */
  height: auto; /* Allow height to grow based on content */
  position: relative;
  overflow: hidden;
  white-space: normal; /* Allow text wrapping */
  line-height: 1.3; /* Slightly increased line height for better readability */
  animation: simpleFadeIn 0.2s ease forwards; /* Simpler animation */
}

/* Chip content and reset button */
.chip-content {
  flex: 1;
  text-align: center;
  padding-right: 0.5rem; /* Space for the reset button */
  transition: transform 0.3s ease; /* Only transition transform, not color */
  animation: simpleFadeIn 0.2s ease forwards; /* Apply the same animation to the text */
}

/* Ensure text color changes immediately on hover */
.category-chip:hover .chip-content {
  transition: none; /* No transition for text color */
}

.reset-button {
  width: 1.25rem;
  height: 1.25rem;
  min-width: 1.25rem; /* Ensure consistent width */
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.1s ease, background-color 0.1s ease;
}

.reset-button:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.2);
}

.reset-icon {
  font-size: 0.8rem;
  width: 0.8rem;
  height: 0.8rem;
  line-height: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Shine effect on hover */
.category-chip::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.5) 50%,
    /* Increased opacity from 0.1 to 0.5 */ rgba(255, 255, 255, 0) 100%
  );
  /* Add a subtle shadow to enhance visibility on light backgrounds */
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  transform: rotate(45deg);
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden; /* Ensure it's completely hidden when not animating */
}

.category-chip:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  transition: background-color 0.3s ease, transform 0.3s ease,
    box-shadow 0.3s ease;
  /* Remove color from transition to make text color change immediate */
}

.category-chip:hover::after {
  animation: shine 1.5s ease forwards; /* Added 'forwards' to maintain the final state */
  opacity: 1; /* Ensure the shine effect is visible during animation */
  visibility: visible; /* Make it visible during animation */
}

@keyframes shine {
  0% {
    left: -100%;
    opacity: 0;
  }
  10% {
    opacity: 0.7; /* Increased from 0.5 to 0.7 */
  }
  50% {
    opacity: 0.9; /* Added midpoint with high opacity */
  }
  99% {
    left: 100%;
    opacity: 0.1;
  }
  100% {
    left: 100%;
    opacity: 0;
    visibility: hidden; /* Completely hide at the end */
  }
}

/* Color variations for chips */
.primary-chip {
  background-color: var(--mat-sys-primary-container);
  color: var(--mat-sys-on-primary-container);
}

.secondary-chip {
  background-color: var(--mat-sys-secondary-container);
  color: var(--mat-sys-on-secondary-container);
}

.tertiary-chip {
  background-color: var(--mat-sys-tertiary-container);
  color: var(--mat-sys-on-tertiary-container);
}

.success-chip {
  background-color: #d4edda;
  color: #155724;
}

.warning-chip {
  background-color: #fff3cd;
  color: #856404;
}

.info-chip {
  background-color: #d1ecf1;
  color: #0c5460;
}

/* Add special hover effect for each color with smoother transitions */
.primary-chip:hover {
  background-color: var(--mat-sys-primary);
  color: var(--mat-sys-on-primary);
}

.secondary-chip:hover {
  background-color: var(--mat-sys-secondary);
  color: var(--mat-sys-on-secondary);
}

.tertiary-chip:hover {
  background-color: var(--mat-sys-tertiary);
  color: var(--mat-sys-on-tertiary);
}

/* Use darker versions of the colors but keep them in the same family for smoother transitions */
.success-chip:hover {
  background-color: #1e7e34; /* Slightly lighter than before */
  color: #ffffff;
}

.warning-chip:hover {
  background-color: #d39e00; /* Lighter gold color */
  color: #1f1600; /* Dark text on light background */
}

.info-chip:hover {
  background-color: #138496; /* Lighter teal color */
  color: #ffffff;
}

/* Enhanced shine effect for light-colored chips */
.success-chip::after,
.warning-chip::after,
.info-chip::after {
  /* Add a darker gradient for light chips */
  background: linear-gradient(
    to bottom right,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0) 100%
  );
  /* This creates a dual-tone effect that's visible on light backgrounds */
  mix-blend-mode: multiply;
}

/* Selected categories section */
.selected-categories-container {
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 0.75rem;
}

.selected-categories-container h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--mat-sys-on-primary-container);
  font: var(--mat-sys-title-medium);
}

.selected-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.selected-chip {
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.remove-button {
  width: 1.5rem;
  height: 1.5rem;
  line-height: 1.5rem;
  padding: 0;
}

.remove-button mat-icon {
  font-size: 1rem;
  width: 1rem;
  height: 1rem;
  line-height: 1rem;
}

/* Color legend styles */
.compact-subtitle {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
  line-height: 1.5;
}

.legend-item {
  padding: 0.15rem 0.4rem;
  border-radius: 1rem;
  white-space: nowrap;
  font-size: 0.7rem;
  display: inline-flex;
  align-items: center;
}

/* Animation classes with more specific targeting */
.fade-in {
  animation: simpleFadeIn 0.2s ease forwards;
}

.fade-out {
  animation: simpleFadeOut 0.2s ease forwards;
}

/* Text content animation classes */
.text-fade-in .chip-content {
  animation: textFadeIn 0.3s ease forwards;
}

.text-fade-out .chip-content {
  animation: textFadeOut 0.3s ease forwards;
}

/* Chip animations */
@keyframes simpleFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes simpleFadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.9);
  }
}

/* Text content animations */
@keyframes textFadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes textFadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(5px);
  }
}

/* Ensure animations complete properly */
.category-chip {
  transition: all 0.3s ease;
  will-change: opacity, transform;
}

/* Hover effect */
.category-chip:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
/* Tablet layout */
@media (min-width: 601px) and (max-width: 900px) {
  .spawning-categories-container {
    grid-template-columns: repeat(2, 1fr); /* 2 columns on tablet */
    grid-auto-rows: minmax(3rem, auto); /* Automatic row sizing */
    min-height: 7rem; /* Minimum height for tablet */
    gap: 0.8rem; /* Increased gap to prevent overlapping */
  }

  .category-chip {
    font: var(--mat-sys-label-small); /* Slightly smaller font on tablet */
    min-height: 2.2rem;
  }

  .category-chip-wrapper {
    min-height: 3rem;
    padding: 0.2rem 0;
  }
}

/* Mobile layout */
@media (max-width: 600px) {
  .spawning-categories-container {
    min-height: auto; /* Let height adjust to content */
    grid-template-columns: repeat(
      1,
      1fr
    ); /* Single column on mobile for better readability */
    grid-auto-rows: minmax(3rem, auto); /* Taller rows for better readability */
    gap: 0.4rem; /* Reduced gap for more compact layout */
    padding: 0.5rem;
    overflow-y: auto; /* Allow vertical scrolling if needed */
  }

  .category-chip {
    padding: 0.4rem 0.7rem;
    font: var(
      --mat-sys-body-medium
    ); /* Larger font on mobile for better readability */
    border-radius: 1.25rem;
    min-height: 2.2rem; /* Adjusted height for better touch targets while being compact */
    line-height: 1.3;
  }

  .reset-button {
    width: 1.5rem;
    height: 1.5rem;
    min-width: 1.5rem;
    margin-left: 0.5rem;
  }

  .reset-icon {
    font-size: 1rem;
    width: 1rem;
    height: 1rem;
  }

  .compact-subtitle {
    font-size: 0.65rem;
    gap: 0.3rem;
  }

  .legend-item {
    padding: 0.1rem 0.25rem;
    font-size: 0.65rem;
  }

  .category-chip-wrapper {
    min-height: 2.8rem; /* Adjusted minimum height for mobile */
    padding: 0.1rem 0;
    margin-bottom: 0;
  }

  mat-card-header {
    padding: 0.4rem 0.6rem 0.2rem;
  }

  mat-card-content {
    padding: 0.2rem 0.6rem 0.5rem;
  }
}

/* TV layout (16:9 aspect ratio) */
@media screen and (min-aspect-ratio: 16/9) and (max-aspect-ratio: 16/9) {
  .category-selector-card {
    max-height: 300px;
  }

  .spawning-categories-container {
    min-height: 8rem;
    max-height: 200px;
    grid-template-columns: repeat(3, 1fr);
    grid-auto-rows: minmax(2.5rem, auto);
    gap: 0.5rem;
    padding: 0.5rem;
    overflow-y: auto;
  }

  .category-chip {
    padding: 0.3rem 0.5rem;
    font: var(--mat-sys-label-small);
    min-height: 2rem;
  }

  mat-card-header {
    padding: 0.5rem 0.75rem 0.25rem;
  }

  mat-card-content {
    padding: 0.25rem 0.5rem 0.5rem;
  }
}

/* Cast mode specific styles */
.is-cast .category-selector-card {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0;
  border-radius: 0.5rem;
  max-width: 600px;
  margin: 0 auto;
  padding: 0.25rem;
}

.is-cast mat-card-header {
  flex: 0 0 auto;
  padding: 0.25rem;
  min-height: auto;
  width: auto;
}

.is-cast .title-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: auto;
  margin: 0;
  padding: 0;
}

.is-cast .mat-headline-small {
  font-size: 0.9rem;
  margin: 0;
  white-space: nowrap;
}

.is-cast .refresh-all-button {
  transform: scale(0.9);
}

.is-cast mat-card-content {
  flex: 1 1 auto;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  min-height: auto;
  margin: 0;
}

.is-cast .spawning-categories-container {
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem;
  width: 100%;
  min-height: auto;
  height: auto;
  overflow-x: hidden;
}

.is-cast .category-chip {
  padding: 0.25rem 0.5rem;
  font-size: clamp(0.7rem, 1.5vw, 0.9rem);
  border-radius: 1rem;
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
}

.is-cast .reset-button {
  margin-left: 0.25rem;
}

/* Fix for Material icons in cast mode */
.is-cast .mat-icon {
  height: auto !important;
  width: auto !important;
  font-size: 1em !important;
  line-height: 1 !important;
  overflow: visible !important;
}

/* Read-only mode styling */
.info-icon {
  cursor: help;
  opacity: 0.7;
  margin-right: 0.5rem;
  font-size: 1em;
  height: 1em;
  width: 1em;
  position: relative;
  color: var(--mat-sys-info);
  vertical-align: middle;
}

.read-only-message {
  display: flex;
  align-items: center;
  color: var(--mat-sys-on-surface-variant);
  font-style: italic;
}
