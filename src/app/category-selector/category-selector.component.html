<mat-card class="category-selector-card" [class.is-cast]="isCast">
  <mat-card-header>
    <mat-card-title>
      <div class="title-container">
        <div class="title-and-spinner">
          Category Inspiration
        </div>
        <button
          mat-icon-button
          *ngIf="!isCast"
          class="refresh-all-button"
          (click)="refreshAllCategories()"
          [matTooltip]="'Refresh all categories'"
          aria-label="Refresh all categories"
        >
          <mat-icon>refresh</mat-icon>
        </button>
      </div>
    </mat-card-title>
    <mat-card-subtitle class="mat-body-small" *ngIf="!isCast">
      <ng-container *ngIf="!isReadOnly">
        Click on a category to add it to your game
      </ng-container>
      <ng-container *ngIf="isReadOnly" class="read-only-message">
        <mat-icon class="info-icon">visibility</mat-icon>
        Category suggestions are for inspiration only. Only the host can add
        categories.
      </ng-container>
    </mat-card-subtitle>
  </mat-card-header>

  <mat-card-content>
    <!-- Spawning categories container -->
    <div class="spawning-categories-container">
      <div
        *ngFor="
          let category of spawnedCategories;
          let i = index;
          trackBy: trackByIndex
        "
        class="category-chip-wrapper fade-in text-fade-in"
      >
        <div
          class="category-chip {{ getChipColorClass(i) }}"
          (click)="addCategory(category, i)"
        >
          <span class="chip-content">{{ category }}</span>
          <button
            class="reset-button"
            (click)="resetChip(i, $event)"
            [matTooltip]="
              isReadOnly ? 'Show a different suggestion' : 'Reset this category'
            "
            [attr.aria-label]="
              isReadOnly ? 'Show a different suggestion' : 'Reset category'
            "
          >
            <mat-icon class="reset-icon">close</mat-icon>
          </button>
        </div>
      </div>
    </div>

    <!-- We've removed the selected categories section as it's redundant with the categories list below -->
  </mat-card-content>
</mat-card>
