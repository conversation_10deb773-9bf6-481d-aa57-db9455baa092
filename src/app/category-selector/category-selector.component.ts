import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnD<PERSON>roy,
  OnInit,
  Output
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
// Removed import for DynamicListInputComponent as it's not used
import { GameService } from '../services/game.service';
// Removed animation imports as we're using CSS animations
import { SubCategory } from '../../../functions/src/services';

@Component({
  selector: 'app-category-selector',
  templateUrl: './category-selector.component.html',
  styleUrls: ['./category-selector.component.css'],
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatTooltipModule,
    MatProgressSpinnerModule
    // Removed DynamicListInputComponent as it's not used in this component
  ],
  // Removed animations from component - using CSS animations instead
  animations: [],
  standalone: true
})
export class CategorySelectorComponent implements OnInit, OnDestroy {
  @Output() emitSubCategories = new EventEmitter<SubCategory>();
  @Input() isReadOnly: boolean = false;
  @Input() isCast: boolean = false;
  // We don't need the DynamicListInputComponent reference anymore

  gameService = inject(GameService);

  // Arrays to hold categories
  selectedCategories: string[] = [];
  spawnedCategories: string[] = [];

  // Category components for generating combinations
  private categoryComponents = {
    // Subjects/Themes
    subjects: [
      // Entertainment
      'Movies',
      'TV Shows',
      'Music',
      'Books',
      'Video Games',
      'Sports',
      'Animation',
      'Documentaries',
      'Podcasts',
      'Streaming Services',
      'Reality TV',
      'Comedy',
      'Drama',
      'Horror',
      'Sci-Fi',
      'Fantasy',
      'Thrillers',
      'Musicals',
      'Concerts',
      'Festivals',
      'Award Shows',
      'Celebrities',
      'Directors',
      'Authors',
      'Musicians',
      'Actors',
      'Comedians',

      // Academic
      'History',
      'Science',
      'Geography',
      'Art',
      'Mathematics',
      'Literature',
      'Astronomy',
      'Biology',
      'Chemistry',
      'Physics',
      'Psychology',
      'Sociology',
      'Anthropology',
      'Economics',
      'Political Science',
      'Philosophy',
      'Linguistics',
      'Archaeology',
      'Geology',
      'Oceanography',
      'Meteorology',
      'Neuroscience',
      'Computer Science',
      'Engineering',
      'Medicine',
      'Law',
      'Education',
      'Ethics',
      'Logic',

      // Languages & Linguistics
      'Etymology',
      'Sanskrit',
      'Hindi',
      'Latin',
      'Ancient Greek',
      'Mandarin',
      'Japanese',
      'Arabic',
      'Spanish',
      'French',
      'German',
      'Russian',
      'Idioms',
      'Proverbs',
      'Slang',
      'Palindromes',
      'Anagrams',
      'Portmanteaus',
      'Dialects',
      'Accents',
      'Translation',
      'Sign Language',
      'Constructed Languages',
      'Dead Languages',
      'Language Evolution',
      'Writing Systems',
      'Alphabets',
      'Grammar',
      'Phonetics',
      'Semantics',

      // Lifestyle
      'Food & Drink',
      'Fashion',
      'Architecture',
      'Interior Design',
      'Gardening',
      'Cooking',
      'Baking',
      'Mixology',
      'Fitness',
      'Wellness',
      'Meditation',
      'Yoga',
      'Travel',
      'Home Decor',
      'DIY Projects',
      'Crafts',
      'Photography',
      'Social Media',
      'Dating',
      'Relationships',
      'Parenting',
      'Pets',
      'Hobbies',
      'Collecting',
      'Minimalism',
      'Sustainability',

      // Nature & Science
      'Animals',
      'Plants',
      'Weather',
      'Natural Disasters',
      'Oceans',
      'Mountains',
      'Deserts',
      'Rainforests',
      'Ecosystems',
      'Endangered Species',
      'Conservation',
      'Climate Change',
      'Renewable Energy',
      'Space Exploration',
      'Planets',
      'Stars',
      'Galaxies',
      'Black Holes',
      'Quantum Physics',
      'Genetics',
      'Evolution',
      'Microbiology',
      'Artificial Intelligence',
      'Robotics',
      'Nanotechnology',
      'Biotechnology',

      // Decade-Specific Media & Culture
      '60s Music',
      '70s Music',
      '80s Music',
      '90s Music',
      '2000s Music',
      '2010s Music',
      '60s Movies',
      '70s Movies',
      '80s Movies',
      '90s Movies',
      '2000s Movies',
      '2010s Movies',
      '60s TV',
      '70s TV',
      '80s TV',
      '90s TV',
      '2000s TV',
      '2010s TV',
      '80s Cartoons',
      '90s Cartoons',
      '2000s Cartoons',
      '80s Fashion',
      '90s Fashion',
      '2000s Fashion',
      '80s Technology',
      '90s Technology',
      '2000s Technology',
      '90s Toys',
      '90s Video Games',
      '2000s Video Games',
      '90s Sitcoms',
      '90s Action Movies',
      '90s Hip Hop',
      '90s Pop',
      '90s Rock',
      '90s Anime',
      '90s Commercials',
      '90s Snacks',
      '2000s Reality TV',
      '2000s Internet',
      '2000s Social Media',
      '2010s Memes',
      '2010s Viral Videos',
      '2010s Apps',

      // Indian Decade-Specific
      '80s Bollywood',
      '90s Bollywood',
      '2000s Bollywood',
      '90s Doordarshan',
      '80s Indian Music',
      '90s Indian Music',
      '2000s Indian Music',
      '90s Indian Cartoons',
      '90s Indian Ads',
      '90s Indian Snacks',
      '2000s Indian TV',

      // Other
      'Celebrities',
      'Technology',
      'Politics',
      'Mythology',
      'Folklore',
      'Urban Legends',
      'Conspiracy Theories',
      'Inventions',
      'Discoveries',
      'World Records',
      'Optical Illusions',
      'Paradoxes',
      'Puzzles',
      'Mysteries',
      'True Crime',
      'Paranormal',
      'Supernatural',
      'Religion',
      'Spirituality',
      'Traditions',
      'Customs',
      'Holidays',
      'Festivals',
      'Ceremonies',
      'Rituals',
      'Superstitions',
      'Etiquette',
      'Cultural Practices'
    ],

    // Specific franchises/themes
    franchises: [
      // Movie/TV franchises
      'Harry Potter',
      'Marvel',
      'Star Wars',
      'Game of Thrones',
      'Lord of the Rings',
      'Disney',
      'Pixar',
      'Star Trek',
      'DC Comics',
      'The Simpsons',
      'Friends',
      'Hollywood',
      'Broadway',
      'Breaking Bad',
      'Stranger Things',
      'The Office',
      'Parks and Recreation',
      'Doctor Who',
      'Sherlock',
      'Jurassic Park',
      'Fast & Furious',
      'James Bond',
      'Mission Impossible',
      'The Matrix',
      'Indiana Jones',
      'Back to the Future',
      'Toy Story',
      'Shrek',
      'How to Train Your Dragon',
      'Despicable Me',

      // Video game franchises
      'Mario',
      'Zelda',
      'Pokémon',
      'Minecraft',
      'Call of Duty',
      'Final Fantasy',
      'Grand Theft Auto',
      "Assassin's Creed",
      'Halo',
      'Fortnite',
      'League of Legends',
      'Dota 2',
      'World of Warcraft',
      'The Sims',
      'Sonic the Hedgehog',
      'Among Us',
      'Valorant',
      'Overwatch',
      'Genshin Impact',
      'Roblox',
      'Apex Legends',
      'Animal Crossing',
      'God of War',
      'Uncharted',
      'The Last of Us',
      'Resident Evil',
      'Metal Gear Solid',
      'Fallout',
      'The Elder Scrolls',
      'Civilization',

      // Indian Cinema
      'Bollywood',
      'Indian Cinema',
      'Indian Regional Cinema',
      'Indian Film Industry',
      'Indian Directors',
      'Indian Actors',
      'Indian Film Music',
      'Indian Film Awards',
      'Indian Film History',
      'Indian Film Genres',

      // Sports/Awards
      'Olympics',
      'World Cup',
      'Cricket World Cup',
      'IPL Teams',
      'Super Bowl',
      'Grammy Awards',
      'Oscars',
      'Filmfare Awards',
      'FIFA',
      'NBA',
      'NFL',
      'MLB',
      'NHL',
      'Tennis Grand Slams',
      'Formula 1',
      'UFC',
      'WWE',
      'Commonwealth Games',
      'Asian Games',
      'Emmy Awards',
      'Golden Globe Awards',
      'BAFTA Awards',
      'Tony Awards',
      'Cannes Film Festival',

      // Indian Tech & Culture
      'Indian Tech Companies',
      'Indian Tech CEOs',
      'Indian Startups',
      'Indian Regional Cuisines',
      'Indian Classical Music',
      'Indian Festivals',
      'Indian TV Commercials',
      'Indian Street Foods',
      'Indian Classical Dance',
      'Indian Mythology',
      'Indian Literature',
      'Indian Cinema Movements',
      'Indian Independence Movement',
      'Indian Folk Art',
      'Indian Architecture',
      'Indian Fashion Designers',
      'Indian Scientists',
      'Indian Freedom Fighters',
      'Indian Entrepreneurs',
      'Indian Sports Legends',
      'Indian Television Shows',
      'Indian Web Series',
      'Indian Stand-up Comedy',
      'Indian Music Genres',
      'Indian Traditional Games'
    ],

    // Time periods
    timePeriods: [
      '80s',
      '90s',
      '2000s',
      '2010s',
      '2020s',
      'Ancient',
      'Medieval',
      'Renaissance',
      'Modern',
      'Classical',
      'Prehistoric',
      'Future',
      'Victorian',
      'World War II',
      'Cold War',
      'Space Age',
      'Digital Age',
      'Stone Age',
      'Bronze Age',
      'Iron Age',
      'Ancient Egypt',
      'Ancient Greece',
      'Ancient Rome',
      'Byzantine Empire',
      'Middle Ages',
      'Industrial Revolution',
      'Roaring Twenties',
      'Great Depression',
      'Post-War Era',
      'Information Age',
      'Vedic Period',
      'Mughal Era',
      'British Raj',
      'Post-Independence India',
      'Y2K Era'
    ],

    // Question formats
    formats: [
      'Guess the {0} from {1}',
      'Name the {0} from {1}',
      'Identify the {0} from {1}',
      'Match the {0} to {1}',
      '{0} Trivia',
      'Famous {0}',
      '{0} in {1}',
      '{0} vs {1}',
      'Hidden {0} in {1}',
      '{0} Quotes',
      'Who Said It in {0}',
      'Before & After: {0}',
      'Fill in the Blank: {0}',
      'True or False: {0}',
      'Multiple Choice: {0}',
      'Finish the {0}',
      '{0} Origins',
      '{0} Records',
      '{0} Facts',
      'Obscure {0}',
      'The Evolution of {0}',
      'Forgotten {0}',
      'Misunderstood {0}',
      'Surprising Facts About {0}',
      'The Science Behind {0}',
      'The History of {0}',
      'The Future of {0}',
      'Innovations in {0}',
      'Controversies in {0}',
      'Myths About {0} Debunked',
      'The Psychology of {0}',
      'The Art of {0}',
      'The Philosophy of {0}',
      'The Economics of {0}',
      'The Politics of {0}',
      'The Ethics of {0}',
      'The Mathematics of {0}',
      'The Physics of {0}',
      'The Chemistry of {0}',
      'The Biology of {0}',
      '{0} That Changed {1}',
      '{0} Inspired by {1}',
      '{0} Across Cultures',
      '{0} Through the Ages',
      '{0} Around the World',
      'The Most Influential {0}',
      'Underrated {0}',
      'Overrated {0}',
      'Bizarre {0}',
      'Unexplained {0}',
      'Mysterious {0}',
      'Legendary {0}',
      'Iconic {0}',
      'Revolutionary {0}',
      'Groundbreaking {0}',
      'Innovative {0}',
      'Disruptive {0}',
      'Transformative {0}',
      'Visionary {0}',
      'Pioneering {0}'
    ],

    // Elements to guess
    elements: [
      'Character',
      'Quote',
      'Scene',
      'Title',
      'Actor',
      'Director',
      'Lyrics',
      'Song',
      'Album',
      'Artist',
      'Band',
      'Logo',
      'Mascot',
      'Flag',
      'Country',
      'City',
      'Landmark',
      'Symbol',
      'Emoji',
      'Photo',
      'Voice',
      'Sound Effect',
      'Dialogue',
      'Tagline',
      'Slogan',
      'Invention',
      'Discovery',
      'Theory',
      'Concept',
      'Principle',
      'Law',
      'Formula',
      'Equation',
      'Algorithm',
      'Theorem',
      'Paradox',
      'Fallacy',
      'Phenomenon',
      'Effect',
      'Syndrome',
      'Disease',
      'Condition',
      'Treatment',
      'Remedy',
      'Cure',
      'Technique',
      'Method',
      'Process',
      'Procedure',
      'Protocol',
      'Standard',
      'Guideline',
      'Rule',
      'Regulation',
      'Policy',
      'Strategy',
      'Tactic',
      'Approach',
      'Perspective',
      'Viewpoint',
      'Opinion',
      'Belief',
      'Value',
      'Principle',
      'Ideology',
      'Philosophy',
      'Religion',
      'Mythology',
      'Legend',
      'Folklore',
      'Tradition',
      'Custom',
      'Ritual',
      'Ceremony',
      'Festival',
      'Holiday',
      'Celebration',
      'Event',
      'Occasion',
      'Milestone',
      'Achievement',
      'Accomplishment',
      'Success',
      'Failure',
      'Mistake',
      'Error',
      'Bug',
      'Glitch',
      'Problem',
      'Challenge',
      'Obstacle',
      'Barrier',
      'Limitation',
      'Constraint',
      'Restriction',
      'Requirement',
      'Specification',
      'Feature',
      'Function',
      'Capability',
      'Ability',
      'Skill',
      'Talent',
      'Gift',
      'Strength',
      'Weakness',
      'Opportunity',
      'Threat',
      'Risk',
      'Danger',
      'Hazard',
      'Warning',
      'Alert',
      'Notification',
      'Message',
      'Signal',
      'Sign',
      'Indicator',
      'Metric',
      'Measure',
      'Statistic',
      'Data',
      'Information',
      'Knowledge',
      'Wisdom',
      'Intelligence',
      'Insight',
      'Understanding',
      'Awareness',
      'Consciousness',
      'Perception',
      'Sensation',
      'Feeling',
      'Emotion',
      'Mood',
      'Attitude',
      'Behavior',
      'Action',
      'Reaction',
      'Response',
      'Stimulus',
      'Trigger',
      'Cause',
      'Effect',
      'Consequence',
      'Result',
      'Outcome',
      'Impact',
      'Influence',
      'Power',
      'Authority',
      'Control',
      'Leadership',
      'Management',
      'Administration',
      'Governance',
      'Government',
      'Politics',
      'Policy',
      'Regulation',
      'Law',
      'Rule',
      'Standard',
      'Guideline',
      'Principle',
      'Value',
      'Belief',
      'Ideology',
      'Philosophy',
      'Religion',
      'Spirituality',
      'Faith',
      'Hope',
      'Love',
      'Hate',
      'Fear',
      'Anger',
      'Joy',
      'Sadness',
      'Surprise',
      'Disgust',
      'Trust',
      'Distrust',
      'Confidence',
      'Doubt',
      'Certainty',
      'Uncertainty',
      'Risk',
      'Reward',
      'Cost',
      'Benefit',
      'Advantage',
      'Disadvantage',
      'Strength',
      'Weakness',
      'Opportunity',
      'Threat'
    ],

    // Clue types
    clues: [
      'Emojis',
      'Quotes',
      'Photos',
      'Silhouettes',
      'Hints',
      'Clues',
      'Descriptions',
      'First Line',
      'Last Line',
      'One Word',
      'Sounds',
      'Drawings',
      'Partial Images',
      'Voice',
      'Music',
      'Lyrics',
      'GIFs',
      'Anagrams',
      'Riddles',
      'Puzzles',
      'Crosswords',
      'Word Searches',
      'Sudoku',
      'Logic Puzzles',
      'Brain Teasers',
      'Optical Illusions',
      'Visual Puzzles',
      'Mathematical Puzzles',
      'Lateral Thinking Problems',
      'Paradoxes',
      'Thought Experiments',
      'Hypothetical Scenarios',
      'What-If Questions',
      'Fill-in-the-Blanks',
      'Multiple Choice',
      'True or False',
      'Matching',
      'Sorting',
      'Ranking',
      'Rating',
      'Voting',
      'Polling',
      'Surveying',
      'Interviewing',
      'Observing',
      'Experimenting',
      'Testing',
      'Measuring',
      'Calculating',
      'Computing',
      'Analyzing',
      'Synthesizing',
      'Evaluating',
      'Judging',
      'Critiquing',
      'Reviewing',
      'Assessing',
      'Appraising',
      'Valuing',
      'Appreciating',
      'Admiring',
      'Respecting',
      'Honoring',
      'Celebrating',
      'Commemorating',
      'Remembering',
      'Recalling',
      'Reminiscing',
      'Reflecting',
      'Contemplating',
      'Meditating',
      'Dreaming',
      'Imagining',
      'Visualizing',
      'Conceptualizing',
      'Ideating',
      'Brainstorming',
      'Mind Mapping',
      'Storyboarding',
      'Prototyping',
      'Modeling',
      'Simulating',
      'Animating',
      'Rendering',
      'Illustrating',
      'Drawing',
      'Sketching',
      'Painting',
      'Sculpting',
      'Crafting',
      'Building',
      'Constructing',
      'Engineering',
      'Designing',
      'Architecting',
      'Planning',
      'Organizing',
      'Arranging',
      'Structuring',
      'Formatting',
      'Styling',
      'Decorating',
      'Embellishing',
      'Enhancing',
      'Improving',
      'Optimizing',
      'Maximizing',
      'Minimizing',
      'Balancing',
      'Harmonizing',
      'Synchronizing',
      'Coordinating',
      'Collaborating',
      'Cooperating',
      'Partnering',
      'Allying',
      'Networking',
      'Connecting',
      'Linking',
      'Relating',
      'Associating',
      'Correlating',
      'Corresponding',
      'Matching',
      'Pairing',
      'Coupling',
      'Joining',
      'Uniting',
      'Merging',
      'Combining',
      'Integrating',
      'Synthesizing',
      'Blending',
      'Mixing',
      'Fusing'
    ]
  };

  // Some pre-generated interesting categories
  categorySuggestions = [
    // General formats
    'Guess the Movie from Emojis',
    'Name the Band from Lyrics',
    'Identify Countries from Flags',
    'Finish the Famous Movie Quote',
    'Name the 90s Song from First Line',
    'Identify the Brand Logo',
    'Guess the Year from Historical Events',
    'Guess the Movie from Plot Holes',
    'Identify the Song from Misheard Lyrics',
    'Name the Celebrity from Childhood Photos',
    'Guess the Book from First Sentence',
    'Identify the Painting from Details',
    'Name the Scientist from Discoveries',
    'Guess the TV Show from Character Descriptions',
    'Identify the City from Landmarks',
    'Name the Invention from Failed Prototypes',

    // Etymology & Language
    'Words with Surprising Origins',
    'English Words from Hindi',
    'Hindi Words in English',
    'Words That Changed Meaning Over Time',
    'Same Word, Different Meanings Across Languages',
    'Untranslatable Words',
    'Palindromes in Different Languages',
    'Longest Words in Various Languages',
    'Words Invented by Famous Authors',
    'Animal Names in Sanskrit',
    'Food Words Across Cultures',
    "Words That Sound Offensive But Aren't",
    'Phrases Lost in Translation',
    'Words That Look the Same in Multiple Languages',
    'Idioms That Make No Sense Literally',
    'Words Created by Mistake',
    'Phrases Unique to Indian English',
    "Words That Can't Be Translated Directly",
    'Regional Slang from Around India',
    'Tech Terms That Became Everyday Words',
    'Words That Changed Pronunciation Over Time',

    // Surprising Connections
    'Scientific Concepts in Ancient Mythology',
    'Mathematical Patterns in Nature',
    'Historical Events That Inspired Video Games',
    'Accidental Discoveries That Changed the World',
    'Foods That Were Once Considered Poisonous',
    'Animals Named After Celebrities',
    'Movie Plots Based on True Events',
    'Songs Inspired by Literature',
    'Everyday Objects Invented for Space Travel',
    'Art Techniques Used in Modern Technology',
    'Sports Rules That Changed Everything',
    'Architectural Designs Inspired by Nature',
    'Medical Treatments Discovered by Accident',
    'Historical Figures Who Never Met But Almost Did',
    'Inventions That Were Ahead of Their Time',
    'Algorithms Inspired by Natural Phenomena',
    'Ancient Practices That Modern Science Validates',
    'Video Game Mechanics Used in Education',
    'Fictional Technologies That Became Real',
    'Mythological Stories That Predicted Science',
    'Cooking Techniques That Revolutionized Chemistry',
    'Fashion Trends Inspired by Technology',
    'Music Genres Born from Cultural Fusion',
    'Traditional Games That Teach Programming Concepts',

    // Cross-Cultural & Immigrant Experience
    'Indian Influences in Western Pop Culture',
    'American Brands in India vs. America',
    'Foods That Changed When They Crossed Borders',
    'English Words with Indian Origins',
    'Hindi Words Now Used in English',
    'Cultural Festivals Around the World',
    'Global Tech Hubs Compared',
    'Immigrant Success Stories in Tech',
    'Nostalgic 90s Items from India and America',
    'University Experiences: India vs. America',
    'Cricket Terms Explained Through Baseball',
    'Regional Indian Dishes Unknown to the West',
    'Tech Office Culture: India vs. Silicon Valley',
    'Childhood Games Across Cultures',
    'Diaspora Literature Themes',
    'Cultural Faux Pas Around the World',
    'Wedding Traditions Across Cultures',
    'How Holidays Are Celebrated Differently',
    'Superstitions That Cross Cultural Boundaries',
    'Gestures With Different Meanings Across Cultures',
    'Indian Innovations That Changed Global Markets',
    'How American Products Adapted for Indian Markets',
    'Third Culture Kid Experiences',
    'Multilingual Family Dynamics',

    // Cross-disciplinary
    'Food in Movies',
    'Science in Pop Culture',
    'Historical Figures in Art',
    'Animals in Literature',
    'Music in Video Games',
    'Sports Records',
    'Mathematics in Music',
    'Physics in Sports',
    'Chemistry in Cooking',
    'Biology in Architecture',
    'Geography in Politics',
    'Psychology in Marketing',
    'Astronomy in Ancient Religions',
    'Linguistics in Computer Science',
    'Economics in Board Games',
    'Philosophy in Modern TV Shows',
    'Neuroscience in User Experience Design',
    'Anthropology in Fashion Trends',
    'Ecology in Urban Planning',
    'Mythology in Corporate Branding',
    'Quantum Physics in Sci-Fi Movies',
    'Game Theory in Social Media',
    'Forensics in Detective Fiction',
    'Sociology in Video Game Communities',

    // Movie/TV franchises
    'Harry Potter Spells',
    'Marvel Superheroes',
    'Star Wars Planets',
    'Disney Princesses',
    'Pixar Characters',
    'Game of Thrones Houses',
    'Lord of the Rings Locations',
    'Star Trek Captains',
    'DC Comics Villains',
    'Breaking Bad Chemistry',
    'Friends Recurring Jokes',
    'The Office Pranks',
    'Stranger Things 80s References',
    'Black Mirror Technologies',
    'Doctor Who Companions',
    'Sherlock Holmes Cases',
    'Jurassic Park Dinosaurs',
    'James Bond Gadgets',
    'Simpsons Predictions That Came True',
    'Westworld AI Concepts',

    // Indian Cinema
    'Guess the Bollywood Movie from Dialogue',
    'Iconic Bollywood Songs',
    'Bollywood Movie Remakes',
    'Famous Bollywood Dialogues',
    'Bollywood Dance Moves',
    'Bollywood Movies Shot Abroad',
    'Bollywood Actors in Hollywood',
    'Iconic Bollywood Villain Dialogues',
    'Bollywood Movies Based on Books',
    'Bollywood Item Songs',
    'Bollywood Cameo Appearances',
    'Bollywood Sequels Better Than Originals',
    'Bollywood Biopics vs. Reality',
    'Iconic Bollywood Costumes',
    'Bollywood Movies That Broke Box Office Records',
    'Indian Regional Cinema Classics',
    'Indian Cinema Pioneers',
    'Indian Film Industry Evolution',
    'Indian Cinema Global Influence',

    // Indian Ads & Pop Culture
    'Guess the Indian Ad from Jingle',
    'Iconic Indian TV Commercials',
    'Indian Ad Slogans',
    'Indian Brand Mascots',
    'Doordarshan Shows from the 90s',
    'Indian Comic Book Characters',
    'Indian Childhood Snacks',
    'Indian Regional Cinema',
    'Indian Web Series',
    'Indian TV Soap Opera Clichés',
    'Indian Reality Show Moments',
    'Indian Memes That Went Viral',
    'Indian Street Food Origins',
    'Indian Radio Shows That Everyone Listened To',
    'Indian Board Games and Their History',
    'Indian Festival Traditions Across Regions',
    'Indian Fashion Trends Through Decades',
    'Indian Music Genres Beyond Bollywood',
    'Indian YouTube Channels That Went Mainstream',
    'Indian Cartoons That Defined Childhoods',

    // Indian Tech & Professional
    'Indian Origin Tech CEOs',
    'Indian Tech Startups',
    'Indian IT Companies',
    'Famous Indian Scientists',
    'Indian Space Missions',
    'Indian Engineering Colleges',
    'Indian Tech Innovations',
    'Indian Entrepreneurs Who Changed Industries',
    'Indian AI Research Breakthroughs',
    'Indian Women in STEM',
    'Indian Medical Innovations',
    'Indian Sustainable Technology Solutions',
    'Indian Educational Technology Platforms',
    'Indian Financial Technology Innovations',
    'Indian Agricultural Technology Advancements',
    'Indian Healthcare Startups',
    'Indian Contributions to Global Software',
    'Indian Space Program Milestones',
    'Indian Renewable Energy Innovations',
    'Indian Cybersecurity Advancements',

    // Video game specific
    'Mario Power-Ups',
    'Zelda Items',
    'Pokémon Types',
    'Minecraft Blocks',
    'Call of Duty Weapons',
    'Final Fantasy Characters',
    'Grand Theft Auto Cities',
    "Assassin's Creed Assassins",
    'Halo Enemies',
    'Fortnite Dances',
    'League of Legends Champions',
    'Dota 2 Heroes',
    'Dota 2 Items',
    'World of Warcraft Classes',
    'The Sims Traits',
    'Sonic the Hedgehog Zones',
    'Among Us Strategies',
    'Valorant Agents',
    'Overwatch Ultimate Abilities',
    'Genshin Impact Characters',
    'Roblox Popular Games',
    'Apex Legends Weapons',
    'Minecraft Biomes',
    'Animal Crossing Villagers',
    'Indie Games That Changed Gaming',

    // Science & Technology
    'Breakthrough Technologies of the Decade',
    'AI Algorithms Explained Simply',
    'Space Missions That Changed Our Understanding',
    'Quantum Computing Concepts',
    'Renewable Energy Innovations',
    'Biomedical Breakthroughs',
    'Tech That Was Supposed to Fail But Succeeded',
    'Failed Tech That Was Ahead of Its Time',
    'Everyday Science Misconceptions',
    'Technologies Inspired by Science Fiction',
    'Scientific Discoveries Made by Accident',
    'Emerging Technologies to Watch',
    'Controversial Scientific Theories',
    'Unsolved Scientific Mysteries',
    'Citizen Science Success Stories',

    // Food & Cuisine
    'Dishes That Changed Names Across Borders',
    'Foods Named After People',
    'Unexpected Food Origin Stories',
    'Regional Variations of the Same Dish',
    'Street Foods Around the World',
    'Desserts With Surprising Ingredients',
    'Food Science Experiments You Can Eat',
    'Traditional Cooking Techniques',
    'Fusion Cuisines That Actually Work',
    'Misunderstood Ingredients',
    'Foods That Were Accidents',
    'Cooking Myths Debunked',
    'Dishes That Taste Better the Next Day',
    'Foods That Changed History',
    'Restaurant Secrets Revealed',

    // History & Geography
    'Historical Events Misrepresented in Movies',
    'Lost Cities Rediscovered',
    'Borders That Changed Overnight',
    'Historical Figures With Surprising Skills',
    'Geographical Features Named After Mistakes',
    'Historical Predictions That Came True',
    'Obscure Historical Jobs',
    'Forgotten Historical Achievements',
    'Places That Changed Their Names',
    'Historical Mysteries Finally Solved',
    'Unexpected Origins of Modern Traditions',
    'Geographical Oddities Around the World',
    'Historical Figures Who Were Actually Terrible',
    'Unsung Heroes of Major Historical Events',
    "Ancient Technologies We Still Don't Understand",

    // Wordplay & Puzzles
    'Words That Are Their Own Opposites',
    'Pangrams Around the World',
    'Puzzles That Stumped Mathematicians',
    'Words With No Direct Translation',
    'Riddles With Surprising Answers',
    'Optical Illusions Explained',
    'Logic Puzzles That Changed Mathematics',
    'Codes and Ciphers Through History',
    'Paradoxes That Will Blow Your Mind',
    'Lateral Thinking Problems',
    'Mathematical Puzzles Anyone Can Solve',
    'Word Games From Around the World',
    'Linguistic Tongue Twisters',
    'Visual Puzzles That Test Perception',
    'Brain Teasers That Reveal Cognitive Biases',

    // Decade-Specific Categories (Global)
    '70s Rock Bands',
    '80s Action Movies',
    '80s Fashion Trends',
    '80s TV Shows',
    '80s Video Games',
    '80s Pop Hits',
    '80s Cartoons',
    '90s Sitcoms',
    '90s Pop Hits',
    '90s Fashion Trends',
    '90s TV Shows',
    '90s Video Games',
    '90s Cartoons',
    '90s Action Movies',
    '90s Hip Hop',
    '90s Boy Bands',
    '90s Girl Groups',
    '90s Toys',
    '90s Commercials',
    '90s Snacks',
    '90s Technology',
    '2000s Reality TV',
    '2000s Pop Stars',
    '2000s Internet Trends',
    '2000s Fashion Fads',
    '2000s TV Shows',
    '2000s Video Games',
    '2000s Movies',
    '2010s Social Media Trends',
    '2010s Viral Videos',
    '2010s TV Shows',
    '2010s Music Hits',
    '2010s Memes',

    // Decade-Specific Categories (Indian)
    '90s Doordarshan Shows',
    '90s Indian TV Commercials',
    '90s Indian Cartoons',
    '90s Indian Movies',
    '90s Indian Music',
    '90s Indian Fashion',
    '90s Indian Snacks',
    '90s Indian TV Shows',
    '2000s Indian Reality TV',
    '2000s Indian Pop Culture',
    '2000s Bollywood Hits',
    '2000s Indian TV Serials',
    '80s Indian Cinema',
    '80s Indian Music',
    '80s Indian Television',
    '70s Indian Cinema',

    // Other categories
    'Olympic Gold Medalists',
    'World Cuisines',
    'Famous Landmarks',
    'Mythological Creatures',
    'Word Origins',
    'Anagrams',
    'Rhyme Time',
    'Who Said It in Politics',
    'Famous Firsts in Technology',
    'Hidden Connections in History',
    'Unusual Sports Around the World',
    'Misattributed Quotes',
    'Urban Legends Debunked',
    'Surprising Facts About Everyday Objects',
    'Bizarre World Records',
    'Unexpected Celebrity Skills',
    'Mistranslations That Changed History',
    'Coincidences Too Strange to Believe',
    'Forgotten Fashion Trends',
    'Surprising Animal Abilities'
  ];

  // Timing variables
  private spawnInterval: any;
  private spawnRate = 5000; // New category every 5 seconds (slowed down from 3s)

  // Category history tracking
  private recentlyShownCategories: string[] = [];
  private maxHistorySize = 30; // Remember the last 30 categories to avoid repetition

  // Thematic groups for rotating category suggestions
  private thematicGroups = [
    { name: 'Entertainment', weight: 1.0 },
    { name: 'Academic', weight: 0.8 },
    { name: 'Language & Etymology', weight: 0.9 },
    { name: 'Cross-Cultural', weight: 1.0 },
    { name: 'Tech & Science', weight: 0.9 },
    { name: 'Indian Culture', weight: 1.0 },
    { name: 'Pop Culture Franchises', weight: 1.0 },
    { name: 'Video Games', weight: 0.8 },
    { name: 'Sports & Recreation', weight: 0.7 },
    { name: 'Food & Cuisine', weight: 0.8 },
    { name: 'Art & Music', weight: 0.8 },
    { name: 'History & Geography', weight: 0.8 },
    { name: 'Surprising Connections', weight: 1.2 },
    { name: 'Wordplay & Puzzles', weight: 0.9 },
    { name: 'Nostalgic Decades', weight: 1.1 }
  ];

  // Current active thematic group (changes periodically)
  private currentThematicGroup = 0;
  private thematicGroupChangeInterval: any;

  // Getter for category count based on cast mode or screen size
  private getCategoryCount(): number {
    // Check if we're on a mobile device (screen width <= 600px)
    const isMobile = window.innerWidth <= 600;
    // Return 4 categories for cast mode or mobile devices, otherwise 8
    if (isMobile) {
      return 2;
    }
    return this.isCast ? 4 : 8;
  }

  // Track previous window width to avoid unnecessary refreshes
  private previousWindowWidth: number = window.innerWidth;
  private resizeListener: any;

  ngOnInit() {
    // Initialize with some random categories
    this.spawnInitialCategories();
    // Start the spawning interval
    this.startCategorySpawning();

    // Start rotating thematic groups every 2 minutes
    this.thematicGroupChangeInterval = setInterval(() => {
      this.rotateThematicGroup();
      // When the thematic group changes, refresh all categories
      this.refreshAllCategories();
    }, 120000); // 2 minutes

    // Add window resize listener to update categories when screen size changes
    this.resizeListener = () => {
      const currentWidth = window.innerWidth;
      // Only refresh if crossing the mobile threshold (600px)
      const wasMobile = this.previousWindowWidth <= 600;
      const isMobile = currentWidth <= 600;

      if (wasMobile !== isMobile) {
        this.refreshAllCategories();
      }

      this.previousWindowWidth = currentWidth;
    };

    window.addEventListener('resize', this.resizeListener);
  }

  ngOnDestroy() {
    // Clean up intervals when component is destroyed
    this.stopCategorySpawning();

    if (this.thematicGroupChangeInterval) {
      clearInterval(this.thematicGroupChangeInterval);
    }

    // Remove the resize listener
    if (this.resizeListener) {
      window.removeEventListener('resize', this.resizeListener);
    }
  }

  // Spawn initial set of categories
  spawnInitialCategories() {
    const categoryCount = this.getCategoryCount();

    // Initialize with an array of the exact size we need
    // This ensures the grid cells are created immediately with the right structure
    this.spawnedCategories = new Array(categoryCount).fill('');

    // Then fill each position with a unique category
    for (let i = 0; i < categoryCount; i++) {
      let category = this.getRandomCategory();
      let attempts = 0;

      // Make sure we don't have duplicates
      while (this.spawnedCategories.includes(category) && attempts < 10) {
        category = this.getRandomCategory();
        attempts++;
      }

      // Set the category at this position
      this.spawnedCategories[i] = category;
    }
  }

  // Ensure we always have exactly the right number of categories
  ensureFixedCategoryCount() {
    const categoryCount = this.getCategoryCount();

    // If we have too many, remove extras
    while (this.spawnedCategories.length > categoryCount) {
      this.spawnedCategories.pop();
    }

    // If we have too few, add more
    while (this.spawnedCategories.length < categoryCount) {
      const category = this.getRandomCategory();
      // Try to avoid duplicates, but don't try too hard to prevent infinite loops
      let attempts = 0;
      let uniqueCategory = category;

      while (this.spawnedCategories.includes(uniqueCategory) && attempts < 5) {
        uniqueCategory = this.getRandomCategory();
        attempts++;
      }

      this.spawnedCategories.push(uniqueCategory);
    }
  }

  // Start spawning new categories at intervals
  startCategorySpawning() {
    // First ensure we have exactly the fixed number of categories
    this.ensureFixedCategoryCount();

    // Then start the interval to replace one category at a time
    this.spawnInterval = setInterval(() => {
      this.replaceRandomCategory();
    }, this.spawnRate);
  }

  // Replace a random category with a smooth fade transition
  private replaceRandomCategory() {
    // Always replace exactly one category to maintain the fixed count
    const categoryCount = this.getCategoryCount();
    if (this.spawnedCategories.length === categoryCount) {
      // Choose a random position to replace (not the most recently added one)
      const maxIndex = this.spawnedCategories.length - 1;
      const indexToReplace = Math.floor(Math.random() * maxIndex);

      // Generate a new unique category
      let newCategory = this.getRandomCategory();
      let attempts = 0;

      // Make sure the new category is not already in the list
      while (
        (this.spawnedCategories.includes(newCategory) ||
          this.selectedCategories.includes(newCategory)) &&
        attempts < 10
      ) {
        newCategory = this.getRandomCategory();
        attempts++;
      }

      // Only replace if we found a unique category
      if (
        !this.spawnedCategories.includes(newCategory) &&
        !this.selectedCategories.includes(newCategory)
      ) {
        // Get the DOM element for the chip we want to replace
        setTimeout(() => {
          // Create a copy of the array
          const newCategories = [...this.spawnedCategories];

          // Replace the category directly in the copy
          newCategories[indexToReplace] = newCategory;

          // Update the array reference to trigger change detection
          this.spawnedCategories = newCategories;
        }, 50); // Small delay to ensure smooth transition
      }
    } else {
      // If we somehow don't have the right number, fix it
      this.ensureFixedCategoryCount();
    }
  }

  // Stop spawning new categories
  stopCategorySpawning() {
    if (this.spawnInterval) {
      clearInterval(this.spawnInterval);
    }
  }

  // Rotate to the next thematic group
  private rotateThematicGroup() {
    this.currentThematicGroup =
      (this.currentThematicGroup + 1) % this.thematicGroups.length;
  }

  // Get a random category - either from pre-generated list or dynamically created
  getRandomCategory(): string {
    // Apply weighted randomization based on current thematic group
    const currentGroup = this.thematicGroups[this.currentThematicGroup];
    const usePreGenerated = Math.random() < 0.7 * currentGroup.weight;

    let category: string;
    let attempts = 0;
    const maxAttempts = 15;

    do {
      if (usePreGenerated || attempts > 10) {
        // Use a pre-generated category with thematic influence
        category = this.getThematicPreGeneratedCategory();
      } else {
        // Generate a dynamic category using templates
        category = this.generateDynamicCategory();
      }
      attempts++;

      // If we've tried too many times, just use any category
      if (attempts >= maxAttempts) {
        break;
      }
    } while (this.recentlyShownCategories.includes(category));

    // Add to recently shown categories and maintain history size
    this.addToRecentlyShown(category);

    return category;
  }

  // Add a category to the recently shown list
  private addToRecentlyShown(category: string) {
    this.recentlyShownCategories.push(category);

    // Keep the history at a reasonable size
    if (this.recentlyShownCategories.length > this.maxHistorySize) {
      this.recentlyShownCategories.shift(); // Remove oldest entry
    }
  }

  // Get a pre-generated category with thematic influence
  private getThematicPreGeneratedCategory(): string {
    const currentTheme = this.thematicGroups[this.currentThematicGroup].name;

    // Filter categories that match the current theme
    let thematicCategories: string[] = [];

    switch (currentTheme) {
      case 'Entertainment':
        thematicCategories = this.categorySuggestions.filter(cat =>
          /movie|tv|music|celebrity|band|song|pop culture|dance|actor|character/i.test(
            cat
          )
        );
        break;
      case 'Academic':
        thematicCategories = this.categorySuggestions.filter(cat =>
          /science|history|geography|mathematics|physics|chemistry|biology|astronomy|literature/i.test(
            cat
          )
        );
        break;
      case 'Language & Etymology':
        thematicCategories = this.categorySuggestions.filter(cat =>
          /etymology|sanskrit|hindi|latin|greek|mandarin|japanese|arabic|spanish|french|german|russian|idioms|proverbs|slang|palindromes|anagrams|portmanteaus|words|language/i.test(
            cat
          )
        );
        break;
      case 'Cross-Cultural':
        thematicCategories = this.categorySuggestions.filter(cat =>
          /indian influences|american brands|foods that changed|english words with indian|hindi words|cultural festivals|global tech|immigrant|cricket terms|regional indian|childhood games across cultures/i.test(
            cat
          )
        );
        break;
      case 'Tech & Science':
        thematicCategories = this.categorySuggestions.filter(cat =>
          /technology|science|computers|artificial intelligence|robotics|engineering|space|inventions|discoveries|tech ceos|startups/i.test(
            cat
          )
        );
        break;
      case 'Indian Culture':
        thematicCategories = this.categorySuggestions.filter(cat =>
          /bollywood|indian|india|cricket|ipl|doordarshan|filmfare|sanskrit|hindi|regional|classical|festival|mythology/i.test(
            cat
          )
        );
        break;
      case 'Pop Culture Franchises':
        thematicCategories = this.categorySuggestions.filter(cat =>
          /harry potter|marvel|star wars|game of thrones|lord of the rings|disney|pixar|star trek|dc comics|simpsons|friends|hollywood|broadway/i.test(
            cat
          )
        );
        break;
      case 'Video Games':
        thematicCategories = this.categorySuggestions.filter(cat =>
          /mario|zelda|pok[eé]mon|minecraft|call of duty|final fantasy|grand theft auto|assassin|halo|fortnite|league of legends|dota 2|world of warcraft|sims|sonic/i.test(
            cat
          )
        );
        break;
      case 'Sports & Recreation':
        thematicCategories = this.categorySuggestions.filter(cat =>
          /sports|olympic|world cup|cricket|ipl|super bowl|athletes|records|games|competition/i.test(
            cat
          )
        );
        break;
      case 'Food & Cuisine':
        thematicCategories = this.categorySuggestions.filter(cat =>
          /food|cuisine|cooking|drink|recipe|restaurant|chef|dish|ingredient|spice|regional|street food/i.test(
            cat
          )
        );
        break;
      case 'Art & Music':
        thematicCategories = this.categorySuggestions.filter(cat =>
          /art|music|painting|sculpture|artist|band|song|album|instrument|genre|classical|contemporary/i.test(
            cat
          )
        );
        break;
      case 'History & Geography':
        thematicCategories = this.categorySuggestions.filter(cat =>
          /history|geography|ancient|medieval|renaissance|modern|world war|cold war|civilization|country|city|landmark|monument/i.test(
            cat
          )
        );
        break;
      case 'Surprising Connections':
        thematicCategories = this.categorySuggestions.filter(cat =>
          /surprising|connections|inspired|patterns|concepts|accidental|discoveries|changed|invented|techniques|unexpected/i.test(
            cat
          )
        );
        break;
      case 'Wordplay & Puzzles':
        thematicCategories = this.categorySuggestions.filter(cat =>
          /guess|identify|name the|match|finish|anagram|palindrome|riddle|puzzle|word|fill in the blank|true or false|multiple choice/i.test(
            cat
          )
        );
        break;
      case 'Nostalgic Decades':
        thematicCategories = this.categorySuggestions.filter(cat =>
          /70s|80s|90s|2000s|nostalgic|childhood|retro|vintage|classic|old school|throwback|sitcoms|cartoons|toys|commercials|pop hits|fashion|video games|action movies|hip hop|boy bands|girl groups|snacks|technology/i.test(
            cat
          )
        );
        break;
      default:
        // If no match, use all categories
        thematicCategories = this.categorySuggestions;
    }

    // If we don't have enough thematic categories, add some general ones
    if (thematicCategories.length < 5) {
      thematicCategories = [
        ...thematicCategories,
        ...this.categorySuggestions.slice(0, 20)
      ];
    }

    // Return a random category from the filtered list
    return thematicCategories[
      Math.floor(Math.random() * thematicCategories.length)
    ];
  }

  // Generate a dynamic category using templates
  private generateDynamicCategory(): string {
    const randomChoice = (arr: string[]): string => {
      return arr[Math.floor(Math.random() * arr.length)];
    };

    // Weighted random choice based on array and weights
    const weightedRandomChoice = (arr: string[], weights: number[]): string => {
      // Normalize weights if needed
      const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
      const normalizedWeights = weights.map(w => w / totalWeight);

      // Calculate cumulative weights
      const cumulativeWeights: number[] = [];
      let cumWeight = 0;
      for (const weight of normalizedWeights) {
        cumWeight += weight;
        cumulativeWeights.push(cumWeight);
      }

      // Generate random value between 0 and 1
      const random = Math.random();

      // Find the index where random falls
      for (let i = 0; i < cumulativeWeights.length; i++) {
        if (random <= cumulativeWeights[i]) {
          return arr[i];
        }
      }

      // Fallback (should never reach here)
      return arr[arr.length - 1];
    };

    // Get current thematic group to influence generation
    const currentTheme = this.thematicGroups[this.currentThematicGroup].name;

    // Choose a format template with thematic influence
    let formatWeights: number[] = [];

    // Adjust weights based on current theme
    this.categoryComponents.formats.forEach(format => {
      let weight = 1.0; // Default weight

      // Increase weight for formats that match the current theme
      if (
        currentTheme === 'Wordplay & Puzzles' &&
        /guess|identify|name|match|fill in|true or false|multiple choice/i.test(
          format
        )
      ) {
        weight = 1.5;
      } else if (
        currentTheme === 'Pop Culture Franchises' &&
        /quotes|who said it|famous/i.test(format)
      ) {
        weight = 1.5;
      } else if (
        currentTheme === 'Surprising Connections' &&
        /hidden|origins|vs/i.test(format)
      ) {
        weight = 1.5;
      } else if (
        currentTheme === 'Language & Etymology' &&
        /origins|in/i.test(format)
      ) {
        weight = 1.5;
      }

      formatWeights.push(weight);
    });

    const format = weightedRandomChoice(
      this.categoryComponents.formats,
      formatWeights
    );

    // Determine what to fill in the template based on the format
    if (format.includes('{0}') && format.includes('{1}')) {
      // Format needs two parameters
      let param1: string, param2: string;

      // Decide what types of parameters to use based on the format
      if (
        format.startsWith('Guess') ||
        format.startsWith('Name') ||
        format.startsWith('Identify')
      ) {
        // For "Guess/Name/Identify X from Y" formats
        param1 = randomChoice(this.categoryComponents.elements);
        param2 = randomChoice(this.categoryComponents.clues);
      } else if (format.includes('in')) {
        // For "X in Y" formats
        param1 = randomChoice([
          ...this.categoryComponents.subjects,
          ...this.categoryComponents.elements
        ]);
        param2 = randomChoice([
          ...this.categoryComponents.franchises,
          ...this.categoryComponents.subjects,
          ...this.categoryComponents.timePeriods
        ]);
      } else if (format.includes('vs')) {
        // For "X vs Y" formats
        const options = [
          ...this.categoryComponents.franchises,
          ...this.categoryComponents.subjects
        ];
        param1 = randomChoice(options);
        // Make sure param2 is different from param1
        let param2Options = options.filter(item => item !== param1);
        param2 = randomChoice(param2Options);
      } else {
        // Default case - use generic parameters
        param1 = randomChoice([
          ...this.categoryComponents.subjects,
          ...this.categoryComponents.franchises,
          ...this.categoryComponents.elements
        ]);
        param2 = randomChoice([
          ...this.categoryComponents.subjects,
          ...this.categoryComponents.franchises,
          ...this.categoryComponents.clues
        ]);
      }

      // Replace the placeholders in the template
      return format.replace('{0}', param1).replace('{1}', param2);
    } else if (format.includes('{0}')) {
      // Format needs one parameter
      let param: string;

      if (
        format.includes('Trivia') ||
        format.includes('Famous') ||
        format.includes('Facts') ||
        format.includes('Obscure')
      ) {
        // For "X Trivia", "Famous X", "X Facts", "Obscure X" formats
        param = randomChoice([
          ...this.categoryComponents.subjects,
          ...this.categoryComponents.franchises
        ]);
      } else if (format.includes('Finish') || format.includes('Fill')) {
        // For "Finish the X" or "Fill in the Blank: X" formats
        param = randomChoice([
          'Quote',
          'Lyric',
          'Sentence',
          'Title',
          'Phrase',
          'Saying'
        ]);
      } else {
        // Default case
        param = randomChoice([
          ...this.categoryComponents.subjects,
          ...this.categoryComponents.franchises,
          ...this.categoryComponents.elements
        ]);
      }

      // Replace the placeholder in the template
      return format.replace('{0}', param);
    } else {
      // Format doesn't have placeholders, return as is
      return format;
    }
  }

  // Track items by index for better performance
  trackByIndex(index: number, _item: any): number {
    return index;
  }

  // Reset a specific chip with a new random category
  resetChip(index: number, event: MouseEvent) {
    // Stop the click event from propagating to the parent (which would add the category)
    event.stopPropagation();

    // Get the chip element and add a fade-out class
    const chipElement = event.currentTarget as HTMLElement;
    const chipWrapper = chipElement.closest('.category-chip-wrapper');

    if (chipWrapper) {
      // Add fade-out classes to trigger animations for both chip and text
      chipWrapper.classList.remove('fade-in', 'text-fade-in');
      chipWrapper.classList.add('fade-out', 'text-fade-out');

      // After animation completes, replace the category
      setTimeout(() => {
        // Generate a new unique category
        let newCategory = this.getRandomCategory();
        let attempts = 0;

        // Make sure the new category is not already in the list
        while (
          (this.spawnedCategories.includes(newCategory) ||
            this.selectedCategories.includes(newCategory)) &&
          attempts < 10
        ) {
          newCategory = this.getRandomCategory();
          attempts++;
        }

        // Replace the category
        if (!this.spawnedCategories.includes(newCategory)) {
          const updatedCategories = [...this.spawnedCategories];
          updatedCategories[index] = newCategory;
          this.spawnedCategories = updatedCategories;

          // Reset animation classes after a short delay
          setTimeout(() => {
            if (chipWrapper) {
              chipWrapper.classList.remove('fade-out', 'text-fade-out');
              chipWrapper.classList.add('fade-in', 'text-fade-in');
            }
          }, 50);
        }
      }, 300); // Wait for fade-out animation to complete
    }
  }

  // Add a category to the selected list and replace it with a new one
  addCategory(category: string, index: number) {
    // In read-only mode, just show a tooltip or do nothing
    if (this.isReadOnly) {
      return;
    }

    // Check if it's already selected
    if (!this.selectedCategories.includes(category)) {
      // Add to selected categories
      this.selectedCategories.push(category);

      // Create a SubCategory object to emit
      const subCategory: SubCategory = {
        subCategory: category,
        description: `Category: ${category}`
      };

      // Emit the selected category
      this.emitSubCategories.emit(subCategory);

      // Replace the selected category with a new one immediately
      this.replaceCategory(index);
    }
  }

  // Refresh all categories at once
  refreshAllCategories() {
    // Get the appropriate category count
    const categoryCount = this.getCategoryCount();

    // Clear the array first
    this.spawnedCategories = [];

    // Get the current thematic group
    const currentTheme = this.thematicGroups[this.currentThematicGroup].name;
    console.log(`Refreshing categories with theme: ${currentTheme}`);

    // Then repopulate with new random categories
    for (let i = 0; i < categoryCount; i++) {
      let category = this.getRandomCategory();
      let attempts = 0;

      // Make sure we don't have duplicates in the current display
      while (this.spawnedCategories.includes(category) && attempts < 10) {
        category = this.getRandomCategory();
        attempts++;
      }

      this.spawnedCategories.push(category);
    }
  }

  // Replace a category at a specific index with a new random one
  private replaceCategory(index: number) {
    // Find the chip wrapper element
    const chipWrappers = document.querySelectorAll('.category-chip-wrapper');
    const chipWrapper = chipWrappers[index] as HTMLElement;

    if (chipWrapper) {
      // Add fade-out classes to trigger animations for both chip and text
      chipWrapper.classList.remove('fade-in', 'text-fade-in');
      chipWrapper.classList.add('fade-out', 'text-fade-out');

      // After animation completes, replace the category
      setTimeout(() => {
        // Generate a new unique category
        let newCategory = this.getRandomCategory();
        let attempts = 0;

        // Make sure the new category is not already in the list
        while (
          (this.spawnedCategories.includes(newCategory) ||
            this.selectedCategories.includes(newCategory)) &&
          attempts < 10
        ) {
          newCategory = this.getRandomCategory();
          attempts++;
        }

        // Replace the category
        if (!this.spawnedCategories.includes(newCategory)) {
          const updatedCategories = [...this.spawnedCategories];
          updatedCategories[index] = newCategory;
          this.spawnedCategories = updatedCategories;

          // Reset animation classes after a short delay
          setTimeout(() => {
            if (chipWrapper) {
              chipWrapper.classList.remove('fade-out', 'text-fade-out');
              chipWrapper.classList.add('fade-in', 'text-fade-in');
            }
          }, 50);
        }
      }, 300); // Wait for fade-out animation to complete
    } else {
      // Fallback if element not found - just update the array
      let newCategory = this.getRandomCategory();
      let attempts = 0;

      while (
        (this.spawnedCategories.includes(newCategory) ||
          this.selectedCategories.includes(newCategory)) &&
        attempts < 10
      ) {
        newCategory = this.getRandomCategory();
        attempts++;
      }

      if (!this.spawnedCategories.includes(newCategory)) {
        const updatedCategories = [...this.spawnedCategories];
        updatedCategories[index] = newCategory;
        this.spawnedCategories = updatedCategories;
      }
    }
  }

  // Remove a category from the selected list
  removeCategory(category: string) {
    const index = this.selectedCategories.indexOf(category);
    if (index !== -1) {
      this.selectedCategories.splice(index, 1);
    }
  }

  // Generate a color class for each chip based on category type
  getChipColorClass(index: number): string {
    const category = this.spawnedCategories[index];

    // Color coding based on category type:
    // - Entertainment categories (movies, TV, music, etc.) - primary (blue)
    // - Knowledge categories (science, history, etc.) - secondary (purple)
    // - Creative formats ("Guess the X") - tertiary (green)
    // - Movie/TV franchises - success (light green)
    // - Video game franchises - warning (orange)
    // - Bollywood categories - info (light blue)
    // - Indian culture & tech categories - info (light blue)
    // - Etymology & language categories - secondary (purple)
    // - Surprising connections - tertiary (green)
    // - Cross-cultural categories - tertiary (green)

    // Video game franchises - orange
    if (
      /mario|zelda|pok[eé]mon|minecraft|call of duty|final fantasy|grand theft auto|assassin|halo|fortnite|league of legends|dota 2|world of warcraft|sims|sonic/i.test(
        category
      )
    ) {
      return 'warning-chip';
    }
    // Bollywood and Indian culture categories - light blue
    else if (
      /bollywood|dhoom|krrish|golmaal|dabangg|baahubali|housefull|race|don|singham|indian|india|cricket|ipl|doordarshan|filmfare|sanskrit|hindi|regional|classical|festival|mythology|comic|childhood|snack|web series|ad jingle|ad from lyrics|commercial|slogan|mascot/i.test(
        category
      )
    ) {
      return 'info-chip';
    }
    // Movie/TV franchises - light green
    else if (
      /harry potter|marvel|star wars|game of thrones|lord of the rings|disney|pixar|star trek|dc comics|simpsons|friends/i.test(
        category
      )
    ) {
      return 'success-chip';
    }
    // Creative formats, Surprising connections & Cross-cultural - green
    else if (
      /guess|identify|name the|match|finish|surprising|connections|inspired|patterns|concepts|accidental|discoveries|changed|invented|techniques|cross-cultural|immigrant|influences|compared|vs\.|across cultures|across borders|explained through/i.test(
        category
      )
    ) {
      return 'tertiary-chip';
    }
    // Knowledge categories & Etymology/Language - purple
    else if (
      /science|history|geography|world|ancient|space|civilizations|mathematics|technology|inventions|etymology|sanskrit|hindi|latin|greek|mandarin|japanese|arabic|spanish|french|german|russian|idioms|proverbs|slang|palindromes|anagrams|portmanteaus|words|language/i.test(
        category
      )
    ) {
      return 'secondary-chip';
    }
    // Entertainment categories - blue
    else if (
      /movie|tv|music|celebrity|band|song|pop culture|dance|actor|character/i.test(
        category
      )
    ) {
      return 'primary-chip';
    }
    // Combinations and others - blue
    else {
      return 'primary-chip';
    }
  }

  // Get a random position class for spawned chips
  getRandomPositionClass(): string {
    const positions = [
      'top-left',
      'top-right',
      'bottom-left',
      'bottom-right',
      'center'
    ];
    return positions[Math.floor(Math.random() * positions.length)];
  }
}
