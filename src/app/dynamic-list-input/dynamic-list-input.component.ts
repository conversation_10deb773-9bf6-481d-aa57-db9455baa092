import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  Input,
  Output,
  ViewChild,
  ElementRef,
  forwardRef
} from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  NG_VALUE_ACCESSOR
} from '@angular/forms';
import {
  MatFormField,
  MatFormFieldControl,
  MatLabel
} from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { ControlValueAccessor } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { BehaviorSubject, filter, map, Observable, startWith } from 'rxjs';
import { MatAutocompleteModule } from '@angular/material/autocomplete';

@Component({
  selector: 'app-dynamic-list-input',
  imports: [
    <PERSON><PERSON>abe<PERSON>,
    FormsModule,
    ReactiveFormsModule,
    MatFormField,
    CommonModule,
    MatFormField,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    MatAutocompleteModule
  ],
  templateUrl: './dynamic-list-input.component.html',
  styleUrl: './dynamic-list-input.component.css',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DynamicListInputComponent),
      multi: true
    }
  ]
})
export class DynamicListInputComponent implements ControlValueAccessor {
  @Input() listLabel: string = 'Item';
  @Input() maxLength = 10;
  @Input() autoCompleteList: Array<string> = [];
  @Output() listChangeEvent = new EventEmitter<Array<string>>();

  filteredOptions = new BehaviorSubject<string[]>([]);

  ngOnInit() {}

  private _filter(value: string): string[] {
    const filterValue = value.toLowerCase();
    const res = this.autoCompleteList.filter(option =>
      option.toLowerCase().includes(filterValue)
    );
    return res;
  }

  fb = inject(FormBuilder);

  createInput(input: string) {
    const ctrl = this.fb.control(input);
    ctrl.valueChanges
      .pipe(
        map(value => this._filter(value || '')) // Filter the options
      )
      .subscribe(this.filteredOptions);
    return ctrl;
  }
  myForm = this.fb.group({
    items: this.fb.array([this.createInput('')])
  });

  get items() {
    return this.myForm.get('items') as FormArray;
  }

  getList(): Array<string> {
    return this.items.value.filter((item: string) => item !== '');
  }

  setElements(elements: Array<string>, callback: boolean = false) {
    this.clearElements(callback);
    elements.forEach(element => {
      this.addElement(element, callback);
    });
  }

  updateItems(callback = true) {
    // 1. Filter out empty items
    const newItems: Array<string> = this.items.value
      .filter((item: string) => item !== '')
      .slice(0, this.maxLength);

    // 2. Create a new FormArray with the filtered items
    const newItemsArray = this.fb.array(
      newItems.map((item: string) => this.createInput(item))
    );

    // 3. Set the new FormArray to the form
    this.myForm.setControl(
      'items',
      newItemsArray as FormArray<FormControl<string | null>>
    );

    // 4. Ensure there's always at least one empty input
    if (
      this.items.length < this.maxLength &&
      this.items.controls.every(control => control.value)
    ) {
      this.items.push(this.createInput(''));
    }
    if (callback) {
      this.listChangeEvent.emit(newItems);
      this.onChange(newItems);
    }
  }

  trackByIndex(index: number, item: any): number {
    return index;
  }

  addElement(value: string = '', callback: boolean = true) {
    this.items.push(this.createInput(value));
    this.updateItems(callback); // Emit the updated list after adding
  }
  clearElements(callback: boolean = true) {
    this.items.clear();
    this.updateItems(callback);
  }

  // ControlValueAccessor methods
  onChange: any = () => {};
  onTouched: any = () => {};

  writeValue(value: any): void {
    if (value && Array.isArray(value)) {
      const newItemsArray = this.fb.array(
        value.map((item: string) => this.fb.control(item))
      );
      this.myForm.setControl('items', newItemsArray);
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    // Implement if needed to disable the form
  }

  clearInput(index: number) {
    this.items.controls[index].setValue('');
    this.updateItems();
  }
}
