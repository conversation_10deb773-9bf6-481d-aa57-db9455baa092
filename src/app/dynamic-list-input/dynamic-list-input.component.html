<form [formGroup]="myForm">
  <div formArrayName="items" class="input-list">
    <mat-form-field
      *ngFor="let item of items.controls; let i = index; trackBy: trackByIndex"
      class="form-field"
    >
      <mat-label>{{ listLabel }} {{ i + 1 }}</mat-label>
      <input
        matInput
        type="text"
        [formControlName]="i"
        (input)="updateItems(false)"
        (blur)="updateItems(true)"
        [matAutocomplete]="auto"
      />
      <mat-autocomplete #auto="matAutocomplete">
        <mat-option
          *ngFor="let option of filteredOptions.asObservable() | async"
          [value]="option"
        >
          {{ option }}
        </mat-option>
      </mat-autocomplete>

      <button
        mat-icon-button
        matSuffix
        (click)="clearInput(i); $event.stopPropagation()"
        type="button"
        [attr.aria-label]="'Clear input ' + (i + 1)"
      >
        <mat-icon>clear</mat-icon>
      </button>
    </mat-form-field>
  </div>
</form>
