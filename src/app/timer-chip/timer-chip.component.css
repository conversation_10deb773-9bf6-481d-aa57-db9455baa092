app-timer-chip {
  display: flex;
}

.timer-chip {
  position: relative;
  display: flex;
  padding: 0.3rem 0.8rem;
  border-radius: 1rem;
  font-size: 0.9rem;
  font-weight: bold;
  color: var(--timer-track-color, #fff);
  overflow: hidden;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  background-color: rgba(0, 0, 0, 0.3);
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2),
    inset 0 1px 2px rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  animation: timerAppear 0.3s ease-out;
}

@keyframes timerAppear {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.timer-chip:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 2px rgba(255, 255, 255, 0.2);
}

.timer-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--timer-stroke-color, var(--mat-sys-primary)) 0%,
    var(--timer-stroke-color-secondary, var(--mat-sys-tertiary)) 100%
  );
  background-size: 200% 100%;
  animation: gradientMove 2s linear infinite;
  transition: width 0.1s linear;
  z-index: 1;
  box-shadow: 0 0 10px rgba(var(--mat-sys-primary-rgb), 0.5);
  opacity: 0.8;
}

@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

.timer-text {
  z-index: 2;
  position: relative;
  color: var(--timer-foreground-color, #fff);
  letter-spacing: 0.05em;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0 0.2rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  font-weight: 600;
}
.timer-value {
  font-style: italic;
  font-weight: lighter;
  color: var(--timer-foreground-color);
}
