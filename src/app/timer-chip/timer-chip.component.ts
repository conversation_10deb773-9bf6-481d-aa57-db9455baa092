import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, OnDestroy, OnChanges } from '@angular/core';

@Component({
  selector: 'app-timer-chip',
  imports: [CommonModule],
  templateUrl: './timer-chip.component.html',
  styleUrls: ['./timer-chip.component.css']
})
export class TimerChipComponent implements OnChanges, OnDestroy {
  @Input() initialTime: number = 0;
  @Input() targetTime: number = 0;
  @Input() text: string = ''; // Text to display next to the timer
  @Input() showTimeLeft: boolean = false;

  timeLeft: number = 0;
  initialTimeLeft: number = 0;
  private intervalId: any;

  ngOnChanges() {
    this.clearTimer();
    this.initialTimeLeft = this.targetTime - this.initialTime;
    this.startTimer();
  }

  ngOnDestroy() {
    this.clearTimer();
  }

  get progress(): number {
    if (this.initialTimeLeft === 0) {
      return 0;
    }
    return (this.timeLeft / this.initialTimeLeft) * 100;
  }

  startTimer() {
    this.clearTimer();
    this.intervalId = setInterval(() => {
      const now = Date.now();
      const delta = this.targetTime - now;

      if (delta <= 0) {
        this.timeLeft = 0;
        this.clearTimer();
      } else {
        this.timeLeft = Math.round(delta);
      }
    }, 100);
  }

  clearTimer() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }
}
