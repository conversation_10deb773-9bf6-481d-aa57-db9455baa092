<mat-toolbar class="top-app-bar" [class.is-cast]="isCast">
  <mat-icon matTooltip="JeopardyGPT - AI-powered trivia game"
    >smart_toy</mat-icon
  >
  <a href="/" style="color:inherit; text-decoration: inherit;">JeopardyGPT</a>
  <span class="spacer" *ngIf="!isCast"></span>
  <div class="right-buttons" *ngIf="!isCast">
    <app-cast
      class="cast-button"
      [url]="getRoomUrl()"
      matTooltip="Cast this game to a TV or another device"
    ></app-cast>
    <div
      class="settings-button-container"
      [ngClass]="{ 'settings-menu-open': settingsOpen }"
      #settingsButtonContainer
    >
      <app-settings *ngIf="settingsOpen"></app-settings>
      <button
        mat-fab
        extended
        (click)="toggleSettings()"
        matTooltip="Open settings menu"
      >
        <div class="settings-button">
          <ng-container *ngIf="user && user.photoUrl; else noPhoto">
            <img src="{{ user.photoUrl }}" class="logo" />
          </ng-container>
          <ng-template #noPhoto>
            <mat-icon>settings</mat-icon>
          </ng-template>
          <div class="settings-text">
            <ng-container *ngIf="user && user.displayName; else noName">
              <span class="user-info">{{ user.displayName }}</span>
            </ng-container>
            <ng-template #noName>
              <div *ngIf="user" class="user-info">Anonymous</div>
            </ng-template>
          </div>
        </div>
      </button>
    </div>
  </div>
</mat-toolbar>
