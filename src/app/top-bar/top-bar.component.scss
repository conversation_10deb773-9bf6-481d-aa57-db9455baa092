mat-icon {
  margin-right: 0.75rem;
  font-size: 1.25rem; /* Smaller icon */
  transition: transform 0.3s ease;
  color: var(--mat-sys-on-primary); /* Ensure icon uses correct color */
}

.top-app-bar > a {
  color: var(
    --mat-sys-on-primary
  ) !important; /* Ensure app title uses correct color */
  font-weight: 500;
}

.spacer {
  min-width: 1rem;
  flex: 1 1 auto;
}

.top-app-bar {
  padding: 0 1rem;
  min-height: 3.5rem; /* Reduced height */
  position: relative;
  overflow: visible;
  background: linear-gradient(
    90deg,
    var(--mat-sys-primary),
    var(--mat-sys-tertiary)
  );
  box-shadow: 0 2px 10px
    color-mix(in srgb, var(--mat-sys-shadow) 20%, transparent);
  display: flex;
  align-items: center;
  z-index: 1000; /* Higher z-index to appear above clue */
  animation: fadeIn 0.5s ease-out;
  justify-content: center;
}

/* Add subtle animated background */
.top-app-bar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: shimmer 3s infinite linear;
  z-index: 1;
  pointer-events: none;
}

/* Make sure content is above the shimmer effect */
.top-app-bar > * {
  position: relative;
  z-index: 2;
}

.top-app-bar a {
  display: flex;
  align-items: center;
  font-size: var(--mat-sys-headline-small); /* Smaller font size */
  font-weight: 600;
  letter-spacing: 0.05em;
  // text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
  height: 3rem; /* Fixed height */
}

.top-app-bar a:hover {
  transform: scale(1.05);
}

.top-app-bar a:hover mat-icon {
  transform: rotate(10deg);
}

.logo {
  height: 2.5em;
  vertical-align: middle;
  margin-right: 1rem;
  border-radius: 100%;
  box-shadow: 0 2px 5px
    color-mix(in srgb, var(--mat-sys-shadow) 20%, transparent);
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.1);
}

.top-app-bar {
  color: var(--mat-sys-on-primary);
}

app-cast {
  height: 80%;
}

.user-info {
  max-width: 150px; /* Limit width */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 0.5rem;
  color: var(--mat-sys-on-primary-container); /* Use Material Design variable */
  font-weight: 500; /* Make it slightly bolder */
}
.settings-button-container {
  position: relative;
  display: inline-block;
}

.settings-button {
  display: flex;
  align-items: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  color: var(--mat-sys-on-primary); /* Use Material Design variable */
}

.settings-text {
  color: var(--mat-sys-on-primary); /* Use Material Design variable */
}

.settings-button mat-icon {
  color: var(--mat-sys-on-primary-container); /* Use Material Design variable */
}

.settings-button-container app-settings {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--mat-sys-secondary-container);
  z-index: 1000; /* Higher z-index to appear above clue */
  border-radius: 1rem;
  margin-top: 1rem;
  box-shadow: 0 10px 30px
    color-mix(in srgb, var(--mat-sys-shadow) 30%, transparent);
  animation: slideInUp 0.3s ease-out;
  border: 1px solid
    color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent);
  overflow: hidden;
  width: auto;
}

/* Add gradient border */
.settings-button-container {
  position: relative;
}

.settings-menu-open::before {
  content: "";
  position: absolute;
  top: 100%;
  right: 0;
  width: 100%;
  height: calc(100% + 1rem);
  z-index: 99;
  margin-top: 1rem;
  border-radius: 1rem;
  background: linear-gradient(
    45deg,
    var(--mat-sys-tertiary),
    var(--mat-sys-primary)
  );
  opacity: 0.7;
  pointer-events: none;
}

.right-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
  padding-right: 0.5rem;
  height: 100%;

  /* Ensure all buttons in the right-buttons container have consistent height */
  & > * {
    display: flex;
    align-items: center;
  }
}

.right-buttons button {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 5px
    color-mix(in srgb, var(--mat-sys-shadow) 20%, transparent);
}

.right-buttons button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px
    color-mix(in srgb, var(--mat-sys-shadow) 30%, transparent);
}

.right-buttons button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 5px
    color-mix(in srgb, var(--mat-sys-shadow) 20%, transparent);
}

/* Media queries for responsive design */
@media screen and (max-width: 600px) {
  .top-app-bar {
    padding: 0 0.5rem;
    min-height: 3rem;
  }

  .right-buttons {
    gap: 0.5rem;
  }

  .right-buttons button[mat-fab] {
    /* Ensure buttons don't exceed toolbar height */
    height: 36px;
    width: auto;
    line-height: 36px;
    min-height: 0;
    padding: 0 0.75rem;
    border-radius: 18px;
  }

  .right-buttons button[mat-mini-fab] {
    margin: 0;
    /* No need for transform scale as we're setting explicit dimensions in the component CSS */
  }

  .logo {
    height: 24px;
    width: 24px;
  }

  .user-info {
    max-width: 100px;
    font-size: 0.875rem;
  }

  .top-app-bar a {
    height: 2.5rem;
    font-size: var(--mat-sys-title-small);
  }
}
