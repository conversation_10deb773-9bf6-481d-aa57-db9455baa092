import {
  Component,
  ElementRef,
  HostListener,
  inject,
  Input,
  OnInit,
  ViewChild
} from '@angular/core';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';

import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { AuthService } from '../services/auth.service';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatTooltipModule } from '@angular/material/tooltip';
import { SettingsComponent } from '../settings/settings.component';
import { User } from '@capacitor-firebase/authentication';

import { SettingsService } from '../services/settings.service';
import { CastComponent } from '../cast/cast.component';
import { RoomComponent } from '../room/room.component';

@Component({
  selector: 'app-top-bar',
  imports: [
    MatToolbarModule,
    MatIconModule,
    CommonModule,
    FormsModule,
    MatSlideToggleModule,
    MatButtonModule,
    MatCardModule,
    MatTooltipModule,
    SettingsComponent,
    MatButtonModule,
    CastComponent
  ],
  templateUrl: './top-bar.component.html',
  styleUrl: './top-bar.component.scss',
  standalone: true
})
export class TopBarComponent implements OnInit {
  @Input() roomId?: string;
  @Input() isCast: boolean = false;
  isLightTheme = false;
  auth = inject(AuthService);
  user?: User;
  elementRef = inject(ElementRef);
  currentRoutePath = '';
  settingsOpen: boolean = false;
  settingsService = inject(SettingsService);
  @ViewChild('settingsButtonContainer') settingsButtonContainer?: ElementRef; //Get a reference to the settings button container
  @HostListener('document:click', ['$event'])
  // undefined if no room.
  onDocumentClick(event: MouseEvent) {
    // Check if the click was outside the settings button container
    if (
      this.settingsOpen &&
      this.settingsButtonContainer &&
      !this.settingsButtonContainer.nativeElement.contains(event.target)
    ) {
      this.settingsOpen = false;
    }
  }
  ngOnInit() {
    this.auth.getAuthState().subscribe(
      user => {
        this.user = user || undefined;
      },
      error => {
        console.error('Auth State Error:', error);
      }
    );
    this.settingsService.settings$.subscribe(settings => {
      if (settings.theme == 'light') {
        document.body.classList.add('light-mode');
      } else {
        document.body.classList.remove('light-mode');
      }
    });
  }
  toggleSettings() {
    this.settingsOpen = !this.settingsOpen;
  }
  getRoomUrl() {
    if (!this.roomId) return undefined;
    return `https://jeopardy-gpt.web.app/castRoom/${this.roomId}`;
  }
}
