import { Directive, ElementRef, OnInit, OnDestroy, Input } from '@angular/core';
import { MediaControlService } from '../services/media-control.service';

@Directive({
  selector: 'video[appMediaRegister], audio[appMediaRegister]',
  standalone: true
})
export class MediaRegisterDirective implements OnInit, OnDestroy {
  @Input() appMediaRegister: string = '';
  private mediaId: string | null = null;

  constructor(
    private elementRef: ElementRef<HTMLVideoElement | HTMLAudioElement>,
    private mediaControlService: MediaControlService
  ) {}

  ngOnInit(): void {
    const element = this.elementRef.nativeElement;
    const src = this.appMediaRegister || element.src || element.currentSrc;
    
    if (src) {
      this.mediaId = this.mediaControlService.registerMediaElement(element, src);
    }
  }

  ngOnDestroy(): void {
    if (this.mediaId) {
      this.mediaControlService.unregisterMediaElement(this.mediaId);
    }
  }
}
