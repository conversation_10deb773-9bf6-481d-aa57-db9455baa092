import { Directive, ElementRef, OnInit, OnDestroy, Input } from '@angular/core';
import { MediaControlService } from '../services/media-control.service';

@Directive({
  selector:
    'video[appMediaRegister], audio[appMediaRegister], iframe[appMediaRegister]',
  standalone: true
})
export class MediaRegisterDirective implements OnInit, OnDestroy {
  @Input() appMediaRegister: string = '';
  private mediaId: string | null = null;

  constructor(
    private elementRef: ElementRef<
      HTMLVideoElement | HTMLAudioElement | HTMLIFrameElement
    >,
    private mediaControlService: MediaControlService
  ) {}

  ngOnInit(): void {
    const element = this.elementRef.nativeElement;
    let src = this.appMediaRegister;

    if (!src) {
      if (element.tagName.toLowerCase() === 'iframe') {
        src = (element as HTMLIFrameElement).src;
      } else {
        const mediaEl = element as HTMLVideoElement | HTMLAudioElement;
        src = mediaEl.src || mediaEl.currentSrc;
      }
    }

    if (src) {
      this.mediaId = this.mediaControlService.registerMediaElement(
        element,
        src
      );
    }
  }

  ngOnDestroy(): void {
    if (this.mediaId) {
      this.mediaControlService.unregisterMediaElement(this.mediaId);
    }
  }
}
