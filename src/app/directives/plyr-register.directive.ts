import { Directive, ElementRef, OnInit, OnDestroy, Input } from '@angular/core';
import { PlyrService } from '../services/plyr.service';

@Directive({
  selector: 'video[appPlyrRegister], audio[appPlyrRegister], [data-plyr-provider][appPlyrRegister]',
  standalone: true
})
export class PlyrRegisterDirective implements OnInit, OnDestroy {
  @Input() appPlyrRegister: any = {};
  private playerId: string | null = null;

  constructor(
    private elementRef: ElementRef<HTMLElement>,
    private plyrService: PlyrService
  ) {}

  async ngOnInit(): Promise<void> {
    const element = this.elementRef.nativeElement;
    
    // Prepare element for Plyr
    this.prepareElement(element);
    
    // Create Plyr player
    try {
      this.playerId = await this.plyrService.createPlayer(element, this.appPlyrRegister);
    } catch (error) {
      console.error('Failed to create Plyr player:', error);
    }
  }

  ngOnDestroy(): void {
    if (this.playerId) {
      this.plyrService.destroy(this.playerId);
    }
  }

  private prepareElement(element: HTMLElement): void {
    const tagName = element.tagName.toLowerCase();
    
    if (tagName === 'iframe') {
      // Convert YouTube iframe to Plyr format
      const iframe = element as HTMLIFrameElement;
      const src = iframe.src;
      
      if (src.includes('youtube.com/embed/')) {
        // Extract video ID from YouTube URL
        const videoId = this.extractYouTubeId(src);
        if (videoId) {
          // Replace iframe with div for Plyr YouTube
          const div = document.createElement('div');
          div.setAttribute('data-plyr-provider', 'youtube');
          div.setAttribute('data-plyr-embed-id', videoId);
          
          // Copy any custom attributes
          Array.from(iframe.attributes).forEach(attr => {
            if (attr.name.startsWith('data-plyr-')) {
              div.setAttribute(attr.name, attr.value);
            }
          });
          
          // Replace the iframe
          iframe.parentNode?.replaceChild(div, iframe);
          this.elementRef.nativeElement = div;
        }
      }
    } else if (tagName === 'video' || tagName === 'audio') {
      // Ensure media elements have proper attributes
      const mediaEl = element as HTMLVideoElement | HTMLAudioElement;
      mediaEl.controls = true;
      mediaEl.preload = 'metadata';
    }
  }

  private extractYouTubeId(url: string): string | null {
    const regex = /(?:youtube\.com\/embed\/|youtu\.be\/)([^?&]+)/;
    const match = url.match(regex);
    return match ? match[1] : null;
  }
}
