import { Directive, ElementRef, OnInit, OnD<PERSON>roy, Input } from '@angular/core';
import { PlyrService } from '../services/plyr.service';

@Directive({
  selector: '[appPlyrRegister]',
  standalone: true
})
export class PlyrRegisterDirective implements OnInit, OnDestroy {
  @Input() appPlyrRegister: any = {};
  @Input() questionId: string = ''; // Add questionId input
  private playerIds: string[] = [];
  private mutationObserver?: MutationObserver;

  constructor(
    private elementRef: ElementRef<HTMLElement>,
    private plyrService: PlyrService
  ) {}

  async ngOnInit(): Promise<void> {
    // Wait a bit for DOM to be fully rendered
    setTimeout(async () => {
      // Initial scan for media elements
      await this.scanAndInitializeMedia();

      // Set up mutation observer to watch for dynamically added content
      this.setupMutationObserver();
    }, 100);
  }

  private async scanAndInitializeMedia(): Promise<void> {
    const container = this.elementRef.nativeElement;

    // Find all media elements within the container that haven't been initialized
    const mediaElements = container.querySelectorAll(
      'video:not([data-plyr-initialized]):not(.plyr), audio:not([data-plyr-initialized]):not(.plyr), iframe[src*="youtube"]:not([data-plyr-initialized]), [data-plyr-provider]:not([data-plyr-initialized])'
    );

    for (const element of Array.from(mediaElements)) {
      await this.initializeMediaElement(element as HTMLElement);
    }
  }

  private async initializeMediaElement(element: HTMLElement): Promise<void> {
    // Skip if already initialized or if it's a Plyr-generated element
    if (
      element.hasAttribute('data-plyr-initialized') ||
      element.classList.contains('plyr') ||
      element.closest('.plyr')
    ) {
      return;
    }

    // Mark as initialized to prevent duplicate processing
    element.setAttribute('data-plyr-initialized', 'true');

    // Prepare element for Plyr
    const preparedElement = this.prepareElement(element);

    // Create Plyr player
    try {
      // Generate a unique questionId if not provided
      const questionId = this.questionId || this.generateQuestionId();
      const playerId = await this.plyrService.createPlayer(
        preparedElement,
        questionId
      );
      this.playerIds.push(playerId);
    } catch (error) {
      console.error('PlyrRegister: Failed to create Plyr player:', error);
    }
  }

  private setupMutationObserver(): void {
    this.mutationObserver = new MutationObserver(mutations => {
      // Debounce mutations to avoid excessive processing
      setTimeout(() => {
        mutations.forEach(mutation => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach(node => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node as HTMLElement;

                // Skip if this is a Plyr-generated element
                if (
                  element.classList.contains('plyr') ||
                  element.querySelector('.plyr') ||
                  element.closest('.plyr')
                ) {
                  return;
                }

                // Check if the added node is a media element
                if (this.isMediaElement(element)) {
                  this.initializeMediaElement(element);
                }
                // Also check for media elements within the added node
                const mediaElements = element.querySelectorAll?.(
                  'video:not([data-plyr-initialized]), audio:not([data-plyr-initialized]), iframe[src*="youtube"]:not([data-plyr-initialized]), [data-plyr-provider]:not([data-plyr-initialized])'
                );
                if (mediaElements) {
                  mediaElements.forEach(mediaEl => {
                    this.initializeMediaElement(mediaEl as HTMLElement);
                  });
                }
              }
            });
          }
        });
      }, 50); // Small debounce delay
    });

    this.mutationObserver.observe(this.elementRef.nativeElement, {
      childList: true,
      subtree: true
    });
  }

  private isMediaElement(element: HTMLElement): boolean {
    const tagName = element.tagName.toLowerCase();
    return (
      tagName === 'video' ||
      tagName === 'audio' ||
      (tagName === 'iframe' &&
        element.getAttribute('src')?.includes('youtube')) ||
      element.hasAttribute('data-plyr-provider')
    );
  }

  ngOnDestroy(): void {
    // Clean up all players
    this.playerIds.forEach(playerId => {
      this.plyrService.destroy(playerId);
    });

    // Clean up mutation observer
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
    }
  }

  private prepareElement(element: HTMLElement): HTMLElement {
    const tagName = element.tagName.toLowerCase();

    if (tagName === 'iframe') {
      // Convert YouTube iframe to Plyr format
      const iframe = element as HTMLIFrameElement;
      const src = iframe.src;

      if (src.includes('youtube.com/embed/')) {
        // Extract video ID from YouTube URL
        const videoId = this.extractYouTubeId(src);
        if (videoId) {
          // Replace iframe with div for Plyr YouTube
          const div = document.createElement('div');
          div.setAttribute('data-plyr-provider', 'youtube');
          div.setAttribute('data-plyr-embed-id', videoId);

          // Copy any custom attributes and annotations
          Array.from(iframe.attributes).forEach(attr => {
            if (
              attr.name.startsWith('data-plyr-') ||
              attr.name.startsWith('data-media-')
            ) {
              div.setAttribute(attr.name, attr.value);
            }
          });

          // Replace the iframe
          iframe.parentNode?.replaceChild(div, iframe);
          return div;
        }
      }
    } else if (tagName === 'video' || tagName === 'audio') {
      // Ensure media elements have proper attributes
      const mediaEl = element as HTMLVideoElement | HTMLAudioElement;
      mediaEl.controls = true;
      mediaEl.preload = 'metadata';

      // Apply annotations
      this.applyMediaAnnotations(mediaEl);
    }

    return element;
  }

  private applyMediaAnnotations(
    element: HTMLVideoElement | HTMLAudioElement
  ): void {
    // Start time annotation
    const startTime = element.getAttribute('data-media-start');
    if (startTime) {
      element.currentTime = parseFloat(startTime);
    }

    // Autoplay annotation
    const autoplay = element.getAttribute('data-media-autoplay');
    if (autoplay === 'true') {
      element.autoplay = true;
      element.muted = true; // Required for autoplay
    }

    // Loop annotation
    const loop = element.getAttribute('data-media-loop');
    if (loop === 'true') {
      element.loop = true;
    }

    // Height annotation
    const height = element.getAttribute('data-media-height');
    if (height) {
      element.style.height = height;
      element.style.width = 'auto'; // Maintain aspect ratio
    }
  }

  private extractYouTubeId(url: string): string | null {
    const regex = /(?:youtube\.com\/embed\/|youtu\.be\/)([^?&]+)/;
    const match = url.match(regex);
    return match ? match[1] : null;
  }

  private generateQuestionId(): string {
    // Generate a fallback questionId based on current time and random number
    return `question_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;
  }
}
