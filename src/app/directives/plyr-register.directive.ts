import { Directive, ElementRef, OnInit, OnD<PERSON>roy, Input } from '@angular/core';
import { PlyrService } from '../services/plyr.service';

@Directive({
  selector: '[appPlyrRegister]',
  standalone: true
})
export class PlyrRegisterDirective implements OnInit, OnDestroy {
  @Input() appPlyrRegister: any = {};
  private playerIds: string[] = [];
  private mutationObserver?: MutationObserver;

  constructor(
    private elementRef: ElementRef<HTMLElement>,
    private plyrService: PlyrService
  ) {}

  async ngOnInit(): Promise<void> {
    // Wait a bit for DOM to be fully rendered
    setTimeout(async () => {
      // Initial scan for media elements
      await this.scanAndInitializeMedia();

      // Set up mutation observer to watch for dynamically added content
      this.setupMutationObserver();
    }, 100);
  }

  private async scanAndInitializeMedia(): Promise<void> {
    const container = this.elementRef.nativeElement;
    console.log(
      'PlyrRegister: Scanning container for media elements:',
      container
    );

    // Find all media elements within the container
    const mediaElements = container.querySelectorAll(
      'video, audio, iframe[src*="youtube"], [data-plyr-provider]'
    );

    console.log(
      'PlyrRegister: Found media elements:',
      mediaElements.length,
      mediaElements
    );

    for (const element of Array.from(mediaElements)) {
      await this.initializeMediaElement(element as HTMLElement);
    }
  }

  private async initializeMediaElement(element: HTMLElement): Promise<void> {
    console.log('PlyrRegister: Initializing media element:', element);

    // Skip if already initialized
    if (element.hasAttribute('data-plyr-initialized')) {
      console.log('PlyrRegister: Element already initialized, skipping');
      return;
    }

    // Mark as initialized to prevent duplicate processing
    element.setAttribute('data-plyr-initialized', 'true');

    // Prepare element for Plyr
    const preparedElement = this.prepareElement(element);
    console.log('PlyrRegister: Prepared element:', preparedElement);

    // Create Plyr player
    try {
      const playerId = await this.plyrService.createPlayer(
        preparedElement,
        this.appPlyrRegister
      );
      this.playerIds.push(playerId);
      console.log('PlyrRegister: Created player with ID:', playerId);
    } catch (error) {
      console.error('PlyrRegister: Failed to create Plyr player:', error);
    }
  }

  private setupMutationObserver(): void {
    this.mutationObserver = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as HTMLElement;
              // Check if the added node is a media element
              if (this.isMediaElement(element)) {
                this.initializeMediaElement(element);
              }
              // Also check for media elements within the added node
              const mediaElements = element.querySelectorAll?.(
                'video, audio, iframe[src*="youtube"], [data-plyr-provider]'
              );
              if (mediaElements) {
                mediaElements.forEach(mediaEl => {
                  this.initializeMediaElement(mediaEl as HTMLElement);
                });
              }
            }
          });
        }
      });
    });

    this.mutationObserver.observe(this.elementRef.nativeElement, {
      childList: true,
      subtree: true
    });
  }

  private isMediaElement(element: HTMLElement): boolean {
    const tagName = element.tagName.toLowerCase();
    return (
      tagName === 'video' ||
      tagName === 'audio' ||
      (tagName === 'iframe' &&
        element.getAttribute('src')?.includes('youtube')) ||
      element.hasAttribute('data-plyr-provider')
    );
  }

  ngOnDestroy(): void {
    // Clean up all players
    this.playerIds.forEach(playerId => {
      this.plyrService.destroy(playerId);
    });

    // Clean up mutation observer
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
    }
  }

  private prepareElement(element: HTMLElement): HTMLElement {
    const tagName = element.tagName.toLowerCase();

    if (tagName === 'iframe') {
      // Convert YouTube iframe to Plyr format
      const iframe = element as HTMLIFrameElement;
      const src = iframe.src;

      if (src.includes('youtube.com/embed/')) {
        // Extract video ID from YouTube URL
        const videoId = this.extractYouTubeId(src);
        if (videoId) {
          // Replace iframe with div for Plyr YouTube
          const div = document.createElement('div');
          div.setAttribute('data-plyr-provider', 'youtube');
          div.setAttribute('data-plyr-embed-id', videoId);

          // Copy any custom attributes
          Array.from(iframe.attributes).forEach(attr => {
            if (attr.name.startsWith('data-plyr-')) {
              div.setAttribute(attr.name, attr.value);
            }
          });

          // Replace the iframe
          iframe.parentNode?.replaceChild(div, iframe);
          return div;
        }
      }
    } else if (tagName === 'video' || tagName === 'audio') {
      // Ensure media elements have proper attributes
      const mediaEl = element as HTMLVideoElement | HTMLAudioElement;
      mediaEl.controls = true;
      mediaEl.preload = 'metadata';
    }

    return element;
  }

  private extractYouTubeId(url: string): string | null {
    const regex = /(?:youtube\.com\/embed\/|youtu\.be\/)([^?&]+)/;
    const match = url.match(regex);
    return match ? match[1] : null;
  }
}
