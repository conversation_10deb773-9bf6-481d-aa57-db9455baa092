import {
  Component,
  Input,
  OnInit,
  OnDestroy,
  SimpleChanges,
  OnChanges,
  Output,
  EventEmitter
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-countdown-timer',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatButtonModule],
  templateUrl: './countdown-timer.component.html',
  styleUrls: ['./countdown-timer.component.scss'],
  host: {
    '[class.warning-time]': 'isWarningTime',
    '[class.critical-time]': 'isCriticalTime',
    '[class.cast-view]': 'horizontal'
  }
})
export class CountdownTimerComponent implements OnInit, OnDestroy, OnChanges {
  @Input() startTime: number | undefined; // When the timer started
  @Input() durationSeconds: number = 60; // Duration in seconds
  @Input() active: boolean = false; // Whether the timer is active
  @Input() showControls: boolean = false; // Whether to show controls (for gamemaster)
  @Input() horizontal: boolean = false; // Whether to show a horizontal progress bar instead of circular

  @Output() startTimer = new EventEmitter<number>(); // Emit when timer starts with duration
  @Output() resetTimer = new EventEmitter<void>(); // Emit when timer resets
  @Output() pauseTimer = new EventEmitter<void>(); // Emit when timer pauses
  @Output() addTime = new EventEmitter<number>(); // Emit when adding time (in seconds)
  @Output() subtractTime = new EventEmitter<number>(); // Emit when subtracting time (in seconds)

  remainingTime: string = '00:00'; // Display format MM:SS
  circumference: number = 2 * Math.PI * 45; // For circular progress indicator
  progress: number = 0;
  private intervalId: any;
  isWarningTime: boolean = false; // True when timer is below warning threshold
  isCriticalTime: boolean = false; // True when timer is below critical threshold
  warningThreshold: number = 10; // Warning threshold in seconds
  criticalThreshold: number = 5; // Critical threshold in seconds

  constructor() {}

  ngOnInit(): void {
    if (this.active) {
      this.startCountdown();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['active'] || changes['startTime']) {
      this.clearTimer();
      if (this.active && this.startTime) {
        this.startCountdown();
      }
    }
  }

  ngOnDestroy(): void {
    this.clearTimer();
  }

  private startCountdown(): void {
    if (!this.startTime || !this.active) {
      return;
    }

    this.intervalId = setInterval(() => {
      this.updateProgressAndRemainingTime();
    }, 50);
  }

  private clearTimer(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  private updateProgressAndRemainingTime(): void {
    if (!this.startTime || !this.active) {
      return;
    }

    const now = Date.now();
    const elapsedMs = now - this.startTime;
    const totalMs = this.durationSeconds * 1000;
    const remainingMs = totalMs - elapsedMs;

    if (remainingMs <= 0) {
      this.remainingTime = '00:00';
      this.progress = this.circumference;
      this.clearTimer();
      return;
    }

    const minutes = Math.floor(remainingMs / (1000 * 60));
    const seconds = Math.floor((remainingMs % (1000 * 60)) / 1000);
    const totalSeconds = minutes * 60 + seconds;

    // Update warning flags based on remaining time
    this.isWarningTime = totalSeconds <= this.warningThreshold;
    this.isCriticalTime = totalSeconds <= this.criticalThreshold;

    // Format remaining time as MM:SS
    this.remainingTime = `${this.pad(minutes)}:${this.pad(seconds)}`;

    // Calculate progress for the circular indicator
    this.progress = (elapsedMs / totalMs) * this.circumference;
  }

  // Helper function to add leading zeros
  private pad(num: number): string {
    return num < 10 ? `0${num}` : `${num}`;
  }

  // Control functions
  onStartTimer(): void {
    this.startTimer.emit(this.durationSeconds);
  }

  onResetTimer(): void {
    this.resetTimer.emit();
  }

  onPauseTimer(): void {
    this.pauseTimer.emit();
  }

  onAddTime(seconds: number): void {
    this.addTime.emit(seconds);
  }

  onSubtractTime(seconds: number): void {
    this.subtractTime.emit(seconds);
  }
}
