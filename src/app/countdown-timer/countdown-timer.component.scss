.countdown-timer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.timer-display {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto;
}

.time-remaining {
  font-size: 24px;
  font-weight: bold;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--timer-stroke-color, #007bff);
}

/* Horizontal timer styles */
.horizontal-timer-display {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.horizontal-timer-bar-container {
  width: 100%;
  background-color: rgba(var(--mat-sys-on-surface-rgb, 0, 0, 0), 0.1);
  border-radius: 1rem;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.3),
    0 0 15px rgba(var(--mat-sys-primary-rgb, 33, 150, 243), 0.7); /* Strong glow with fallback */
  transition: box-shadow 0.3s ease, border-color 0.3s ease;
}

/* Warning state styles for container */
:host.warning-time .horizontal-timer-bar-container {
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.3),
    0 0 15px rgba(var(--mat-sys-warning-rgb, 255, 152, 0), 0.7);
}

/* Warning state styles for cast view */
:host.cast-view.warning-time .horizontal-timer-bar-container {
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(var(--mat-sys-warning-rgb, 255, 152, 0), 0.8);
}

/* Critical state styles for container */
:host.critical-time .horizontal-timer-bar-container {
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(var(--mat-sys-error-rgb, 244, 67, 54), 0.8);
}

/* Critical state styles for cast view */
:host.cast-view.critical-time .horizontal-timer-bar-container {
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.3),
    0 0 25px rgba(var(--mat-sys-error-rgb, 244, 67, 54), 0.9);
}

.horizontal-timer-bar {
  height: 2.5rem;
  background: linear-gradient(
    90deg,
    var(--timer-stroke-color-secondary, var(--mat-sys-tertiary)) 0%,
    var(--timer-stroke-color, #007bff) 100%
  );
  border-radius: 1rem;
  transition: width 0.1s linear, box-shadow 0.3s ease;
  float: right; /* Align to the right side for the countdown effect */
  /* No box-shadow or border on the progress bar */
}

.horizontal-timer-bar.warning-time {
  background: linear-gradient(
    90deg,
    var(--mat-sys-warning-container, #ffcc80) 0%,
    var(--mat-sys-warning, #ff9800) 100%
  );
  animation: pulse-horizontal 1s infinite alternate; /* Override the subtle-pulse animation */
}

.horizontal-timer-bar.critical-time {
  background: linear-gradient(
    90deg,
    var(--mat-sys-error-container, #ffcdd2) 0%,
    var(--mat-sys-error, #f44336) 100%
  );
  animation: pulse-horizontal 0.5s infinite alternate; /* Override the subtle-pulse animation */
}

.horizontal-time-remaining {
  font-size: 24px;
  font-weight: bold;
  color: white;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  width: 100%;
}

/* Larger text for cast view */
:host.cast-view .horizontal-time-remaining {
  font-size: 32px;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8); /* Stronger shadow for TV visibility */
}

.horizontal-time-remaining.warning-time {
  color: var(--mat-sys-warning, #ff9800);
  animation: shake-horizontal 1s infinite;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7); /* Darker shadow for better visibility */
}

:host.cast-view .horizontal-time-remaining.warning-time {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}

.horizontal-time-remaining.critical-time {
  color: var(--mat-sys-error, #f44336);
  animation: shake-horizontal 0.5s infinite;
  font-weight: bolder;
  font-size: 28px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7); /* Darker shadow for better visibility */
}

:host.cast-view .horizontal-time-remaining.critical-time {
  font-size: 36px; /* Even larger font when critical */
  font-weight: 900; /* Extra bold */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}

@keyframes pulse-horizontal {
  from {
    opacity: 0.7;
    box-shadow: 0 0 5px rgba(var(--mat-sys-primary-rgb, 33, 150, 243), 0.5);
  }
  to {
    opacity: 1;
    box-shadow: 0 0 15px rgba(var(--mat-sys-primary-rgb, 33, 150, 243), 0.8);
  }
}

@keyframes subtle-pulse {
  from {
    box-shadow: 0 0 15px rgba(var(--mat-sys-primary-rgb, 33, 150, 243), 0.3);
  }
  to {
    box-shadow: 0 0 25px rgba(var(--mat-sys-primary-rgb, 33, 150, 243), 0.5);
  }
}

@keyframes shake-horizontal {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  25% {
    transform: translate(-50%, -50%) rotate(-2deg) scale(1.05);
  }
  50% {
    transform: translate(-50%, -50%) rotate(0deg) scale(1.1);
  }
  75% {
    transform: translate(-50%, -50%) rotate(2deg) scale(1.05);
  }
  100% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
}

.timer-track {
  stroke: var(--timer-track-color, #eee);
}

.progress-circle {
  stroke: var(--timer-stroke-color, #007bff);
  transition: stroke-dashoffset 0.1s linear, stroke 0.3s ease;
}

/* Warning state (10 seconds or less) */
.progress-circle.warning-time {
  stroke: var(--mat-sys-warning, #ff9800);
  animation: pulse 1s infinite alternate;
}

/* Critical state (5 seconds or less) */
.progress-circle.critical-time {
  stroke: var(--mat-sys-error, #f44336);
  animation: pulse 0.5s infinite alternate;
}

.time-remaining.warning-time {
  color: var(--mat-sys-warning, #ff9800);
  animation: shake 1s infinite;
}

.time-remaining.critical-time {
  color: var(--mat-sys-error, #f44336);
  animation: shake 0.5s infinite;
  font-weight: bolder;
  font-size: 28px; /* Make it bigger */
}

@keyframes pulse {
  from {
    opacity: 0.7;
    stroke-width: 10px;
  }
  to {
    opacity: 1;
    stroke-width: 15px;
  }
}

@keyframes shake {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  25% {
    transform: translate(-50%, -50%) rotate(-1deg);
  }
  50% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  75% {
    transform: translate(-50%, -50%) rotate(1deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
}

.timer-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-top: 0.5rem;
}

.main-controls {
  display: flex;
  gap: 1rem;
}

.time-adjust-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.time-adjust-label {
  font-weight: bold;
  font-size: 1rem;
  color: var(--timer-stroke-color, #007bff);
  min-width: 2.5rem;
  text-align: center;
}
