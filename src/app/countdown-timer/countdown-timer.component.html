<div class="countdown-timer-container">
  <!-- Circular timer display -->
  <div class="timer-display" *ngIf="!horizontal">
    <svg width="120" height="120" viewBox="0 0 120 120">
      <circle
        class="timer-track"
        cx="60"
        cy="60"
        r="45"
        fill="none"
        stroke-width="10"
      />
      <circle
        class="progress-circle"
        [ngClass]="{
          'warning-time': isWarningTime,
          'critical-time': isCriticalTime
        }"
        cx="60"
        cy="60"
        r="45"
        fill="none"
        stroke-width="10"
        [attr.stroke-dasharray]="circumference"
        [attr.stroke-dashoffset]="progress"
        transform="rotate(-90 60 60)"
      />
    </svg>
    <div
      class="time-remaining"
      [ngClass]="{
        'warning-time': isWarningTime,
        'critical-time': isCriticalTime
      }"
    >
      {{ remainingTime }}
    </div>
  </div>

  <!-- Horizontal timer display -->
  <div class="horizontal-timer-display" *ngIf="horizontal">
    <div class="horizontal-timer-bar-container">
      <div
        class="horizontal-timer-bar"
        [ngClass]="{
          'warning-time': isWarningTime,
          'critical-time': isCriticalTime
        }"
        [style.width.%]="100 - (progress / circumference) * 100"
      ></div>
      <div
        class="horizontal-time-remaining"
        [ngClass]="{
          'warning-time': isWarningTime,
          'critical-time': isCriticalTime
        }"
      >
        {{ remainingTime }}
      </div>
    </div>
  </div>

  <div class="timer-controls" *ngIf="showControls">
    <div class="main-controls">
      <button
        mat-mini-fab
        color="primary"
        (click)="onStartTimer()"
        matTooltip="Start Timer"
      >
        <mat-icon>play_arrow</mat-icon>
      </button>
      <button
        mat-mini-fab
        color="accent"
        (click)="onResetTimer()"
        matTooltip="Reset Timer"
      >
        <mat-icon>refresh</mat-icon>
      </button>
      <button
        mat-mini-fab
        color="warn"
        (click)="onPauseTimer()"
        matTooltip="Pause Timer"
      >
        <mat-icon>pause</mat-icon>
      </button>
    </div>

    <div class="time-adjust-controls">
      <button
        mat-mini-fab
        color="primary"
        (click)="onSubtractTime(15)"
        matTooltip="Subtract 15 seconds"
      >
        <mat-icon>remove</mat-icon>
      </button>
      <span class="time-adjust-label">15s</span>
      <button
        mat-mini-fab
        color="primary"
        (click)="onAddTime(15)"
        matTooltip="Add 15 seconds"
      >
        <mat-icon>add</mat-icon>
      </button>
    </div>
  </div>
</div>
