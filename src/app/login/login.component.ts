import { Component, inject } from '@angular/core';
import { AuthService } from '../services/auth.service';
import { User } from '@angular/fire/auth';
import { ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-login',
  imports: [MatCardModule, MatButtonModule, MatTooltipModule, MatIconModule],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent {
  auth = inject(AuthService);
  isLoggedIn: boolean = false;
  user?: User;
  route = inject(ActivatedRoute);
  router = inject(Router);
  constructor() {
    this.route.queryParams.subscribe(params => {
      const redir = params['redirect'] || '/';
      this.auth.getAuthState().subscribe(
        user => {
          if (user) {
            this.router.navigate([redir]);
          }
        },
        error => {
          console.error('Auth State Error:', error);
        }
      );
    });
  }
}
