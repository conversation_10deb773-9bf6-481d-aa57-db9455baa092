:host {
  height: 100%;
  background-image: radial-gradient(
    rgba(255, 255, 255, 0.05) 1px,
    transparent 1px
  );
  background-size: 20px 20px;
  animation: fadeIn 0.5s ease-in-out;
}

.auth-container {
  display: flex;
  justify-content: center;
  flex-direction: column;
  height: 100%;
  align-items: center;
}

.social-buttons {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  gap: 1rem;
  padding-top: 2rem;
  width: 100%;
}

.auth-card {
  display: flex;
  flex-direction: column;
  min-width: 400px;
  width: 50%;
  border-radius: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  background-color: var(--mat-sys-surface-container);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: slideInUp 0.5s ease-out;
  position: relative;

  /* Add gradient border */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    margin: -2px;
    border-radius: inherit;
    opacity: 0.7;
  }
}

.login-title {
  justify-content: center;
  color: var(--mat-sys-on-surface);
  padding: 0.5rem 0;
  background: linear-gradient(
    135deg,
    var(--mat-sys-primary),
    var(--mat-sys-tertiary)
  );

  mat-card-title {
    color: var(--mat-sys-on-primary);
    font-size: 1.75rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.05em;
  }
}

.social-button {
  vertical-align: middle;
  padding: 0.75rem 1.5rem;
  border-radius: 2rem;
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 0.03em;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  background-color: var(--mat-sys-surface-container-high);

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: translateY(1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  /* Add ripple effect */
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
  }

  &:focus:not(:active)::after {
    animation: ripple 1s ease-out;
  }
}

.logo {
  height: 2rem;
  vertical-align: middle;
  margin-right: 1rem;
  border-radius: 100%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
