<div class="button-container">
  <button
    mat-fab
    class="chat-fab tertiary-colors"
    [ngClass]="isChatCollapsed ? '' : 'invisible'"
    (click)="toggleChat()"
    aria-label="Toggle chat"
    matBadge="{{ unreadCount }}"
    matBadgeColor="warn"
    [matBadgeHidden]="unreadCount === 0 || !isChatCollapsed"
    matBadgeSize="large"
    matBadgePosition="above after"
    cdkDrag
    cdkDragBoundary=".button-container"
    (cdkDragStarted)="onDragStarted()"
    (cdkDragEnded)="onDragEnded($event)"
  >
    <mat-icon color="primary">
      {{ isChatCollapsed ? "chat" : "close" }}
    </mat-icon>
  </button>
</div>
<div
  class="chat-container tertiary-colors"
  [ngClass]="isChatCollapsed ? 'invisible' : ''"
>
  <div class="chat-content">
    <div class="chat-header" (click)="toggleChat()">
      <div class="messages-title">Chat</div>
      <mat-icon class="close-chat">close</mat-icon>
    </div>

    <div class="messages" #messagesContainer>
      <ng-container *ngFor="let group of groupedMessages">
        <div
          class="message-group-row"
          [class.sent]="isMyMessage(group[0])"
          [class.received]="!isMyMessage(group[0])"
        >
          <div
            class="message-group"
            [class.sent]="isMyMessage(group[0])"
            [class.received]="!isMyMessage(group[0])"
          >
            <span class="sender">
              {{ getUserNameFromPlayerId(group[0].playerId) }}:
            </span>
            <div class="message-container">
              <ng-container *ngFor="let message of group">
                <span class="text">{{ message.text }}</span>
              </ng-container>
            </div>
          </div>
        </div>
      </ng-container>
    </div>

    <div class="input-area">
      <input
        type="text"
        [(ngModel)]="newMessage"
        placeholder="Enter message"
        (keyup.enter)="sendMessage()"
      />
      <button (click)="sendMessage()">Send</button>
    </div>
  </div>
</div>
