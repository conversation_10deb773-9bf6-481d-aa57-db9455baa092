import {
  Component,
  OnD<PERSON>roy,
  Input,
  inject,
  ViewChild,
  ElementRef,
  AfterViewInit,
  OnChanges,
  OnInit,
  ChangeDetectorRef,
  Renderer2
} from '@angular/core';
import { ChatService } from '../services/chat.service';
import { Message, Room } from '../../../functions/src/resources';
import { Subscription, fromEvent } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { AuthService } from '../services/auth.service';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatBadgeModule } from '@angular/material/badge';
import { debounceTime } from 'rxjs/operators';
import {
  CdkDrag,
  CdkDragEnd,
  CdkDragMove,
  DragDropModule
} from '@angular/cdk/drag-drop';

@Component({
  selector: 'app-chat-room',
  templateUrl: './chat-room.component.html',
  styleUrls: ['./chat-room.component.css'],
  imports: [
    FormsModule,
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatBadgeModule,
    DragDropModule,
    CdkDrag
  ],
  standalone: true
})
export class ChatRoomComponent
  implements OnChanges, OnDestroy, AfterViewInit, OnInit {
  @Input() room?: Room;
  messages: Message[] = [];
  groupedMessages: any[][] = []; // Array for grouped messages
  newMessage = '';
  private messagesSubscription: Subscription | undefined;
  isAllowedInRoom: boolean = false;
  authService = inject(AuthService);
  isChatCollapsed = true;
  unreadCount = 0;
  lastReadMessageId: string | null = null;
  cdr = inject(ChangeDetectorRef);
  private shouldScroll = false;
  renderer = inject(Renderer2);
  isDragging = false;
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  private resizeSubscription!: Subscription;

  constructor(private chatService: ChatService) {}

  ngOnInit(): void {
    // Get last read message from localStorage or default to null
    this.lastReadMessageId =
      localStorage.getItem(
        `lastReadMessage-${this.room?.roomId}-${
          this.authService.getUser()?.uid
        }`
      ) ?? null;
  }
  onDragStarted(): void {
    this.isDragging = true;
  }

  onDragEnded(event: CdkDragEnd): void {
    setTimeout(() => {
      this.isDragging = false;
    }, 200);
  }

  ngAfterViewInit(): void {
    // Subscribe to resize events and recalculate scrolling logic
    this.resizeSubscription = fromEvent(window, 'resize')
      .pipe(debounceTime(500)) // Debounce to prevent excessive calls
      .subscribe(() => {
        if (this.shouldScroll) {
          this.scrollToBottom();
        }
      });
  }

  ngOnChanges(): void {
    if (this.room?.roomId) {
      this.checkRoomAccess();
    }
  }

  isMyMessage(message: Message): boolean {
    return message.playerId == this.authService.getUser()!.uid;
  }

  ngOnDestroy(): void {
    this.messagesSubscription?.unsubscribe();
    this.resizeSubscription?.unsubscribe(); // Unsubscribe on destroy
  }

  getUserNameFromPlayerId(playerId: string) {
    return this.room?.playerInfos[
      this.room?.playerIds.findIndex(p => p == playerId)
    ].userName;
  }

  async checkRoomAccess(): Promise<void> {
    if (this.room?.roomId) {
      if (
        !this.room.playerIds.some(p => p == this.authService.getUser()!.uid)
      ) {
        throw new Error('Client not allowed in room.');
      }
      this.loadMessages();
    }
  }

  loadMessages(): void {
    if (this.room?.roomId) {
      this.messagesSubscription = this.chatService
        .getMessages(this.room.roomId)
        .subscribe(
          (messages: Message[]) => {
            const newMessagesCount = messages.length - this.messages.length;
            const isInitialLoad = this.messages.length === 0;

            this.messages = messages;
            this.groupMessages();
            this.updateUnreadCount();

            if (this.messagesContainer) {
              const previousScrollHeight = this.messagesContainer.nativeElement
                .scrollHeight;
              // Determine if we should scroll to the bottom based on new messages and scroll position
              if (isInitialLoad) {
                this.shouldScroll = true; // Scroll on initial load
              } else if (newMessagesCount > 0) {
                // Only consider scrolling if new messages arrived
                const previousScrollPosition = this.messagesContainer
                  .nativeElement.scrollTop;
                const isNearBottomBeforeUpdate =
                  previousScrollHeight -
                    previousScrollPosition -
                    this.messagesContainer.nativeElement.clientHeight <
                  150;
                this.shouldScroll = isNearBottomBeforeUpdate;
              }

              // Use ChangeDetectorRef to trigger a change detection cycle
              this.cdr.detectChanges();

              if (this.shouldScroll) {
                this.scrollToBottom();
              }
              this.cdr.markForCheck();
            }
          },
          error => {
            console.error('Error loading messages:', error);
          }
        );
    }
  }

  sendMessage(): void {
    const text = this.newMessage.trim();
    this.newMessage = '';
    if (text && this.room?.roomId) {
      this.scrollToBottom();
      this.chatService
        .sendMessage(this.room.roomId, text)
        .then(() => {
          this.markMessagesAsRead();
          // No need to manually group messages here, as the subscription will handle it
        })
        .catch(error => {
          console.error('Error sending message:', error);
        });
    }
  }

  groupMessages() {
    const grouped = this.messages.reduce((acc, message) => {
      const lastGroup = acc[acc.length - 1];
      if (lastGroup && lastGroup[0].playerId === message.playerId) {
        lastGroup.push(message);
      } else {
        acc.push([message]);
      }
      return acc;
    }, [] as Message[][]);
    this.groupedMessages = grouped;
  }

  scrollToBottom(): void {
    try {
      setTimeout(() => {
        console.log(this.messagesContainer.nativeElement.scrollHeight);
        this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;
        this.shouldScroll = false;
      }, 100); // Using a small timeout to ensure DOM update is complete
    } catch (err) {
      console.error(err);
    }
  }

  // Example of deleting a message
  deleteMessage(message: Message): void {
    if (this.room?.roomId && message.id) {
      this.chatService.deleteMessage(this.room.roomId, message.id);
    }
  }

  toggleChat() {
    if (this.isDragging) return;

    this.isChatCollapsed = !this.isChatCollapsed;
    if (!this.isChatCollapsed) {
      this.markMessagesAsRead();
    }
  }

  updateUnreadCount() {
    if (!this.lastReadMessageId) {
      this.unreadCount = this.messages.length;
      return;
    }

    const lastReadIndex = this.messages.findIndex(
      message => message.id === this.lastReadMessageId
    );

    if (lastReadIndex === -1) {
      this.unreadCount = this.messages.length;
    } else {
      this.unreadCount = this.messages.length - lastReadIndex - 1;
    }
  }

  markMessagesAsRead() {
    const lastMessage = this.messages[this.messages.length - 1];
    if (lastMessage) {
      this.lastReadMessageId = lastMessage.id!;
      localStorage.setItem(
        `lastReadMessage-${this.room?.roomId}-${
          this.authService.getUser()?.uid
        }`,
        this.lastReadMessageId!
      );
      this.unreadCount = 0;
    }
  }
}
