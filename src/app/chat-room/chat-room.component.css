:host {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000; /* Ensure the chat is above other content */
  display: block; /* Important for :host to take up space */
  pointer-events: none;
}

.invisible {
  opacity: 0 !important;
  pointer-events: none !important;
}
/* --- Chat Container --- */
.button-container {
  padding: 1rem;
  height: calc(100% - 2rem);
  width: calc(100% - 2rem);
}
.chat-container {
  /* Position within the :host (viewport) */
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  height: auto;
  max-height: 80vh;
  border-radius: 0.625rem;
  box-shadow: var(--mat-sys-level5);
  display: flex;
  flex-direction: column;
  background-color: var(--mat-sys-surface-variant);
  color: var(--mat-sys-on-surface-variant);
  font: var(--mat-sys-body-large);
  transition: height 0.3s ease, max-height 0.3s ease;
  pointer-events: all;
}

.chat-header {
  display: flex;
  align-items: center;
  background-color: var(--mat-sys-primary);
  color: var(--mat-sys-on-primary);
  font: var(--mat-sys-title-large);
  cursor: pointer;
  border-top-left-radius: 0.625rem;
  border-top-right-radius: 0.625rem;
  min-height: 1rem;
  display: flex;
  justify-content: space-between;
  padding: 1rem;
}

.chat-content {
  width: 40vh;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  max-height: inherit;
}
/* --- Messages Area --- */
.messages {
  overflow-y: auto;
  flex-grow: 1;
  padding: 0.5rem;
  scroll-behavior: smooth; /* Optional: Smooth scrolling */
  display: flex;
  flex-direction: column;
}

/* --- Message Group Row --- */
.message-group-row {
  display: flex;
  width: 100%;
}

.message-group-row.sent {
  justify-content: flex-end;
}

.message-group-row.received {
  justify-content: flex-start;
}

/* --- Message Group --- */
.message-group {
  display: flex;
  flex-direction: column;
  width: 80%;
  margin-bottom: 0.25rem;
  border-radius: 0.5rem;
  word-wrap: break-word;
}

.message-group.sent {
  align-self: flex-end;
  background-color: var(--mat-sys-primary-container);
  color: var(--mat-sys-on-primary-container);
  text-align: right;
}

.message-group.received {
  align-self: flex-start;
  background-color: var(--mat-sys-secondary-container);
  color: var(--mat-sys-on-secondary-container);
  text-align: left;
}

.message-group .message-container {
  display: flex;
  flex-direction: column;
}

/* --- Individual Message --- */
.message {
  max-width: 100%;
  margin-bottom: 0.25rem;
  padding: 0.3rem 0.75rem;
  border-radius: 0.5rem;
  word-wrap: break-word;
}

/* --- Sender & Text --- */
.sender {
  margin-bottom: 0.1rem;
  color: var(--mat-sys-primary);
  font: var(--mat-sys-title-medium);
  padding: 0.3rem 0.75rem 0 0.75rem;
  align-self: flex-start;
}

.message.sent .sender,
.sender.isMyText {
  display: none;
}

.text {
  padding: 0.3rem 0.75rem;
}

/* --- Input Area --- */
.input-area {
  display: flex;
  padding: 0.625rem;
  border-top: 0.0625rem solid var(--mat-sys-outline);
}

.input-area input {
  flex-grow: 1;
  padding: 0.5rem 0.75rem;
  margin-right: 0.625rem;
  border: none;
  border-radius: 1.25rem;
  background-color: var(--mat-sys-surface-variant);
  color: var(--mat-sys-on-surface-variant);
  font: var(--mat-sys-title-medium);
  outline: none;
}

.input-area button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 1.25rem;
  background-color: var(--mat-sys-primary);
  color: var(--mat-sys-on-primary);
  font-weight: 500;
  cursor: pointer;
  outline: none;
}

.input-area button:hover {
  background-color: var(--mat-sys-primary-hover);
}

/* --- Chat Toggle FAB --- */
.chat-fab {
  position: absolute;
  bottom: 1em;
  right: 1em;
  pointer-events: all;
  transition: 0s ease;
}

.messages-title {
  text-align: center;
  align-items: center;
  text-align: center;
}
