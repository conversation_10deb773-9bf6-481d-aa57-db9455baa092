import { Injectable, Ng<PERSON>one } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { GameService } from './game.service';
import { FirebaseFirestore } from '@capacitor-firebase/firestore';

export interface CastMediaCommand {
  type: 'play' | 'pause' | 'seek' | 'volume' | 'pauseAll' | 'mute' | 'unmute';
  questionId?: string; // Changed from playerId to questionId
  value?: number; // for seek time or volume
  timestamp: number;
}

@Injectable({
  providedIn: 'root'
})
export class CastMediaControlService {
  private mediaCommands = new BehaviorSubject<CastMediaCommand | null>(null);
  private callbackId?: string;

  mediaCommands$ = this.mediaCommands.asObservable();

  constructor(private gameService: GameService, private zone: NgZone) {}

  async sendPlayCommand(roomId: string, questionId: string): Promise<void> {
    const command: CastMediaCommand = {
      type: 'play',
      questionId,
      timestamp: Date.now()
    };

    await this.sendCommand(roomId, command);
  }

  async sendPauseCommand(roomId: string, questionId: string): Promise<void> {
    const command: CastMediaCommand = {
      type: 'pause',
      questionId,
      timestamp: Date.now()
    };

    await this.sendCommand(roomId, command);
  }

  async sendSeekCommand(
    roomId: string,
    questionId: string,
    time: number
  ): Promise<void> {
    const command: CastMediaCommand = {
      type: 'seek',
      questionId,
      value: time,
      timestamp: Date.now()
    };

    await this.sendCommand(roomId, command);
  }

  async sendVolumeCommand(
    roomId: string,
    questionId: string,
    volume: number
  ): Promise<void> {
    const command: CastMediaCommand = {
      type: 'volume',
      questionId,
      value: volume,
      timestamp: Date.now()
    };

    await this.sendCommand(roomId, command);
  }

  async sendPauseAllCommand(roomId: string): Promise<void> {
    const command: CastMediaCommand = {
      type: 'pauseAll',
      timestamp: Date.now()
    };

    await this.sendCommand(roomId, command);
  }

  async sendMuteCommand(roomId: string, questionId: string): Promise<void> {
    const command: CastMediaCommand = {
      type: 'mute',
      questionId,
      timestamp: Date.now()
    };

    await this.sendCommand(roomId, command);
  }

  async sendUnmuteCommand(roomId: string, questionId: string): Promise<void> {
    const command: CastMediaCommand = {
      type: 'unmute',
      questionId,
      timestamp: Date.now()
    };

    console.log(roomId, command);

    await this.sendCommand(roomId, command);
  }

  private async sendCommand(
    roomId: string,
    command: CastMediaCommand
  ): Promise<void> {
    try {
      await this.gameService.call('sendCastMediaCommand', {
        roomId,
        command
      });
    } catch (error) {
      console.error('Failed to send cast media command:', error);
    }
  }

  // Subscribe to media commands for cast room
  async subscribeToCommands(roomId: string): Promise<void> {
    // Unsubscribe from any existing listener
    if (this.callbackId) {
      await FirebaseFirestore.removeSnapshotListener({
        callbackId: this.callbackId
      });
      this.callbackId = undefined;
    }

    // Subscribe to the latest media command document
    const commandRef = `rooms/${roomId}/mediaCommands/latest`;
    this.callbackId = await FirebaseFirestore.addDocumentSnapshotListener(
      { reference: commandRef },
      (event, error) => {
        this.zone.run(() => {
          if (error) {
            console.error('Error listening to media commands:', error);
          } else if (event?.snapshot.data) {
            const command = event.snapshot.data as CastMediaCommand;
            console.log('Received media command:', command);
            this.mediaCommands.next(command);
          }
        });
      }
    );
  }

  // Unsubscribe from media commands
  async unsubscribeFromCommands(): Promise<void> {
    if (this.callbackId) {
      await FirebaseFirestore.removeSnapshotListener({
        callbackId: this.callbackId
      });
      this.callbackId = undefined;
    }
  }

  // For the cast room to receive commands (if needed in the future)
  processCommand(command: CastMediaCommand): void {
    this.mediaCommands.next(command);
  }
}
