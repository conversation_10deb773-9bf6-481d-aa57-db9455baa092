import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { GameService } from './game.service';

export interface CastMediaCommand {
  type: 'play' | 'pause' | 'seek' | 'volume' | 'pauseAll' | 'mute' | 'unmute';
  playerId?: string;
  value?: number; // for seek time or volume
  timestamp: number;
}

@Injectable({
  providedIn: 'root'
})
export class CastMediaControlService {
  private mediaCommands = new BehaviorSubject<CastMediaCommand | null>(null);

  mediaCommands$ = this.mediaCommands.asObservable();

  constructor(private gameService: GameService) {}

  async sendPlayCommand(roomId: string, playerId: string): Promise<void> {
    const command: CastMediaCommand = {
      type: 'play',
      playerId,
      timestamp: Date.now()
    };

    await this.sendCommand(roomId, command);
  }

  async sendPauseCommand(roomId: string, playerId: string): Promise<void> {
    const command: CastMediaCommand = {
      type: 'pause',
      playerId,
      timestamp: Date.now()
    };

    await this.sendCommand(roomId, command);
  }

  async sendSeekCommand(
    roomId: string,
    playerId: string,
    time: number
  ): Promise<void> {
    const command: CastMediaCommand = {
      type: 'seek',
      playerId,
      value: time,
      timestamp: Date.now()
    };

    await this.sendCommand(roomId, command);
  }

  async sendVolumeCommand(
    roomId: string,
    playerId: string,
    volume: number
  ): Promise<void> {
    const command: CastMediaCommand = {
      type: 'volume',
      playerId,
      value: volume,
      timestamp: Date.now()
    };

    await this.sendCommand(roomId, command);
  }

  async sendPauseAllCommand(roomId: string): Promise<void> {
    const command: CastMediaCommand = {
      type: 'pauseAll',
      timestamp: Date.now()
    };

    await this.sendCommand(roomId, command);
  }

  async sendMuteCommand(roomId: string, playerId: string): Promise<void> {
    const command: CastMediaCommand = {
      type: 'mute',
      playerId,
      timestamp: Date.now()
    };

    await this.sendCommand(roomId, command);
  }

  async sendUnmuteCommand(roomId: string, playerId: string): Promise<void> {
    const command: CastMediaCommand = {
      type: 'unmute',
      playerId,
      timestamp: Date.now()
    };

    await this.sendCommand(roomId, command);
  }

  private async sendCommand(
    roomId: string,
    command: CastMediaCommand
  ): Promise<void> {
    try {
      await this.gameService.call('sendCastMediaCommand', {
        roomId,
        command
      });
    } catch (error) {
      console.error('Failed to send cast media command:', error);
    }
  }

  // For the cast room to receive commands (if needed in the future)
  processCommand(command: CastMediaCommand): void {
    this.mediaCommands.next(command);
  }
}
