import { Injectable, inject } from '@angular/core';
import { FirebaseFunctions } from '@capacitor-firebase/functions';
import { LlmProgressService, LlmProgressUpdate } from './llm-progress.service';

@Injectable({
  providedIn: 'root'
})
export class GenerationProgressService {
  private llmProgressService = inject(LlmProgressService);

  constructor() {}

  /**
   * Update the generation progress in Firebase
   * @param roomId The ID of the room to update
   * @param progressUpdate The progress update object
   */
  async updateGenerationProgress(roomId: string, progressUpdate: LlmProgressUpdate): Promise<void> {
    try {
      await FirebaseFunctions.callByName({
        name: 'updateGenerationProgress',
        data: {
          roomId,
          progress: progressUpdate.progress,
          message: progressUpdate.message,
          category: progressUpdate.category,
          questionIndex: progressUpdate.questionIndex
        }
      });
    } catch (error) {
      console.error('Error updating generation progress:', error);
    }
  }

  /**
   * Set the room state to "GeneratingQuestions" to indicate that question generation is in progress
   * @param roomId The ID of the room to update
   */
  async startGenerationPhase(roomId: string): Promise<void> {
    try {
      await FirebaseFunctions.callByName({
        name: 'startGenerationPhase',
        data: {
          roomId
        }
      });
    } catch (error) {
      console.error('Error starting generation phase:', error);
    }
  }
}
