import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { CastMediaCommand } from './cast-media-control.service';

declare const Plyr: any;

export interface PlyrInstance {
  id: string;
  player: any;
  element: HTMLElement;
  type: 'video' | 'audio' | 'youtube';
  src: string;
  title: string;
}

@Injectable({
  providedIn: 'root'
})
export class PlyrService {
  private players = new Map<string, PlyrInstance>();
  private currentlyPlaying = new BehaviorSubject<string | null>(null);
  private plyrLoaded = false;

  currentlyPlaying$ = this.currentlyPlaying.asObservable();

  constructor() {
    this.loadPlyr();
  }

  private async loadPlyr(): Promise<void> {
    if (this.plyrLoaded) return;

    return new Promise(resolve => {
      // Load Plyr CSS
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = 'https://cdn.plyr.io/3.7.8/plyr.css';
      document.head.appendChild(link);

      // Load Plyr JS
      const script = document.createElement('script');
      script.src = 'https://cdn.plyr.io/3.7.8/plyr.polyfilled.js';
      script.onload = () => {
        this.plyrLoaded = true;
        resolve();
      };
      document.head.appendChild(script);
    });
  }

  async createPlayer(element: HTMLElement, options: any = {}): Promise<string> {
    await this.loadPlyr();

    // Check if element already has a Plyr instance
    if (
      element.hasAttribute('data-plyr-id') ||
      element.classList.contains('plyr')
    ) {
      return element.getAttribute('data-plyr-id') || '';
    }

    const id = this.generateId();
    const src = this.extractSrc(element);
    const type = this.getMediaType(element, src);
    const title = this.getMediaTitle(src, type);

    // Mark element with our ID
    element.setAttribute('data-plyr-id', id);

    // Extract annotations from element
    const annotations = this.extractAnnotations(element);

    // Default Plyr options
    const defaultOptions = {
      controls: [
        'play-large',
        'play',
        'progress',
        'current-time',
        'duration',
        'mute',
        'volume',
        'fullscreen'
      ],
      autoplay: annotations.autoplay,
      muted: annotations.autoplay, // Mute if autoplay is enabled
      hideControls: false,
      clickToPlay: true,
      keyboard: { focused: true, global: false },
      tooltips: { controls: true, seek: true },
      captions: { active: false, update: false, language: 'auto' },
      loop: { active: annotations.loop }
    };

    // YouTube specific options
    if (type === 'youtube') {
      defaultOptions.controls.push('settings');
      Object.assign(defaultOptions, {
        youtube: {
          noCookie: false,
          rel: 0,
          showinfo: 0,
          iv_load_policy: 3,
          modestbranding: 1
        }
      });
    }

    const finalOptions = { ...defaultOptions, ...options };
    const player = new Plyr(element, finalOptions);

    // Add event listeners
    player.on('play', () => {
      this.pauseAllExcept(id);
      this.currentlyPlaying.next(id);
    });

    player.on('pause', () => {
      if (this.currentlyPlaying.value === id) {
        this.currentlyPlaying.next(null);
      }
    });

    player.on('ended', () => {
      if (this.currentlyPlaying.value === id) {
        this.currentlyPlaying.next(null);
      }
    });

    // Apply start time if specified
    if (annotations.startTime !== null) {
      player.on('ready', () => {
        player.currentTime = annotations.startTime;
      });
    }

    // Apply start delay if specified
    if (annotations.startDelay !== null && annotations.autoplay) {
      player.on('ready', () => {
        setTimeout(() => {
          player.play();
        }, annotations.startDelay! * 1000); // Convert to milliseconds
      });
    }

    // Handle end time for looping
    if (annotations.endTime) {
      player.on('timeupdate', () => {
        if (player.currentTime >= annotations!.endTime!) {
          if (annotations.loop) {
            player.currentTime = annotations.startTime || 0;
          } else {
            player.pause();
          }
        }
      });
    }

    // Apply height styling
    if (annotations.height) {
      const playerElement = player.elements.container;
      if (playerElement) {
        playerElement.style.height = annotations.height;
        playerElement.style.width = 'auto';
      }
    }

    const plyrInstance: PlyrInstance = {
      id,
      player,
      element,
      type,
      src,
      title
    };

    this.players.set(id, plyrInstance);
    return id;
  }

  private extractSrc(element: HTMLElement): string {
    if (element.tagName.toLowerCase() === 'iframe') {
      return (element as HTMLIFrameElement).src;
    } else if (element.tagName.toLowerCase() === 'video') {
      return (element as HTMLVideoElement).src;
    } else if (element.tagName.toLowerCase() === 'audio') {
      return (element as HTMLAudioElement).src;
    }
    return element.getAttribute('data-plyr-provider') || '';
  }

  private getMediaType(
    element: HTMLElement,
    src: string
  ): 'video' | 'audio' | 'youtube' {
    if (src.includes('youtube.com') || src.includes('youtu.be')) {
      return 'youtube';
    }
    return element.tagName.toLowerCase() as 'video' | 'audio';
  }

  private getMediaTitle(src: string, type: string): string {
    if (type === 'youtube') {
      return 'YouTube Video';
    }
    try {
      const url = new URL(src);
      const filename = url.pathname.split('/').pop() || 'Media';
      return filename.length > 30
        ? filename.substring(0, 27) + '...'
        : filename;
    } catch {
      return src.length > 30 ? src.substring(0, 27) + '...' : src;
    }
  }

  private extractAnnotations(
    element: HTMLElement
  ): {
    autoplay: boolean;
    loop: boolean;
    startTime: number | null;
    endTime: number | null;
    height: string | null;
    startDelay: number | null;
  } {
    return {
      autoplay: element.getAttribute('data-media-autoplay') === 'true',
      loop: element.getAttribute('data-media-loop') === 'true',
      startTime: element.getAttribute('data-media-start')
        ? parseFloat(element.getAttribute('data-media-start')!)
        : null,
      endTime: element.getAttribute('data-media-end')
        ? parseFloat(element.getAttribute('data-media-end')!)
        : null,
      height: element.getAttribute('data-media-height'),
      startDelay: element.getAttribute('data-media-start-delay')
        ? parseFloat(element.getAttribute('data-media-start-delay')!)
        : null
    };
  }

  private generateId(): string {
    return (
      'plyr_' +
      Math.random()
        .toString(36)
        .substr(2, 9)
    );
  }

  getPlayer(id: string): any {
    return this.players.get(id)?.player;
  }

  getAllPlayers(): PlyrInstance[] {
    return Array.from(this.players.values());
  }

  play(id: string): void {
    const instance = this.players.get(id);
    if (instance) {
      this.pauseAllExcept(id);
      instance.player.play();
    }
  }

  pause(id: string): void {
    const instance = this.players.get(id);
    if (instance) {
      instance.player.pause();
    }
  }

  pauseAll(): void {
    this.players.forEach(instance => {
      if (!instance.player.paused) {
        instance.player.pause();
      }
    });
    this.currentlyPlaying.next(null);
  }

  private pauseAllExcept(excludeId: string): void {
    this.players.forEach((instance, id) => {
      if (id !== excludeId && !instance.player.paused) {
        instance.player.pause();
      }
    });
  }

  seek(id: string, time: number): void {
    const instance = this.players.get(id);
    if (instance) {
      instance.player.currentTime = time;
    }
  }

  setVolume(id: string, volume: number): void {
    const instance = this.players.get(id);
    if (instance) {
      instance.player.volume = Math.max(0, Math.min(1, volume));
    }
  }

  mute(id: string): void {
    const instance = this.players.get(id);
    if (instance) {
      instance.player.muted = true;
    }
  }

  unmute(id: string): void {
    const instance = this.players.get(id);
    if (instance) {
      instance.player.muted = false;
    }
  }

  toggleMute(id: string): void {
    const instance = this.players.get(id);
    if (instance) {
      instance.player.muted = !instance.player.muted;
    }
  }

  isMuted(id: string): boolean {
    const instance = this.players.get(id);
    return instance ? instance.player.muted : false;
  }

  destroy(id: string): void {
    const instance = this.players.get(id);
    if (instance) {
      instance.player.destroy();
      this.players.delete(id);
      if (this.currentlyPlaying.value === id) {
        this.currentlyPlaying.next(null);
      }
    }
  }

  destroyAll(): void {
    this.players.forEach((instance, id) => {
      instance.player.destroy();
    });
    this.players.clear();
    this.currentlyPlaying.next(null);
  }

  isPlaying(id: string): boolean {
    return this.currentlyPlaying.value === id;
  }

  // Execute cast media command
  executeCastCommand(command: CastMediaCommand): void {
    console.log('Executing cast command:', command);
    switch (command.type) {
      case 'play':
        if (command.playerId) {
          this.play(command.playerId);
        }
        break;
      case 'pause':
        if (command.playerId) {
          this.pause(command.playerId);
        }
        break;
      case 'seek':
        if (command.playerId && command.value !== undefined) {
          this.seek(command.playerId, command.value);
        }
        break;
      case 'volume':
        if (command.playerId && command.value !== undefined) {
          this.setVolume(command.playerId, command.value);
        }
        break;
      case 'mute':
        if (command.playerId) {
          this.mute(command.playerId);
        }
        break;
      case 'unmute':
        if (command.playerId) {
          this.unmute(command.playerId);
        }
        break;
      case 'pauseAll':
        this.pauseAll();
        break;
      default:
        console.warn('Unknown cast command type:', command.type);
    }
  }
}
