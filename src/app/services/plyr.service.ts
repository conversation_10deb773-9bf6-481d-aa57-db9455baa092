import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { CastMediaCommand } from './cast-media-control.service';

declare const Plyr: any;

export interface PlyrInstance {
  id: string;
  questionId: string;
  player: any;
  element: HTMLElement;
  type: 'video' | 'audio' | 'youtube';
  src: string;
  title: string;
}

@Injectable({
  providedIn: 'root'
})
export class PlyrService {
  private players = new Map<string, PlyrInstance>();
  private currentlyPlaying = new BehaviorSubject<string | null>(null);
  private plyrLoaded = false;

  currentlyPlaying$ = this.currentlyPlaying.asObservable();

  constructor() {
    this.loadPlyr();
  }

  private async loadPlyr(): Promise<void> {
    if (this.plyrLoaded) return;

    return new Promise(resolve => {
      // Check if JS is already loaded
      if (typeof window !== 'undefined' && (window as any).Plyr) {
        this.plyrLoaded = true;
        resolve();
        return;
      }

      // Load Plyr JS only (CSS will be added to index.html)
      const script = document.createElement('script');
      script.src = 'https://cdn.plyr.io/3.7.8/plyr.polyfilled.js';
      script.onload = () => {
        this.plyrLoaded = true;
        resolve();
      };
      script.onerror = () => {
        console.error('Failed to load Plyr script');
        this.plyrLoaded = true; // Mark as loaded to prevent retries
        resolve();
      };
      document.head.appendChild(script);
    });
  }

  async createPlayer(
    element: HTMLElement,
    questionId: string,
    options: any = {}
  ): Promise<string> {
    await this.loadPlyr();

    // Check if element already has a Plyr instance
    if (
      element.hasAttribute('data-plyr-id') ||
      element.classList.contains('plyr')
    ) {
      return element.getAttribute('data-plyr-id') || '';
    }

    // Use questionId as the player ID for easier management
    const id = questionId;
    console.log('PlyrService: Creating player with questionId:', questionId);
    const src = this.extractSrc(element);
    const type = this.getMediaType(element, src);
    const title = this.getMediaTitle(src, type);

    // Mark element with our ID
    element.setAttribute('data-plyr-id', id);

    // Extract annotations from element
    const annotations = this.extractAnnotations(element);

    // Default Plyr options
    const defaultOptions = {
      controls: [
        'play-large',
        'play',
        'progress',
        'current-time',
        'duration',
        'mute',
        'volume',
        'fullscreen'
      ],
      autoplay: annotations.autoplay,
      muted: false, // Mute if autoplay is enabled
      hideControls: false,
      keyboard: { focused: true, global: false },
      tooltips: { controls: true, seek: true },
      loop: { active: annotations.loop },
      title: '', // Hide title to prevent spoilers
      hideYouTubeDOMError: true
    };

    // YouTube specific options
    if (type === 'youtube') {
      defaultOptions.controls.push('settings');
      Object.assign(defaultOptions, {
        youtube: {
          noCookie: false,
          showinfo: 0,
          iv_load_policy: 3,
          modestbranding: 1
        }
      });
    }

    const finalOptions = { ...defaultOptions, ...options };

    // Try to create Plyr player, fallback to basic HTML5 if it fails
    let player: any;
    try {
      if (typeof window !== 'undefined' && (window as any).Plyr) {
        player = new (window as any).Plyr(element, finalOptions);
      } else {
        console.warn('Plyr not available, using basic HTML5 player');
        player = this.createBasicPlayer(element);
      }
    } catch (error) {
      console.error(
        'Failed to create Plyr player, using basic HTML5 player:',
        error
      );
      player = this.createBasicPlayer(element);
    }

    // Add event listeners
    player.on('play', () => {
      this.pauseAllExcept(id);
      this.currentlyPlaying.next(id);
    });

    player.on('pause', () => {
      if (this.currentlyPlaying.value === id) {
        this.currentlyPlaying.next(null);
      }
    });

    player.on('ended', () => {
      if (this.currentlyPlaying.value === id) {
        this.currentlyPlaying.next(null);
      }
    });

    // Apply start time if specified
    if (annotations.startTime !== null) {
      player.on('ready', () => {
        player.currentTime = annotations.startTime;
      });
    }

    // Apply start delay if specified
    if (annotations.startDelay !== null && annotations.autoplay) {
      player.on('ready', () => {
        setTimeout(() => {
          player.play();
        }, annotations.startDelay! * 1000); // Convert to milliseconds
      });
    }

    // Handle end time for looping
    if (annotations.endTime) {
      player.on('timeupdate', () => {
        if (player.currentTime >= annotations!.endTime!) {
          if (annotations.loop) {
            // Loop immediately without delay
            player.currentTime = annotations.startTime || 0;
          } else {
            player.pause();
          }
        }
      });
    }

    // Apply height styling
    if (annotations.height) {
      const playerElement = player.elements.container;
      if (playerElement) {
        playerElement.style.height = annotations.height;
        playerElement.style.width = 'auto';
      }
    }

    const plyrInstance: PlyrInstance = {
      id,
      questionId,
      player,
      element,
      type,
      src,
      title
    };

    this.players.set(id, plyrInstance);
    return id;
  }

  private extractSrc(element: HTMLElement): string {
    if (element.tagName.toLowerCase() === 'iframe') {
      return (element as HTMLIFrameElement).src;
    } else if (element.tagName.toLowerCase() === 'video') {
      return (element as HTMLVideoElement).src;
    } else if (element.tagName.toLowerCase() === 'audio') {
      return (element as HTMLAudioElement).src;
    }
    return element.getAttribute('data-plyr-provider') || '';
  }

  private getMediaType(
    element: HTMLElement,
    src: string
  ): 'video' | 'audio' | 'youtube' {
    if (src.includes('youtube.com') || src.includes('youtu.be')) {
      return 'youtube';
    }
    return element.tagName.toLowerCase() as 'video' | 'audio';
  }

  private getMediaTitle(src: string, type: string): string {
    if (type === 'youtube') {
      return 'YouTube Video';
    }
    try {
      const url = new URL(src);
      const filename = url.pathname.split('/').pop() || 'Media';
      return filename.length > 30
        ? filename.substring(0, 27) + '...'
        : filename;
    } catch {
      return src.length > 30 ? src.substring(0, 27) + '...' : src;
    }
  }

  private extractAnnotations(
    element: HTMLElement
  ): {
    autoplay: boolean;
    loop: boolean;
    startTime: number | null;
    endTime: number | null;
    height: string | null;
    startDelay: number | null;
  } {
    return {
      autoplay: element.getAttribute('data-media-autoplay') === 'true',
      loop: element.getAttribute('data-media-loop') === 'true',
      startTime: element.getAttribute('data-media-start')
        ? parseFloat(element.getAttribute('data-media-start')!)
        : null,
      endTime: element.getAttribute('data-media-end')
        ? parseFloat(element.getAttribute('data-media-end')!)
        : null,
      height: element.getAttribute('data-media-height'),
      startDelay: element.getAttribute('data-media-start-delay')
        ? parseFloat(element.getAttribute('data-media-start-delay')!)
        : null
    };
  }

  private createBasicPlayer(element: HTMLElement): any {
    // Create a basic player wrapper for HTML5 media elements
    element.classList.add('plyr');

    // Ensure controls are enabled
    if (
      element.tagName.toLowerCase() === 'video' ||
      element.tagName.toLowerCase() === 'audio'
    ) {
      (element as HTMLMediaElement).controls = true;
    }

    // Create a basic player interface that mimics Plyr API
    return {
      play: () => {
        if (
          element.tagName.toLowerCase() === 'video' ||
          element.tagName.toLowerCase() === 'audio'
        ) {
          (element as HTMLMediaElement).play().catch(console.error);
        }
      },
      pause: () => {
        if (
          element.tagName.toLowerCase() === 'video' ||
          element.tagName.toLowerCase() === 'audio'
        ) {
          (element as HTMLMediaElement).pause();
        }
      },
      get currentTime() {
        if (
          element.tagName.toLowerCase() === 'video' ||
          element.tagName.toLowerCase() === 'audio'
        ) {
          return (element as HTMLMediaElement).currentTime;
        }
        return 0;
      },
      set currentTime(time: number) {
        if (
          element.tagName.toLowerCase() === 'video' ||
          element.tagName.toLowerCase() === 'audio'
        ) {
          (element as HTMLMediaElement).currentTime = time;
        }
      },
      get volume() {
        if (
          element.tagName.toLowerCase() === 'video' ||
          element.tagName.toLowerCase() === 'audio'
        ) {
          return (element as HTMLMediaElement).volume;
        }
        return 1;
      },
      set volume(vol: number) {
        if (
          element.tagName.toLowerCase() === 'video' ||
          element.tagName.toLowerCase() === 'audio'
        ) {
          (element as HTMLMediaElement).volume = vol;
        }
      },
      get muted() {
        if (
          element.tagName.toLowerCase() === 'video' ||
          element.tagName.toLowerCase() === 'audio'
        ) {
          return (element as HTMLMediaElement).muted;
        }
        return false;
      },
      set muted(mute: boolean) {
        if (
          element.tagName.toLowerCase() === 'video' ||
          element.tagName.toLowerCase() === 'audio'
        ) {
          (element as HTMLMediaElement).muted = mute;
        }
      },
      get paused() {
        if (
          element.tagName.toLowerCase() === 'video' ||
          element.tagName.toLowerCase() === 'audio'
        ) {
          return (element as HTMLMediaElement).paused;
        }
        return true;
      },
      on: (event: string, callback: () => void) => {
        if (
          element.tagName.toLowerCase() === 'video' ||
          element.tagName.toLowerCase() === 'audio'
        ) {
          element.addEventListener(event, callback);
        }
      },
      destroy: () => {
        // Basic cleanup
        element.classList.remove('plyr');
      },
      elements: {
        container: element
      }
    };
  }

  private generateId(): string {
    return (
      'plyr_' +
      Math.random()
        .toString(36)
        .substr(2, 9)
    );
  }

  getPlayer(id: string): any {
    return this.players.get(id)?.player;
  }

  getAllPlayers(): PlyrInstance[] {
    return Array.from(this.players.values());
  }

  play(id: string): void {
    const instance = this.players.get(id);
    if (instance) {
      this.pauseAllExcept(id);
      instance.player.play();
    }
  }

  pause(id: string): void {
    const instance = this.players.get(id);
    if (instance) {
      instance.player.pause();
    }
  }

  pauseAll(): void {
    this.players.forEach(instance => {
      if (!instance.player.paused) {
        instance.player.pause();
      }
    });
    this.currentlyPlaying.next(null);
  }

  private pauseAllExcept(excludeId: string): void {
    this.players.forEach((instance, id) => {
      if (id !== excludeId && !instance.player.paused) {
        instance.player.pause();
      }
    });
  }

  seek(id: string, time: number): void {
    const instance = this.players.get(id);
    if (instance) {
      instance.player.currentTime = time;
    }
  }

  seekToStart(id: string): void {
    const instance = this.players.get(id);
    if (instance) {
      // Check if element has a start time annotation
      const startTime = instance.element.getAttribute('data-media-start');
      const seekTime = startTime ? parseFloat(startTime) : 0;
      instance.player.currentTime = seekTime;
    }
  }

  setVolume(id: string, volume: number): void {
    const instance = this.players.get(id);
    if (instance) {
      instance.player.volume = Math.max(0, Math.min(1, volume));
    }
  }

  mute(id: string): void {
    const instance = this.players.get(id);
    if (instance) {
      instance.player.muted = true;
    }
  }

  unmute(id: string): void {
    const instance = this.players.get(id);
    if (instance) {
      instance.player.muted = false;
    }
  }

  toggleMute(id: string): void {
    const instance = this.players.get(id);
    if (instance) {
      instance.player.muted = !instance.player.muted;
    }
  }

  isMuted(id: string): boolean {
    const instance = this.players.get(id);
    return instance ? instance.player.muted : false;
  }

  destroy(id: string): void {
    const instance = this.players.get(id);
    if (instance) {
      instance.player.destroy();
      this.players.delete(id);
      if (this.currentlyPlaying.value === id) {
        this.currentlyPlaying.next(null);
      }
    }
  }

  destroyAll(): void {
    this.players.forEach((instance, id) => {
      instance.player.destroy();
    });
    this.players.clear();
    this.currentlyPlaying.next(null);
  }

  isPlaying(id: string): boolean {
    return this.currentlyPlaying.value === id;
  }

  // Execute cast media command
  executeCastCommand(command: CastMediaCommand): void {
    console.log('Executing cast command:', command);
    console.log('Available players:', Array.from(this.players.keys()));
    console.log('Looking for player with questionId:', command.questionId);
    switch (command.type) {
      case 'play':
        if (command.questionId) {
          this.play(command.questionId);
        }
        break;
      case 'pause':
        if (command.questionId) {
          this.pause(command.questionId);
        }
        break;
      case 'seek':
        if (command.questionId && command.value !== undefined) {
          this.seek(command.questionId, command.value);
        }
        break;
      case 'volume':
        if (command.questionId && command.value !== undefined) {
          this.setVolume(command.questionId, command.value);
        }
        break;
      case 'mute':
        if (command.questionId) {
          this.mute(command.questionId);
        }
        break;
      case 'unmute':
        if (command.questionId) {
          this.unmute(command.questionId);
        }
        break;
      case 'pauseAll':
        this.pauseAll();
        break;
      default:
        console.warn('Unknown cast command type:', command.type);
    }
  }
}
