import { inject, Injectable } from '@angular/core';
import { BehaviorSubject, from, Observable, of, switchMap, tap } from 'rxjs';
import { GameService } from './game.service';
import {
  FirebaseAuthentication,
  User
} from '@capacitor-firebase/authentication';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  gs = inject(GameService);
  authBehaviorSubject = new BehaviorSubject<User | null>(null);
  user: User | null = null;
  authState = this.authBehaviorSubject.asObservable();

  constructor() {
    console.log('AuthService constructor ');
    FirebaseAuthentication.addListener('authStateChange', state => {
      console.log('Auth State Change: ', state);
      this.authBehaviorSubject.next(state.user);
      this.user = state.user;
    });
  }

  getUser() {
    return this.user;
  }

  // Google Sign-in
  signInWithGoogle(): Observable<User | null> {
    return from(FirebaseAuthentication.signInWithGoogle()).pipe(
      tap(result => {
        this.authBehaviorSubject.next(result.user);
        this.user = result.user;
      }),
      switchMap(() => this.authState)
    );
  }

  // Anonymous Sign-in
  signInAnonymously(): Observable<User | null> {
    return from(FirebaseAuthentication.signInAnonymously()).pipe(
      tap(result => {
        this.authBehaviorSubject.next(result.user);
        this.user = result.user;
      }),
      switchMap(() => this.authState)
    );
  }

  // Sign out (works for all providers)
  logout(): Promise<void> {
    return FirebaseAuthentication.signOut();
  }

  getAuthState(): Observable<User | null> {
    return this.authState!;
  }
}
