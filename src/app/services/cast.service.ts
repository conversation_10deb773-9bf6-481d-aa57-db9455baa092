import { Injectable, Ng<PERSON>one } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CastService {
  private castContext: cast.framework.CastContext | null = null;
  private sessionStateEventDataSource = new BehaviorSubject<cast.framework.SessionStateEventData | null>(
    null
  );
  sessionStateEventData$: Observable<cast.framework.SessionStateEventData | null> = this.sessionStateEventDataSource.asObservable();

  private scriptLoadSubject = new BehaviorSubject<boolean>(false);
  isCastApiReady$ = this.scriptLoadSubject.asObservable();

  constructor(private ngZone: NgZone) {
    window['__onGCastApiAvailable'] = (isAvailable: boolean) => {
      if (isAvailable) {
        this.initializeCastApi();
      }
    };
    this.loadScript();
  }

  private loadScript(): void {
    const script = document.createElement('script');
    script.src =
      'https://www.gstatic.com/cv/js/sender/v1/cast_sender.js?loadCastFramework=1';
    script.async = true;
    script.defer = true;

    script.onload = () => {
      this.scriptLoadSubject.next(true);
    };

    script.onerror = () => {
      console.error('Error loading Google Cast SDK script');
      this.scriptLoadSubject.next(false);
    };

    document.head.appendChild(script);
  }

  private initializeCastApi(): void {
    this.ngZone.runOutsideAngular(() => {
      this.castContext = cast.framework.CastContext.getInstance();
      this.castContext!.setOptions({
        receiverApplicationId: '5C3F0A3C', // Or your custom app ID
        autoJoinPolicy: chrome.cast.AutoJoinPolicy.ORIGIN_SCOPED
      });
      console.log('Cast API Initialized');

      // Listen for session state changes and emit through the subject
      this.castContext!.addEventListener(
        cast.framework.CastContextEventType.SESSION_STATE_CHANGED,
        (event: cast.framework.SessionStateEventData) => {
          this.sessionStateEventDataSource.next(event);
        }
      );
    });
  }

  getCastContext(): cast.framework.CastContext | null {
    return this.castContext;
  }
}
