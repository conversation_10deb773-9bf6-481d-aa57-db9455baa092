import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class LruCacheService {
  private caches: { [name: string]: LRUCache<any> } = {};

  constructor() {}

  getOrCreateCache(name: string): LRUCache<any> {
    if (!this.caches[name]) {
      this.caches[name] = new LRUCache<any>(100, name);
    }
    return this.caches[name];
  }
}

export class LRUCache<T> {
  private capacity: number;
  private cache: Map<string, T>;
  private keyOrder: string[];
  private storageKey: string;

  constructor(capacity: number, name: string) {
    this.capacity = capacity;
    this.cache = new Map<string, T>();
    this.keyOrder = [];
    this.storageKey = `lruCache_${name}`;
    this.loadFromLocalStorage();
  }

  get(key: string): T | null {
    if (this.cache.has(key)) {
      this.updateKeyOrder(key);
      return this.cache.get(key)!; // Non-null assertion since we checked for existence
    }
    return null;
  }

  list(): Array<T> {
    return Array.from(this.cache.values());
  }

  set(key: string, value: T): void {
    if (this.cache.has(key)) {
      this.cache.set(key, value);
      this.updateKeyOrder(key);
    } else {
      if (this.cache.size >= this.capacity) {
        this.evict();
      }
      this.cache.set(key, value);
      this.keyOrder.push(key);
    }
    this.saveToLocalStorage();
  }

  private evict(): void {
    const lruKey = this.keyOrder.shift()!; // Non-null assertion since capacity is checked
    this.cache.delete(lruKey);
  }

  private updateKeyOrder(key: string): void {
    const index = this.keyOrder.indexOf(key);
    if (index > -1) {
      this.keyOrder.splice(index, 1);
    }
    this.keyOrder.push(key);
  }

  private loadFromLocalStorage(): void {
    const storedCache = localStorage.getItem(this.storageKey);
    if (storedCache) {
      const { cache, keyOrder } = JSON.parse(storedCache);
      this.cache = new Map(cache);
      this.keyOrder = keyOrder;
    }
  }

  private saveToLocalStorage(): void {
    const cacheData = {
      cache: Array.from(this.cache.entries()),
      keyOrder: this.keyOrder
    };
    localStorage.setItem(this.storageKey, JSON.stringify(cacheData));
  }
}
