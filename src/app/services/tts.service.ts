import { inject, Injectable, InjectionToken, OnInit } from '@angular/core';
import { TextToSpeech } from '@capacitor-community/text-to-speech';
import { SettingsService } from './settings.service';

export const TTS_SERVICE = new InjectionToken<Promise<TtsService>>(
  'TtsService'
);

@Injectable({
  providedIn: 'root'
})
export class TtsService {
  voices: SpeechSynthesisVoice[] = [];
  voiceIdx = 0;
  englishVoices: SpeechSynthesisVoice[] = [];
  settingsService = inject(SettingsService);
  constructor() {}
  async initialize() {
    var ttsInit = setInterval(async () => {
      try {
        // console.log('Trying to speak!!!');
        await TextToSpeech.speak({ text: '' });
        clearInterval(ttsInit);
      } catch (e) {
        // console.log(e);
        return;
      }
      this.voices = (await TextToSpeech.getSupportedVoices()).voices;
      this.setVoiceUri('');
      this.englishVoices = this.voices.filter(voice => voice.lang === 'en-US');
    }, 3000);
  }

  getVoiceUri() {
    return this.voices[this.voiceIdx]?.voiceURI;
  }
  getEnglishVoices() {
    return this.englishVoices;
  }

  setVoiceUri(voiceUri: string) {
    this.voiceIdx =
      this.voices.findIndex(voice =>
        voiceUri == ''
          ? voice.name.toLowerCase().includes('google') &&
            voice.lang === 'en-US'
          : voice.voiceURI == voiceUri
      ) || 0;
  }

  async speak(text: string) {
    if (!this.settingsService.getSettings().soundEnabled) {
      return;
    }
    return await TextToSpeech.speak({
      text: text,
      lang: 'en-US',
      voice: this.voiceIdx
    });
  }
}
