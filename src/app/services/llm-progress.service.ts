import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface LlmProgressUpdate {
  progress: number;
  message: string;
  category?: string;
  questionIndex?: number;
}

@Injectable({
  providedIn: 'root'
})
export class LlmProgressService {
  private progressSubject = new BehaviorSubject<LlmProgressUpdate>({
    progress: 0,
    message: 'Initializing...'
  });

  progress$: Observable<LlmProgressUpdate> = this.progressSubject.asObservable();

  constructor() {}

  /**
   * Update the progress of LLM generation
   * @param update The progress update object
   */
  updateProgress(update: LlmProgressUpdate): void {
    this.progressSubject.next(update);
  }

  /**
   * Reset the progress to initial state
   */
  resetProgress(): void {
    this.progressSubject.next({
      progress: 0,
      message: 'Initializing...'
    });
  }

  /**
   * Calculate progress based on streaming response
   * This is a heuristic calculation that can be adjusted based on actual LLM behavior
   * @param totalCategories Total number of categories to generate
   * @param totalQuestionsPerCategory Total questions per category
   * @param currentCategory Current category being processed (0-based index)
   * @param currentQuestion Current question being processed (0-based index)
   * @param responseProgress Progress within the current question (0-100)
   */
  calculateProgress(
    totalCategories: number,
    totalQuestionsPerCategory: number,
    currentCategory: number,
    currentQuestion: number,
    responseProgress: number = 100
  ): number {
    // Calculate total number of questions
    const totalQuestions = totalCategories * totalQuestionsPerCategory;
    
    // Calculate how many questions have been completed
    const completedQuestions = (currentCategory * totalQuestionsPerCategory) + currentQuestion;
    
    // Calculate the base progress (completed questions / total questions)
    const baseProgress = (completedQuestions / totalQuestions) * 100;
    
    // Calculate the progress within the current question
    const questionProgress = (responseProgress / 100) * (1 / totalQuestions) * 100;
    
    // Combine base progress with question progress
    const totalProgress = baseProgress + questionProgress;
    
    // Ensure progress is between 0 and 100
    return Math.min(Math.max(totalProgress, 0), 100);
  }

  /**
   * Generate a message based on the current progress state
   * @param category The current category being processed
   * @param questionIndex The current question index being processed
   */
  generateProgressMessage(category?: string, questionIndex?: number): string {
    if (!category) {
      return 'Preparing to generate questions...';
    }

    const messages = [
      `Thinking about "${category}" questions...`,
      `Crafting clever clues for "${category}"...`,
      `Researching facts about "${category}"...`,
      `Generating question ${questionIndex ? questionIndex + 1 : ''} for "${category}"...`,
      `Creating challenging content for "${category}"...`
    ];

    // Return a random message from the list
    return messages[Math.floor(Math.random() * messages.length)];
  }
}
