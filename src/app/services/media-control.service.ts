import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface MediaElement {
  element: HTMLVideoElement | HTMLAudioElement | HTMLIFrameElement;
  id: string;
  type: 'video' | 'audio' | 'youtube';
  src: string;
}

@Injectable({
  providedIn: 'root'
})
export class MediaControlService {
  private mediaElements = new Map<string, MediaElement>();
  private currentlyPlaying = new BehaviorSubject<string | null>(null);

  // Observable for components to subscribe to
  currentlyPlaying$ = this.currentlyPlaying.asObservable();

  constructor() {}

  registerMediaElement(
    element: HTMLVideoElement | HTMLAudioElement | HTMLIFrameElement,
    src: string
  ): string {
    const id = this.generateMediaId(src);
    let type: 'video' | 'audio' | 'youtube';

    if (
      element.tagName.toLowerCase() === 'iframe' &&
      src.includes('youtube.com')
    ) {
      type = 'youtube';
    } else {
      type = element.tagName.toLowerCase() as 'video' | 'audio';
    }

    this.mediaElements.set(id, {
      element,
      id,
      type,
      src
    });

    // Add event listeners for video/audio elements (not YouTube iframes)
    if (type !== 'youtube') {
      const mediaEl = element as HTMLVideoElement | HTMLAudioElement;
      mediaEl.addEventListener('play', () => {
        this.currentlyPlaying.next(id);
      });

      mediaEl.addEventListener('pause', () => {
        if (this.currentlyPlaying.value === id) {
          this.currentlyPlaying.next(null);
        }
      });

      mediaEl.addEventListener('ended', () => {
        if (this.currentlyPlaying.value === id) {
          this.currentlyPlaying.next(null);
        }
      });
    }

    return id;
  }

  unregisterMediaElement(id: string): void {
    this.mediaElements.delete(id);
    if (this.currentlyPlaying.value === id) {
      this.currentlyPlaying.next(null);
    }
  }

  playMedia(id: string): void {
    const media = this.mediaElements.get(id);
    if (media) {
      // Pause all other media first
      this.pauseAllMedia();

      if (media.type === 'youtube') {
        // For YouTube iframes, we can't directly control playback
        // The user will need to click play on the video
        this.currentlyPlaying.next(id);
      } else {
        const mediaEl = media.element as HTMLVideoElement | HTMLAudioElement;
        mediaEl.play();
      }
    }
  }

  pauseMedia(id: string): void {
    const media = this.mediaElements.get(id);
    if (media) {
      if (media.type === 'youtube') {
        // For YouTube iframes, we can't directly control playback
        // The user will need to click pause on the video
        if (this.currentlyPlaying.value === id) {
          this.currentlyPlaying.next(null);
        }
      } else {
        const mediaEl = media.element as HTMLVideoElement | HTMLAudioElement;
        mediaEl.pause();
      }
    }
  }

  pauseAllMedia(): void {
    this.mediaElements.forEach(media => {
      if (media.type !== 'youtube') {
        const mediaEl = media.element as HTMLVideoElement | HTMLAudioElement;
        if (!mediaEl.paused) {
          mediaEl.pause();
        }
      }
    });
    this.currentlyPlaying.next(null);
  }

  isPlaying(id: string): boolean {
    return this.currentlyPlaying.value === id;
  }

  getMediaElement(id: string): MediaElement | undefined {
    return this.mediaElements.get(id);
  }

  getAllMediaElements(): MediaElement[] {
    return Array.from(this.mediaElements.values());
  }

  private generateMediaId(src: string): string {
    // Create a simple hash of the src URL
    let hash = 0;
    for (let i = 0; i < src.length; i++) {
      const char = src.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return `media_${Math.abs(hash)}`;
  }
}
