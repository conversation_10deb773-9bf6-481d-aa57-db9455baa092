import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface MediaElement {
  element: HTMLVideoElement | HTMLAudioElement;
  id: string;
  type: 'video' | 'audio';
  src: string;
}

@Injectable({
  providedIn: 'root'
})
export class MediaControlService {
  private mediaElements = new Map<string, MediaElement>();
  private currentlyPlaying = new BehaviorSubject<string | null>(null);

  // Observable for components to subscribe to
  currentlyPlaying$ = this.currentlyPlaying.asObservable();

  constructor() {}

  registerMediaElement(element: HTMLVideoElement | HTMLAudioElement, src: string): string {
    const id = this.generateMediaId(src);
    const type = element.tagName.toLowerCase() as 'video' | 'audio';
    
    this.mediaElements.set(id, {
      element,
      id,
      type,
      src
    });

    // Add event listeners
    element.addEventListener('play', () => {
      this.currentlyPlaying.next(id);
    });

    element.addEventListener('pause', () => {
      if (this.currentlyPlaying.value === id) {
        this.currentlyPlaying.next(null);
      }
    });

    element.addEventListener('ended', () => {
      if (this.currentlyPlaying.value === id) {
        this.currentlyPlaying.next(null);
      }
    });

    return id;
  }

  unregisterMediaElement(id: string): void {
    this.mediaElements.delete(id);
    if (this.currentlyPlaying.value === id) {
      this.currentlyPlaying.next(null);
    }
  }

  playMedia(id: string): void {
    const media = this.mediaElements.get(id);
    if (media) {
      // Pause all other media first
      this.pauseAllMedia();
      media.element.play();
    }
  }

  pauseMedia(id: string): void {
    const media = this.mediaElements.get(id);
    if (media) {
      media.element.pause();
    }
  }

  pauseAllMedia(): void {
    this.mediaElements.forEach(media => {
      if (!media.element.paused) {
        media.element.pause();
      }
    });
    this.currentlyPlaying.next(null);
  }

  isPlaying(id: string): boolean {
    return this.currentlyPlaying.value === id;
  }

  getMediaElement(id: string): MediaElement | undefined {
    return this.mediaElements.get(id);
  }

  getAllMediaElements(): MediaElement[] {
    return Array.from(this.mediaElements.values());
  }

  private generateMediaId(src: string): string {
    // Create a simple hash of the src URL
    let hash = 0;
    for (let i = 0; i < src.length; i++) {
      const char = src.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return `media_${Math.abs(hash)}`;
  }
}
