import { inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface AppSettings {
  theme: 'light' | 'dark';
  soundEnabled: boolean;
  voiceUri: string;
  // ... other settings
}

@Injectable({
  providedIn: 'root'
})
export class SettingsService {
  private settings: AppSettings = {
    theme: 'dark', // Default values
    soundEnabled: true,
    voiceUri: ''
    // ... other default settings
  };

  // Use BehaviorSubject to make settings observable and allow subscribing to changes
  private settingsSubject = new BehaviorSubject<AppSettings>(this.settings);
  settings$: Observable<AppSettings> = this.settingsSubject.asObservable();
  constructor() {
    // Load settings from storage (e.g., local storage) on service initialization
    this.loadSettings();
  }

  // Get the current settings
  getSettings(): AppSettings {
    return this.settings;
  }

  // Update a specific setting
  updateSetting<K extends keyof AppSettings>(
    key: K,
    value: AppSettings[K]
  ): void {
    this.settings[key] = value;
    this.saveSettings(); // Save changes to storage
    this.settingsSubject.next(this.settings); // Notify subscribers of the change
  }

  // Load settings from local storage
  private loadSettings(): void {
    const storedSettings = localStorage.getItem('appSettings');
    if (storedSettings) {
      this.settings = JSON.parse(storedSettings);
      this.settingsSubject.next(this.settings); // Emit loaded settings
    }
  }

  // Save settings to local storage
  private saveSettings(): void {
    localStorage.setItem('appSettings', JSON.stringify(this.settings));
  }
}
