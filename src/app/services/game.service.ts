import { inject, Injectable } from '@angular/core';
import { Room } from '../../../functions/src/resources';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FirebaseFunctions } from '@capacitor-firebase/functions';
import { LlmProgressService } from './llm-progress.service';
import { GenerationProgressService } from './generation-progress.service';

@Injectable({
  providedIn: 'root'
})
export class GameService {
  private matSnackBar = inject(MatSnackBar);
  private llmProgressService = inject(LlmProgressService);
  private generationProgressService = inject(GenerationProgressService);

  // --- Room Operations ---
  async call(functionName: string, data: any = {}): Promise<Room> {
    // If this is a startRoom call, we need to handle the generation phase
    if (functionName === 'startRoom') {
      // First, update the room state to GeneratingQuestions
      await this.generationProgressService.startGenerationPhase(data.roomId);

      // Start the progress simulation
      this.simulateProgressUpdates(data);
    }

    try {
      const result = await FirebaseFunctions.callByName({
        name: functionName,
        data
      });

      // Reset progress when done
      if (functionName === 'startRoom') {
        this.llmProgressService.resetProgress();
      }

      return result.data as Room;
    } catch (error) {
      console.error(`Error calling function ${functionName}:`, error);

      // Reset progress on error
      if (functionName === 'startRoom') {
        this.llmProgressService.resetProgress();
      }

      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.matSnackBar.open('Error: ' + errorMessage, 'Dismiss', {
        duration: 3000
      });

      throw new Error(`Failed to call ${functionName}`);
    }
  }

  /**
   * Simulate progress updates for LLM generation
   * In a real implementation, this would be replaced with actual streaming updates from the LLM
   */
  private simulateProgressUpdates(data: any): void {
    const { roomId, categoryTitles, numQuestions } = data;
    const totalCategories = categoryTitles.length;
    const totalQuestionsPerCategory = numQuestions;

    // Reset progress
    this.llmProgressService.resetProgress();

    // Initial progress update
    const initialUpdate = {
      progress: 0,
      message: 'Starting question generation...'
    };

    this.llmProgressService.updateProgress(initialUpdate);
    this.generationProgressService.updateGenerationProgress(
      roomId,
      initialUpdate
    );

    // Simulate progress for each category and question
    let currentCategory = 0;
    let currentQuestion = 0;

    const updateInterval = setInterval(async () => {
      // Generate a progress message
      const message = this.llmProgressService.generateProgressMessage(
        categoryTitles[currentCategory],
        currentQuestion
      );

      // Calculate progress percentage
      const progress = this.llmProgressService.calculateProgress(
        totalCategories,
        totalQuestionsPerCategory,
        currentCategory,
        currentQuestion,
        Math.random() * 100 // Simulate progress within the current question
      );

      // Create the progress update
      const progressUpdate = {
        progress: Math.round(progress),
        message,
        category: categoryTitles[currentCategory],
        questionIndex: currentQuestion
      };

      // Update progress locally and in Firebase
      this.llmProgressService.updateProgress(progressUpdate);
      await this.generationProgressService.updateGenerationProgress(
        roomId,
        progressUpdate
      );

      // Move to the next question or category
      currentQuestion++;
      if (currentQuestion >= totalQuestionsPerCategory) {
        currentQuestion = 0;
        currentCategory++;
      }

      // Stop when all categories are done
      if (currentCategory >= totalCategories) {
        clearInterval(updateInterval);

        // Final update
        const finalUpdate = {
          progress: 100,
          message: 'Finalizing game setup...'
        };

        this.llmProgressService.updateProgress(finalUpdate);
        await this.generationProgressService.updateGenerationProgress(
          roomId,
          finalUpdate
        );
      }
    }, 1500); // Update every 1.5 seconds
  }
}
