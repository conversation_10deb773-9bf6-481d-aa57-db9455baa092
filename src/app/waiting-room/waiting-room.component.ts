import {
  Component,
  inject,
  Input,
  <PERSON><PERSON><PERSON><PERSON>,
  OnDestroy,
  OnInit,
  ViewChild,
  AfterViewInit,
  ChangeDetectorRef
} from '@angular/core';
import { GameService } from '../services/game.service';
import { LlmProgressService } from '../services/llm-progress.service';
import { ActivatedRoute, Router } from '@angular/router';
import {
  Room,
  GameMode,
  LlmAgent,
  Round
} from '../../../functions/src/resources';
import { ThemePalette } from '@angular/material/core';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatSliderModule } from '@angular/material/slider';
import { MatTooltipModule } from '@angular/material/tooltip';

import { CommonModule } from '@angular/common';
import { FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { MatInputModule } from '@angular/material/input';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { DynamicListInputComponent } from '../dynamic-list-input/dynamic-list-input.component';
import { AuthService } from '../services/auth.service';
import { CategorySelectorComponent } from '../category-selector/category-selector.component';
import { SubCategory } from '../../../functions/src/services';

const llmAgents: Array<LlmAgent> = [
  {
    provider: 'Gemini',
    modelName: 'gemini-2.5-flash'
  },
  {
    provider: 'Gemini',
    modelName: 'gemini-2.5-pro'
  },
  {
    provider: 'Gemini',
    modelName: 'gemini-2.5-flash-lite-preview-06-17'
  },
  {
    provider: 'Gemini',
    modelName: 'gemini-2.0-flash-thinking-exp-01-21'
  },
  {
    provider: 'Gemini',
    modelName: 'gemini-2.5-flash-preview-04-17'
  },
  {
    provider: 'Gemini',
    modelName: 'gemini-2.5-pro-exp-03-25'
  },
  {
    provider: 'Gemini',
    modelName: 'gemini-2.0-flash-exp'
  },
  {
    provider: 'DeepSeekAI',
    modelName: 'DeepSeek-R1'
  },
  {
    provider: 'Gemini',
    modelName: 'gemini-1.5-flash-8b'
  },
  {
    provider: 'OpenAI',
    modelName: 'gpt-4o'
  },
  {
    provider: 'Gemini',
    modelName: 'gemini-1.5-pro-002'
  },
  {
    provider: 'Gemini',
    modelName: 'gemini-exp-1206'
  },
  {
    provider: 'Gemini',
    modelName: 'gemini-2.0-flash-thinking-exp-1219'
  },
  {
    provider: 'OpenAI',
    modelName: 'gpt-4o-mini'
  },
  {
    provider: 'Fake',
    modelName: 'fake'
  }
];

@Component({
  selector: 'app-waiting-room',
  imports: [
    CommonModule,
    MatInputModule,
    FormsModule,
    MatCardModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    MatSelectModule,
    MatOptionModule,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    DynamicListInputComponent,
    CategorySelectorComponent,
    MatSliderModule,
    MatTooltipModule
  ],
  templateUrl: './waiting-room.component.html',
  styleUrl: './waiting-room.component.css',
  standalone: true
})
export class WaitingRoomComponent
  implements OnChanges, OnInit, OnDestroy, AfterViewInit {
  @Input() room?: Room;
  @Input() roundData?: Round;
  @Input() isCast: boolean = false;
  gameService: GameService = inject(GameService);
  authService: AuthService = inject(AuthService);
  router: Router = inject(Router);
  route: ActivatedRoute = inject(ActivatedRoute);
  snackbar: MatSnackBar = inject(MatSnackBar);
  fb = inject(FormBuilder);
  llmProgressService = inject(LlmProgressService);
  cdr = inject(ChangeDetectorRef);

  private progressSubscription?: Subscription;

  isAdmin: boolean = false;
  categoryTitles: string[] = [];
  llmAgent = llmAgents[0];
  llmAgents = llmAgents;
  isGamemasterMode: boolean = false;
  loading: boolean = false;
  generationProgress: number = 0;
  currentGenerationMessage: string = 'Initializing...';
  generatingCategory?: string;
  isGeneratingQuestions: boolean = false;
  categories: Array<string> = [];
  teams: Array<string> = [];
  numQuestions = 5;
  numCategories = 5;
  gameMode: string = 'BUZZER';

  // Timer settings with default values
  readonly DEFAULT_BUZZ_IN_TIMER = 60; // in seconds
  readonly DEFAULT_ANSWER_TIMER = 60; // in seconds
  readonly DEFAULT_SHOW_ANSWER_TIMER = 10; // in seconds
  readonly DEFAULT_INITIAL_THINKING_TIMER = 20; // in seconds

  buzzInTimerDuration = this.DEFAULT_BUZZ_IN_TIMER;
  answerDuration = this.DEFAULT_ANSWER_TIMER;
  showAnswerDuration = this.DEFAULT_SHOW_ANSWER_TIMER;
  initialThinkingDuration = this.DEFAULT_INITIAL_THINKING_TIMER;

  // Track if timer settings have been modified from defaults
  timerSettingsModified = {
    buzzIn: false,
    answer: false,
    showAnswer: false,
    initialThinking: false
  };

  @ViewChild('dynamicList')
  dynamicList!: DynamicListInputComponent;

  @ViewChild('teamsList')
  teamsList!: DynamicListInputComponent;

  ngOnInit() {
    // Log initialization state
    console.log('WaitingRoomComponent initialized:', {
      isCast: this.isCast,
      isAdmin: this.isAdmin,
      roomId: this.room?.roomId
    });

    // Subscribe to progress updates
    this.progressSubscription = this.llmProgressService.progress$.subscribe(
      update => {
        this.generationProgress = update.progress;
        this.currentGenerationMessage = update.message;
        this.generatingCategory = update.category;
      }
    );
    this.ngOnChanges();
  }

  // Flag to track if view has been initialized
  private viewInitialized = false;

  ngAfterViewInit() {
    console.log('WaitingRoomComponent view initialized');
    this.viewInitialized = true;

    // Use setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      // If we have categories or teams data, update the dynamic lists
      if (this.categories.length > 0 && this.dynamicList) {
        console.log('Setting categories in dynamicList after view init');
        this.dynamicList.setElements(this.categories, false);
      }

      if (this.teams.length > 0 && this.teamsList) {
        console.log('Setting teams in teamsList after view init');
        this.teamsList.setElements(this.teams, false);
      }

      // Manually trigger change detection
      this.cdr.detectChanges();
    });
  }

  ngOnDestroy() {
    // Clean up subscription
    if (this.progressSubscription) {
      this.progressSubscription.unsubscribe();
    }

    // Reset progress state
    this.llmProgressService.resetProgress();
    this.loading = false;
    this.isGeneratingQuestions = false;
  }

  ngOnChanges() {
    const user = this.authService.getUser();
    this.isAdmin = user ? this.room?.host === user.uid : false;
    this.isGamemasterMode = this.room?.mode === GameMode.GAMEMASTER;

    console.log(
      'WaitingRoom ngOnChanges - isCast:',
      this.isCast,
      'isAdmin:',
      this.isAdmin
    );
    console.log('Room categories:', this.room?.categoryTitles);

    // Check if the room is in the GeneratingQuestions state
    this.isGeneratingQuestions =
      this.room?.gameState.gameProgress.type === 'GeneratingQuestions';

    // If the room is in the GeneratingQuestions state, update the progress information
    if (this.isGeneratingQuestions) {
      const generatingState = this.room?.gameState.gameProgress as any;
      this.generationProgress = generatingState.progress || 0;
      this.currentGenerationMessage =
        generatingState.message || 'Generating questions...';
      this.generatingCategory = generatingState.category;
    }

    // Load saved timer settings from local storage for admin
    // or from room settings for non-admin or cast view
    if (this.isAdmin && !this.isCast) {
      this.loadTimerSettings();

      // For admin, also load categories and teams from the room to preserve them on refresh
      this.loadCategoriesFromRoom();
      this.loadTeamsFromRoom();
      this.loadNumQuestionsFromRoom();
    } else {
      // For non-admin users and cast view, load all settings from the room
      this.loadTimerSettingsFromRoom();
      this.loadTeamsFromRoom();
      this.loadNumQuestionsFromRoom();
      this.loadCategoriesFromRoom();
    }
  }

  // Load timer settings from room for non-admin users
  private loadTimerSettingsFromRoom(): void {
    if (this.room?.roomSettings) {
      // Convert from milliseconds to seconds for display
      this.buzzInTimerDuration =
        this.room.roomSettings.buzzInTimerDurationMillis / 1000;
      this.answerDuration = this.room.roomSettings.answerDurationMillis / 1000;
      this.showAnswerDuration =
        this.room.roomSettings.showAnswerDurationMillis / 1000;
      this.initialThinkingDuration =
        this.room.roomSettings.initialThinkingDurationMillis / 1000;

      // Set game mode from roomSettings (this is the most up-to-date)
      if (this.room.roomSettings.gameMode === GameMode.BUZZER) {
        this.gameMode = 'BUZZER';
      } else if (this.room.roomSettings.gameMode === GameMode.TURN_BASED) {
        this.gameMode = 'TURN_BASED';
      }

      // Also update the room mode to match (for consistency)
      this.isGamemasterMode = this.room.mode === GameMode.GAMEMASTER;

      // Set LLM agent
      if (this.room.roomSettings.llmAgent) {
        this.llmAgent = this.room.roomSettings.llmAgent;
      }

      console.log('Loaded settings from room:', {
        gameMode: this.gameMode,
        isGamemasterMode: this.isGamemasterMode,
        buzzInTimer: this.buzzInTimerDuration,
        answerTimer: this.answerDuration,
        showAnswerTimer: this.showAnswerDuration,
        initialThinkingTimer: this.initialThinkingDuration
      });
    }
  }

  // Load categories from room data
  private loadCategoriesFromRoom(): void {
    console.log(
      'loadCategoriesFromRoom called - isCast:',
      this.isCast,
      'isAdmin:',
      this.isAdmin,
      'viewInitialized:',
      this.viewInitialized
    );

    if (this.room?.categoryTitles) {
      // Always update categories from room data, even if empty
      this.categories = [...this.room.categoryTitles];
      console.log('Loaded categories from room:', this.categories);

      // If this is the admin view, also update the dynamic list input
      // Only update the dynamic list if the view has been initialized and the component exists
      if (
        this.isAdmin &&
        !this.isCast &&
        this.viewInitialized &&
        this.dynamicList
      ) {
        // Use setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError
        setTimeout(() => {
          console.log('Updating dynamicList with categories:', this.categories);
          this.dynamicList.setElements(this.categories, false);
          this.cdr.detectChanges();
        });
      }
    } else {
      this.categories = [];
      console.log('No categories found in room data');
    }
  }

  // Load teams from room data (for gamemaster mode)
  private loadTeamsFromRoom(): void {
    console.log(
      'loadTeamsFromRoom called - isCast:',
      this.isCast,
      'isAdmin:',
      this.isAdmin,
      'viewInitialized:',
      this.viewInitialized
    );

    if (this.isGamemasterMode && this.room?.playerIds) {
      // In gamemaster mode, playerIds represents the teams
      this.teams = [...this.room.playerIds];
      console.log('Loaded teams from room:', this.teams);

      // If this is the admin view, also update the dynamic list input
      // Only update the dynamic list if the view has been initialized and the component exists
      if (
        this.isAdmin &&
        !this.isCast &&
        this.viewInitialized &&
        this.teamsList
      ) {
        // Use setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError
        setTimeout(() => {
          console.log('Updating teamsList with teams:', this.teams);
          this.teamsList.setElements(this.teams, false);
          this.cdr.detectChanges();
        });
      }
    }
  }

  // Load number of questions from room settings
  private loadNumQuestionsFromRoom(): void {
    if (this.room?.roomSettings?.numQuestions) {
      this.numQuestions = this.room.roomSettings.numQuestions;
    }
  }

  /**
   * Checks if the start button should be disabled
   * @returns true if the button should be disabled, false otherwise
   */
  isStartButtonDisabled(): boolean {
    // Check if at least one category is entered
    const hasCategories = this.categories.length > 0;

    // For gamemaster mode, check if at least one team is entered
    const hasTeams =
      !this.isGamemasterMode ||
      (this.isGamemasterMode && this.teams.length > 0);

    return !hasCategories || !hasTeams;
  }

  /**
   * Gets the tooltip text for the start button
   * @returns tooltip text explaining why the button is disabled
   */
  getStartButtonTooltip(): string {
    if (this.categories.length === 0) {
      return 'Please enter at least one category';
    }

    if (this.isGamemasterMode && this.teams.length === 0) {
      return 'Please enter at least one team';
    }

    return 'Start the game';
  }

  async onStartRoom() {
    if (!this.isAdmin) return;
    this.loading = true;
    try {
      await this.gameService.call('startRoom', {
        roomId: this.room!.roomId,
        numQuestions: this.numQuestions,
        numCategories: this.numCategories,
        llmAgent: this.llmAgent,
        teamNames: this.teams,
        categoryTitles: this.categories,
        mode: GameMode[this.gameMode as keyof typeof GameMode],
        timerSettings: {
          buzzInTimerDurationMillis: this.buzzInTimerDuration * 1000,
          answerDurationMillis: this.answerDuration * 1000,
          showAnswerDurationMillis: this.showAnswerDuration * 1000,
          initialThinkingDurationMillis: this.initialThinkingDuration * 1000
        }
      });
      this.loading = false;
    } catch (e) {
      this.loading = false;
      console.error('Error starting the room:', e);
    }
  }

  goToQuestionManager() {
    if (!this.isAdmin) return;
    this.router.navigate(['/questions', this.room!.roomId]);
  }

  onUpdateCategories(newList: Array<string>) {
    // Check if the categories have actually changed
    const currentCategories = this.room?.categoryTitles || [];
    const categoriesChanged =
      JSON.stringify(currentCategories) !== JSON.stringify(newList);

    console.log(
      'Categories changed:',
      categoriesChanged,
      'Current:',
      currentCategories,
      'New:',
      newList
    );

    this.categories = newList;

    // Only update room settings if categories have changed and user is admin
    if (categoriesChanged && this.isAdmin && this.room) {
      // Determine which game mode to send
      // If we're in gamemaster mode, preserve it; otherwise use the dropdown selection
      const modeToSend = this.isGamemasterMode
        ? GameMode.GAMEMASTER
        : GameMode[this.gameMode as keyof typeof GameMode];

      console.log('Updating room settings with new categories:', newList);
      this.gameService
        .call('updateRoomSettings', {
          roomId: this.room.roomId,
          categoryTitles: this.categories,
          // Include the current game mode to prevent it from changing
          gameMode: modeToSend
        })
        .catch(error => {
          console.error('Error updating categories:', error);
        });
    }
  }

  // Handle number of questions change
  onNumQuestionsChange(): void {
    // Update room settings for non-admin users to see
    if (this.isAdmin && this.room) {
      // Determine which game mode to send
      // If we're in gamemaster mode, preserve it; otherwise use the dropdown selection
      const modeToSend = this.isGamemasterMode
        ? GameMode.GAMEMASTER
        : GameMode[this.gameMode as keyof typeof GameMode];

      this.gameService
        .call('updateRoomSettings', {
          roomId: this.room.roomId,
          numQuestions: this.numQuestions,
          // Include the current game mode to prevent it from changing
          gameMode: modeToSend
        })
        .catch(error => {
          console.error('Error updating number of questions:', error);
        });
    }
  }

  // Handle LLM agent change
  onLlmAgentChange(): void {
    // Update room settings for non-admin users to see
    if (this.isAdmin && this.room) {
      // Determine which game mode to send
      // If we're in gamemaster mode, preserve it; otherwise use the dropdown selection
      const modeToSend = this.isGamemasterMode
        ? GameMode.GAMEMASTER
        : GameMode[this.gameMode as keyof typeof GameMode];

      this.gameService
        .call('updateRoomSettings', {
          roomId: this.room.roomId,
          llmAgent: this.llmAgent,
          // Include the current game mode to prevent it from changing
          gameMode: modeToSend
        })
        .catch(error => {
          console.error('Error updating LLM agent:', error);
        });
    }
  }

  onUpdateTeams(newList: Array<string>) {
    // Check if the teams have actually changed
    const currentTeams = this.room?.playerIds || [];
    const teamsChanged =
      JSON.stringify(currentTeams) !== JSON.stringify(newList);

    console.log(
      'Teams changed:',
      teamsChanged,
      'Current:',
      currentTeams,
      'New:',
      newList
    );

    this.teams = newList;

    // Only update room settings if teams have changed and user is admin
    if (teamsChanged && this.isAdmin && this.room && this.isGamemasterMode) {
      console.log('Updating room settings with new teams:', newList);
      // Always use GAMEMASTER mode when updating teams
      this.gameService
        .call('updateRoomSettings', {
          roomId: this.room.roomId,
          teamNames: this.teams,
          // Always use GAMEMASTER mode for team updates
          gameMode: GameMode.GAMEMASTER
        })
        .catch(error => {
          console.error('Error updating teams:', error);
        });
    }
  }

  copyLinkToClipboard() {
    // Determine which link to share based on game mode
    let linkToShare = '';

    if (this.isGamemasterMode) {
      // In gamemaster mode, share the castRoom link
      linkToShare = window.location.origin + '/castRoom/' + this.room!.roomId;
    } else {
      // In regular mode, share the lobby link
      linkToShare = window.location.origin + '/lobby/' + this.room!.roomId;
    }

    navigator.clipboard
      .writeText(linkToShare)
      .then(() => {
        this.snackbar.open('Link copied to clipboard!', '', {
          duration: 2000
        });
      })
      .catch(err => {
        console.error('Failed to copy text: ', err);
        this.snackbar.open('Failed to copy text', 'Close', {
          duration: 3000
        });
      });
  }
  onEmitSubCategory(category: SubCategory) {
    this.dynamicList.addElement(category.subCategory);
  }

  formatSliderLabel(value: number): string {
    return `${value}s`;
  }

  // Reset timer settings to default values
  resetTimerSettings(): void {
    this.buzzInTimerDuration = this.DEFAULT_BUZZ_IN_TIMER;
    this.answerDuration = this.DEFAULT_ANSWER_TIMER;
    this.showAnswerDuration = this.DEFAULT_SHOW_ANSWER_TIMER;
    this.initialThinkingDuration = this.DEFAULT_INITIAL_THINKING_TIMER;

    // Reset modification flags
    this.timerSettingsModified = {
      buzzIn: false,
      answer: false,
      showAnswer: false,
      initialThinking: false
    };

    // Save the reset settings to local storage
    this.saveTimerSettings();

    // Show confirmation
    this.snackbar.open('Timer settings reset to defaults', '', {
      duration: 2000
    });
  }

  // Handle timer value changes
  onTimerChange(timerType: string): void {
    // Mark the timer as modified
    switch (timerType) {
      case 'buzzIn':
        this.timerSettingsModified.buzzIn =
          this.buzzInTimerDuration !== this.DEFAULT_BUZZ_IN_TIMER;
        break;
      case 'answer':
        this.timerSettingsModified.answer =
          this.answerDuration !== this.DEFAULT_ANSWER_TIMER;
        break;
      case 'showAnswer':
        this.timerSettingsModified.showAnswer =
          this.showAnswerDuration !== this.DEFAULT_SHOW_ANSWER_TIMER;
        break;
      case 'initialThinking':
        this.timerSettingsModified.initialThinking =
          this.initialThinkingDuration !== this.DEFAULT_INITIAL_THINKING_TIMER;
        break;
    }

    // Save the updated settings to local storage
    this.saveTimerSettings();

    // Update room settings for non-admin users to see
    if (this.isAdmin && this.room) {
      // Determine which game mode to send
      // If we're in gamemaster mode, preserve it; otherwise use the dropdown selection
      const modeToSend = this.isGamemasterMode
        ? GameMode.GAMEMASTER
        : GameMode[this.gameMode as keyof typeof GameMode];

      this.gameService
        .call('updateRoomSettings', {
          roomId: this.room.roomId,
          timerSettings: {
            buzzInTimerDurationMillis: this.buzzInTimerDuration * 1000,
            answerDurationMillis: this.answerDuration * 1000,
            showAnswerDurationMillis: this.showAnswerDuration * 1000,
            initialThinkingDurationMillis: this.initialThinkingDuration * 1000,
            gameMode: modeToSend,
            llmAgent: this.llmAgent
          }
        })
        .catch(error => {
          console.error('Error updating timer settings:', error);
        });
    }
  }

  // Handle game mode changes
  onGameModeChange(): void {
    // When switching between game modes, we'll keep the values in case user switches back

    // For both modes, make sure initialThinking has a reasonable default if it was 0
    if (this.initialThinkingDuration === 0) {
      // Set a default value for initialThinking
      this.initialThinkingDuration = this.DEFAULT_INITIAL_THINKING_TIMER;
      this.timerSettingsModified.initialThinking = false;
    }

    // Save settings after mode change
    this.saveTimerSettings();

    // Update room settings for non-admin users to see
    if (this.isAdmin && this.room) {
      // Determine which game mode to send
      // If we're in gamemaster mode, preserve it; otherwise use the dropdown selection
      const modeToSend = this.isGamemasterMode
        ? GameMode.GAMEMASTER
        : GameMode[this.gameMode as keyof typeof GameMode];

      // Send the full timer settings to ensure everything is updated
      this.gameService
        .call('updateRoomSettings', {
          roomId: this.room.roomId,
          timerSettings: {
            buzzInTimerDurationMillis: this.buzzInTimerDuration * 1000,
            answerDurationMillis: this.answerDuration * 1000,
            showAnswerDurationMillis: this.showAnswerDuration * 1000,
            initialThinkingDurationMillis: this.initialThinkingDuration * 1000,
            gameMode: modeToSend,
            llmAgent: this.llmAgent
          }
        })
        .catch(error => {
          console.error('Error updating game mode:', error);
        });
    }
  }

  // Get CSS class for timer label based on whether it's been modified
  getTimerLabelClass(timerType: string): string {
    return this.timerSettingsModified[
      timerType as keyof typeof this.timerSettingsModified
    ]
      ? 'timer-label-modified'
      : '';
  }

  // Get color for slider based on value
  getSliderColor(value: number, min: number, max: number): ThemePalette {
    // For most timers, longer is easier (blue), shorter is harder (red)
    const range = max - min;
    const third = range / 3;

    if (value <= min + third) {
      return 'warn'; // Red for short times (harder)
    } else if (value >= max - third) {
      return 'primary'; // Blue for long times (easier)
    } else {
      return 'accent'; // Default color for middle range
    }
  }

  // Save timer settings to local storage
  private saveTimerSettings(): void {
    const settings = {
      buzzInTimerDuration: this.buzzInTimerDuration,
      answerDuration: this.answerDuration,
      showAnswerDuration: this.showAnswerDuration,
      initialThinkingDuration: this.initialThinkingDuration,
      timerSettingsModified: this.timerSettingsModified
    };

    localStorage.setItem('jeopardyTimerSettings', JSON.stringify(settings));
  }

  // Load timer settings from local storage
  private loadTimerSettings(): void {
    const savedSettings = localStorage.getItem('jeopardyTimerSettings');

    if (savedSettings) {
      const settings = JSON.parse(savedSettings);

      this.buzzInTimerDuration = settings.buzzInTimerDuration;
      this.answerDuration = settings.answerDuration;
      this.showAnswerDuration = settings.showAnswerDuration;
      this.initialThinkingDuration = settings.initialThinkingDuration;
      this.timerSettingsModified = settings.timerSettingsModified;
    }
  }
}
