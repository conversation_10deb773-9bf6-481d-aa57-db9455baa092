import { Component, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Subscription } from 'rxjs';
import {
  MediaControlService,
  MediaElement
} from '../../services/media-control.service';

@Component({
  selector: 'app-media-controls',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatTooltipModule
  ],
  template: `
    <mat-card class="media-controls-card" *ngIf="mediaElements.length > 0">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>play_circle</mat-icon>
          Media Controls
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="media-list">
          <div
            class="media-item"
            *ngFor="let media of mediaElements"
            [class.playing]="isPlaying(media.id)"
          >
            <div class="media-info">
              <mat-icon class="media-type-icon">
                {{ getMediaIcon(media.type) }}
              </mat-icon>
              <span class="media-name">{{ getMediaName(media.src) }}</span>
            </div>
            <div class="media-controls">
              <button
                mat-icon-button
                (click)="togglePlayPause(media.id)"
                [matTooltip]="isPlaying(media.id) ? 'Pause' : 'Play'"
                [color]="isPlaying(media.id) ? 'warn' : 'primary'"
              >
                <mat-icon>
                  {{ isPlaying(media.id) ? 'pause' : 'play_arrow' }}
                </mat-icon>
              </button>
            </div>
          </div>
        </div>
        <div class="global-controls" *ngIf="mediaElements.length > 1">
          <button
            mat-stroked-button
            (click)="pauseAll()"
            matTooltip="Pause all media"
          >
            <mat-icon>pause</mat-icon>
            Pause All
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  `,
  styles: [
    `
      .media-controls-card {
        margin: 1rem 0;
        max-width: 400px;
      }

      .media-controls-card mat-card-header {
        padding-bottom: 0.5rem;
      }

      .media-controls-card mat-card-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font: var(--mat-sys-title-medium);
      }

      .media-list {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }

      .media-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.5rem;
        border: 1px solid var(--mat-sys-outline-variant);
        border-radius: 0.5rem;
        transition: all 0.2s ease;
      }

      .media-item.playing {
        background-color: var(--mat-sys-primary-container);
        border-color: var(--mat-sys-primary);
      }

      .media-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex: 1;
        min-width: 0;
      }

      .media-type-icon {
        color: var(--mat-sys-on-surface-variant);
        font-size: 18px;
        width: 18px;
        height: 18px;
      }

      .media-name {
        font: var(--mat-sys-body-medium);
        color: var(--mat-sys-on-surface);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .media-controls {
        display: flex;
        align-items: center;
        gap: 0.25rem;
      }

      .global-controls {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid var(--mat-sys-outline-variant);
        display: flex;
        justify-content: center;
      }

      .global-controls button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
    `
  ]
})
export class MediaControlsComponent implements OnInit, OnDestroy {
  mediaElements: MediaElement[] = [];
  private subscription = new Subscription();

  constructor(private mediaControlService: MediaControlService) {}

  ngOnInit(): void {
    // Subscribe to media element changes
    this.subscription.add(
      this.mediaControlService.currentlyPlaying$.subscribe(() => {
        this.updateMediaElements();
      })
    );

    // Initial load
    this.updateMediaElements();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  private updateMediaElements(): void {
    this.mediaElements = this.mediaControlService.getAllMediaElements();
  }

  togglePlayPause(mediaId: string): void {
    if (this.isPlaying(mediaId)) {
      this.mediaControlService.pauseMedia(mediaId);
    } else {
      this.mediaControlService.playMedia(mediaId);
    }
  }

  pauseAll(): void {
    this.mediaControlService.pauseAllMedia();
  }

  isPlaying(mediaId: string): boolean {
    return this.mediaControlService.isPlaying(mediaId);
  }

  getMediaIcon(type: string): string {
    switch (type) {
      case 'video':
        return 'videocam';
      case 'audio':
        return 'audiotrack';
      case 'youtube':
        return 'smart_display';
      default:
        return 'play_circle';
    }
  }

  getMediaName(src: string): string {
    try {
      if (src.includes('youtube.com')) {
        return 'YouTube Video';
      }
      const url = new URL(src);
      const pathname = url.pathname;
      const filename = pathname.split('/').pop() || 'Unknown Media';
      return filename.length > 30
        ? filename.substring(0, 27) + '...'
        : filename;
    } catch {
      return src.length > 30 ? src.substring(0, 27) + '...' : src;
    }
  }
}
