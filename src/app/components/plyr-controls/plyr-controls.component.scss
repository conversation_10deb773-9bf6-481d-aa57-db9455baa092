.plyr-controls-card {
  background: var(--mat-sys-surface-container);
  border: 1px solid var(--mat-sys-outline-variant);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
}

.plyr-controls-card.embedded {
  background: var(--mat-sys-surface-container-low);
  border: 1px solid var(--mat-sys-outline-variant);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  margin-top: 0.5rem;
}

mat-card-header {
  background: var(--mat-sys-surface-container-high);
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

mat-card-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font: var(--mat-sys-title-medium);
  color: var(--mat-sys-on-surface);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.pause-all-button {
  background: var(--mat-sys-error-container);
  color: var(--mat-sys-on-error-container);
}

mat-card-content {
  padding: 0;
}

.players-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.player-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--mat-sys-outline-variant);
  transition: background-color 0.2s ease;
  min-height: 60px;
  background: var(--mat-sys-tertiary-container);
  color: var(--mat-sys-on-tertiary-container);
}

.player-item:last-child {
  border-bottom: none;
}

.player-item.playing {
  background: var(--mat-sys-primary-container);
  color: var(--mat-sys-on-primary-container);
}

.player-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

.player-type-icon {
  color: var(--mat-sys-on-tertiary-container);
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.player-item.playing .player-type-icon {
  color: var(--mat-sys-on-primary-container);
}

.player-title {
  font: var(--mat-sys-body-medium);
  color: var(--mat-sys-on-tertiary-container);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.player-item.playing .player-title {
  color: var(--mat-sys-on-primary-container);
  font-weight: 600;
}

.player-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.play-pause-button {
  background: var(--mat-sys-primary);
  color: var(--mat-sys-on-primary);
}

.player-item.playing .play-pause-button {
  background: var(--mat-sys-secondary);
  color: var(--mat-sys-on-secondary);
}

.restart-button {
  color: var(--mat-sys-on-surface-variant);
}

.mute-button {
  color: var(--mat-sys-on-surface-variant);
}

.mute-button.muted {
  color: var(--mat-sys-error);
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 100px;
}

.volume-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  color: var(--mat-sys-on-tertiary-container);
}

.player-item.playing .volume-icon {
  color: var(--mat-sys-on-primary-container);
}

.volume-slider {
  flex: 1;
  min-width: 60px;
}

/* Mobile optimizations */
@media (max-width: 600px) {
  .player-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
    padding: 0.5rem;
  }

  .player-controls {
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .volume-control {
    min-width: auto;
    flex: 1;
  }
}
