<mat-card class="plyr-controls-card" [class.embedded]="embedded" *ngIf="players.length > 0">
  <mat-card-header>
    <mat-card-title>
      <mat-icon>cast</mat-icon>
      Cast Room Controls
    </mat-card-title>
    <div class="header-actions">
      <button
        mat-icon-button
        (click)="pauseAll()"
        matTooltip="Pause all media on cast room"
        class="pause-all-button"
      >
        <mat-icon>pause</mat-icon>
      </button>
    </div>
  </mat-card-header>
  <mat-card-content>
    <div class="players-list">
      <div
        class="player-item"
        *ngFor="let player of players"
        [class.playing]="isPlaying(player.id)"
      >
        <div class="player-info">
          <mat-icon class="player-type-icon">
            {{ getPlayerIcon(player.type) }}
          </mat-icon>
          <span class="player-title">{{ player.title }}</span>
        </div>
        <div class="player-controls">
          <button
            mat-icon-button
            (click)="togglePlayPause(player.id)"
            [matTooltip]="isPlaying(player.id) ? 'Pause' : 'Play'"
            class="play-pause-button"
          >
            <mat-icon>
              {{ isPlaying(player.id) ? 'pause' : 'play_arrow' }}
            </mat-icon>
          </button>
          <button
            mat-icon-button
            (click)="seekToStart(player.id)"
            matTooltip="Restart"
            class="restart-button"
          >
            <mat-icon>replay</mat-icon>
          </button>
          
          <button
            mat-icon-button
            (click)="toggleMute(player.id)"
            [matTooltip]="isMuted(player.id) ? 'Unmute' : 'Mute'"
            class="mute-button"
            [class.muted]="isMuted(player.id)"
          >
            <mat-icon>
              {{ isMuted(player.id) ? 'volume_off' : 'volume_up' }}
            </mat-icon>
          </button>
          <div class="volume-control" *ngIf="player.type !== 'youtube'">
            <mat-icon class="volume-icon">cast</mat-icon>
            <input
              type="range"
              class="volume-slider"
              [min]="0"
              [max]="1"
              [step]="0.1"
              [value]="getCastVolume(player.id)"
              (input)="setCastVolume(player.id, $any($event.target).value)"
              matTooltip="Cast Room Volume"
            />
          </div>
        </div>
      </div>
    </div>
  </mat-card-content>
</mat-card>
