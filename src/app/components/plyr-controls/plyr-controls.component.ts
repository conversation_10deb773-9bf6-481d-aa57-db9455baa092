import { Component, OnInit, OnD<PERSON>roy, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSliderModule } from '@angular/material/slider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { PlyrService, PlyrInstance } from '../../services/plyr.service';
import { CastMediaControlService } from '../../services/cast-media-control.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-plyr-controls',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSliderModule,
    MatTooltipModule
  ],
  templateUrl: './plyr-controls.component.html',
  styleUrls: ['./plyr-controls.component.scss']
})
export class PlyrControlsComponent implements OnInit, OnDestroy {
  @Input() roomId: string = '';
  @Input() embedded: boolean = false;
  players: PlyrInstance[] = [];
  private subscription?: Subscription;
  private castVolumes = new Map<string, number>(); // Track cast room volumes separately

  constructor(
    private plyrService: PlyrService,
    private castMediaControlService: CastMediaControlService
  ) {}

  ngOnInit(): void {
    this.updatePlayersList();

    // Subscribe to playing state changes
    this.subscription = this.plyrService.currentlyPlaying$.subscribe(() => {
      this.updatePlayersList();
    });
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  private updatePlayersList(): void {
    this.players = this.plyrService.getAllPlayers();
  }

  getPlayerIcon(type: string): string {
    switch (type) {
      case 'video':
        return 'videocam';
      case 'audio':
        return 'audiotrack';
      case 'youtube':
        return 'smart_display';
      default:
        return 'play_circle';
    }
  }

  isPlaying(playerId: string): boolean {
    return this.plyrService.isPlaying(playerId);
  }

  togglePlayPause(playerId: string): void {
    // Only send commands to cast room, don't affect local player
    if (this.roomId) {
      if (this.isPlaying(playerId)) {
        this.castMediaControlService.sendPauseCommand(this.roomId, playerId);
      } else {
        this.castMediaControlService.sendPlayCommand(this.roomId, playerId);
      }
    }
  }

  seekToStart(playerId: string): void {
    // Only send command to cast room, don't affect local player
    if (this.roomId) {
      const player = this.plyrService
        .getAllPlayers()
        .find(p => p.id === playerId);
      const startTime = player?.element.getAttribute('data-media-start');
      const seekTime = startTime ? parseFloat(startTime) : 0;
      this.castMediaControlService.sendSeekCommand(
        this.roomId,
        playerId,
        seekTime
      );
    }
  }

  getVolume(playerId: string): number {
    const player = this.plyrService.getPlayer(playerId);
    return player ? player.volume : 1;
  }

  setVolume(playerId: string, volume: string | number | null): void {
    if (volume !== null) {
      const numVolume =
        typeof volume === 'string' ? parseFloat(volume) : volume;
      this.plyrService.setVolume(playerId, numVolume);
      // Also send command to cast room
      if (this.roomId) {
        this.castMediaControlService.sendVolumeCommand(
          this.roomId,
          playerId,
          numVolume
        );
      }
    }
  }

  // Cast room volume control
  setCastVolume(playerId: string, volume: string | number | null): void {
    if (volume !== null) {
      const numVolume =
        typeof volume === 'string' ? parseFloat(volume) : volume;
      // Store cast volume locally
      this.castVolumes.set(playerId, numVolume);
      // Send ONLY to cast room
      if (this.roomId) {
        this.castMediaControlService.sendVolumeCommand(
          this.roomId,
          playerId,
          numVolume
        );
      }
    }
  }

  // Get cast room volume
  getCastVolume(playerId: string): number {
    return this.castVolumes.get(playerId) || 1;
  }

  toggleMute(playerId: string): void {
    // Only send command to cast room, don't affect local player
    const wasMuted = this.plyrService.isMuted(playerId);

    if (this.roomId) {
      if (wasMuted) {
        this.castMediaControlService.sendUnmuteCommand(this.roomId, playerId);
      } else {
        this.castMediaControlService.sendMuteCommand(this.roomId, playerId);
      }
    }
  }

  isMuted(playerId: string): boolean {
    return this.plyrService.isMuted(playerId);
  }

  pauseAll(): void {
    // Only send command to cast room, don't affect local player
    if (this.roomId) {
      this.castMediaControlService.sendPauseAllCommand(this.roomId);
    }
  }
}
