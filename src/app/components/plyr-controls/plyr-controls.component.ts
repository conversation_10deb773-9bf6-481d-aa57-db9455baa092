import { Component, OnInit, OnDestroy, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSliderModule } from '@angular/material/slider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { PlyrService, PlyrInstance } from '../../services/plyr.service';
import { CastMediaControlService } from '../../services/cast-media-control.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-plyr-controls',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSliderModule,
    MatTooltipModule
  ],
  templateUrl: './plyr-controls.component.html',
  styleUrls: ['./plyr-controls.component.scss']
})
export class PlyrControlsComponent implements OnInit, OnDestroy {
  @Input() roomId: string = '';
  @Input() embedded: boolean = false;
  players: PlyrInstance[] = [];
  private subscription?: Subscription;
  private castVolumes = new Map<string, number>(); // Track cast room volumes separately
  private castPlayingStates = new Map<string, boolean>(); // Track cast room playing states separately
  private castMutedStates = new Map<string, boolean>(); // Track cast room muted states separately

  constructor(
    private plyrService: PlyrService,
    private castMediaControlService: CastMediaControlService
  ) {}

  ngOnInit(): void {
    this.updatePlayersList();

    // Subscribe to playing state changes
    this.subscription = this.plyrService.currentlyPlaying$.subscribe(() => {
      this.updatePlayersList();
    });
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  private updatePlayersList(): void {
    this.players = this.plyrService.getAllPlayers();
  }

  getPlayerIcon(type: string): string {
    switch (type) {
      case 'video':
        return 'videocam';
      case 'audio':
        return 'audiotrack';
      case 'youtube':
        return 'smart_display';
      default:
        return 'play_circle';
    }
  }

  isPlaying(playerId: string): boolean {
    // Use cast room state, not local player state
    return this.castPlayingStates.get(playerId) || false;
  }

  togglePlayPause(playerId: string): void {
    // Only send commands to cast room, don't affect local player
    if (this.roomId) {
      const isCurrentlyPlaying = this.isPlaying(playerId);
      if (isCurrentlyPlaying) {
        this.castMediaControlService.sendPauseCommand(this.roomId, playerId);
        this.castPlayingStates.set(playerId, false);
      } else {
        this.castMediaControlService.sendPlayCommand(this.roomId, playerId);
        this.castPlayingStates.set(playerId, true);
      }
    }
  }

  seekToStart(playerId: string): void {
    // Only send command to cast room, don't affect local player
    if (this.roomId) {
      const player = this.plyrService
        .getAllPlayers()
        .find(p => p.id === playerId);
      const startTime = player?.element.getAttribute('data-media-start');
      const seekTime = startTime ? parseFloat(startTime) : 0;
      this.castMediaControlService.sendSeekCommand(
        this.roomId,
        playerId,
        seekTime
      );
    }
  }

  getVolume(playerId: string): number {
    const player = this.plyrService.getPlayer(playerId);
    return player ? player.volume : 1;
  }

  setVolume(playerId: string, volume: string | number | null): void {
    if (volume !== null) {
      const numVolume =
        typeof volume === 'string' ? parseFloat(volume) : volume;
      this.plyrService.setVolume(playerId, numVolume);
      // Also send command to cast room
      if (this.roomId) {
        this.castMediaControlService.sendVolumeCommand(
          this.roomId,
          playerId,
          numVolume
        );
      }
    }
  }

  // Cast room volume control
  setCastVolume(playerId: string, volume: string | number | null): void {
    if (volume !== null) {
      const numVolume =
        typeof volume === 'string' ? parseFloat(volume) : volume;
      // Store cast volume locally
      this.castVolumes.set(playerId, numVolume);
      // Send ONLY to cast room
      if (this.roomId) {
        this.castMediaControlService.sendVolumeCommand(
          this.roomId,
          playerId,
          numVolume
        );
      }
    }
  }

  // Get cast room volume
  getCastVolume(playerId: string): number {
    return this.castVolumes.get(playerId) || 1;
  }

  toggleMute(playerId: string): void {
    // Only send command to cast room, don't affect local player
    const wasMuted = this.isMuted(playerId);

    if (this.roomId) {
      if (wasMuted) {
        this.castMediaControlService.sendUnmuteCommand(this.roomId, playerId);
        this.castMutedStates.set(playerId, false);
      } else {
        this.castMediaControlService.sendMuteCommand(this.roomId, playerId);
        this.castMutedStates.set(playerId, true);
      }
    }
  }

  isMuted(playerId: string): boolean {
    // Use cast room state, not local player state
    return this.castMutedStates.get(playerId) || false;
  }

  pauseAll(): void {
    // Only send command to cast room, don't affect local player
    if (this.roomId) {
      this.castMediaControlService.sendPauseAllCommand(this.roomId);
    }
  }
}
