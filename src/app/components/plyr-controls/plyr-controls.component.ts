import { Component, <PERSON>Init, OnD<PERSON>roy, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSliderModule } from '@angular/material/slider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { PlyrService, PlyrInstance } from '../../services/plyr.service';
import { CastMediaControlService } from '../../services/cast-media-control.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-plyr-controls',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSliderModule,
    MatTooltipModule
  ],
  template: `
    <mat-card class="plyr-controls-card" *ngIf="players.length > 0">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>smart_display</mat-icon>
          Media Controls
        </mat-card-title>
        <div class="header-actions">
          <button
            mat-icon-button
            (click)="pauseAll()"
            matTooltip="Pause All Media"
            class="pause-all-button"
          >
            <mat-icon>pause</mat-icon>
          </button>
        </div>
      </mat-card-header>

      <mat-card-content>
        <div class="players-list">
          <div
            class="player-item"
            *ngFor="let player of players"
            [class.playing]="isPlaying(player.id)"
          >
            <div class="player-info">
              <mat-icon class="player-type-icon">
                {{ getPlayerIcon(player.type) }}
              </mat-icon>
              <span class="player-title">{{ player.title }}</span>
            </div>

            <div class="player-controls">
              <button
                mat-icon-button
                (click)="togglePlayPause(player.id)"
                [matTooltip]="isPlaying(player.id) ? 'Pause' : 'Play'"
                class="play-pause-button"
              >
                <mat-icon>
                  {{ isPlaying(player.id) ? 'pause' : 'play_arrow' }}
                </mat-icon>
              </button>

              <button
                mat-icon-button
                (click)="seekToStart(player.id)"
                matTooltip="Restart"
                class="restart-button"
              >
                <mat-icon>replay</mat-icon>
              </button>

              <div class="volume-control" *ngIf="player.type !== 'youtube'">
                <mat-icon class="volume-icon">volume_up</mat-icon>
                <input
                  type="range"
                  class="volume-slider"
                  [min]="0"
                  [max]="1"
                  [step]="0.1"
                  [value]="getVolume(player.id)"
                  (input)="setVolume(player.id, $any($event.target).value)"
                  matTooltip="Volume"
                />
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  `,
  styles: [
    `
      .plyr-controls-card {
        background: var(--mat-sys-surface-container);
        border: 1px solid var(--mat-sys-outline-variant);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        overflow: hidden;
      }

      mat-card-header {
        background: var(--mat-sys-surface-container-high);
        padding: 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      mat-card-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font: var(--mat-sys-title-medium);
        color: var(--mat-sys-on-surface);
        margin: 0;
      }

      .header-actions {
        display: flex;
        gap: 0.5rem;
      }

      .pause-all-button {
        background: var(--mat-sys-error-container);
        color: var(--mat-sys-on-error-container);
      }

      mat-card-content {
        padding: 0;
      }

      .players-list {
        display: flex;
        flex-direction: column;
        gap: 0;
      }

      .player-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--mat-sys-outline-variant);
        transition: background-color 0.2s ease;
      }

      .player-item:last-child {
        border-bottom: none;
      }

      .player-item.playing {
        background: var(--mat-sys-primary-container);
        color: var(--mat-sys-on-primary-container);
      }

      .player-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex: 1;
        min-width: 0;
      }

      .player-type-icon {
        color: var(--mat-sys-on-surface-variant);
        font-size: 20px;
        width: 20px;
        height: 20px;
      }

      .player-item.playing .player-type-icon {
        color: var(--mat-sys-on-primary-container);
      }

      .player-title {
        font: var(--mat-sys-body-medium);
        color: var(--mat-sys-on-surface);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .player-item.playing .player-title {
        color: var(--mat-sys-on-primary-container);
        font-weight: 500;
      }

      .player-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .play-pause-button {
        background: var(--mat-sys-primary);
        color: var(--mat-sys-on-primary);
      }

      .player-item.playing .play-pause-button {
        background: var(--mat-sys-secondary);
        color: var(--mat-sys-on-secondary);
      }

      .restart-button {
        color: var(--mat-sys-on-surface-variant);
      }

      .volume-control {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        min-width: 100px;
      }

      .volume-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
        color: var(--mat-sys-on-surface-variant);
      }

      .volume-slider {
        flex: 1;
        min-width: 60px;
      }

      /* Mobile optimizations */
      @media (max-width: 600px) {
        .player-item {
          flex-direction: column;
          align-items: stretch;
          gap: 0.5rem;
        }

        .player-controls {
          justify-content: space-between;
        }

        .volume-control {
          min-width: auto;
          flex: 1;
        }
      }
    `
  ]
})
export class PlyrControlsComponent implements OnInit, OnDestroy {
  @Input() roomId: string = '';
  players: PlyrInstance[] = [];
  private subscription?: Subscription;

  constructor(
    private plyrService: PlyrService,
    private castMediaControlService: CastMediaControlService
  ) {}

  ngOnInit(): void {
    this.updatePlayersList();

    // Subscribe to playing state changes
    this.subscription = this.plyrService.currentlyPlaying$.subscribe(() => {
      this.updatePlayersList();
    });
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  private updatePlayersList(): void {
    this.players = this.plyrService.getAllPlayers();
  }

  getPlayerIcon(type: string): string {
    switch (type) {
      case 'video':
        return 'videocam';
      case 'audio':
        return 'audiotrack';
      case 'youtube':
        return 'smart_display';
      default:
        return 'play_circle';
    }
  }

  isPlaying(playerId: string): boolean {
    return this.plyrService.isPlaying(playerId);
  }

  togglePlayPause(playerId: string): void {
    if (this.isPlaying(playerId)) {
      this.plyrService.pause(playerId);
      // Also send command to cast room
      if (this.roomId) {
        this.castMediaControlService.sendPauseCommand(this.roomId, playerId);
      }
    } else {
      this.plyrService.play(playerId);
      // Also send command to cast room
      if (this.roomId) {
        this.castMediaControlService.sendPlayCommand(this.roomId, playerId);
      }
    }
  }

  seekToStart(playerId: string): void {
    this.plyrService.seek(playerId, 0);
    // Also send command to cast room
    if (this.roomId) {
      this.castMediaControlService.sendSeekCommand(this.roomId, playerId, 0);
    }
  }

  getVolume(playerId: string): number {
    const player = this.plyrService.getPlayer(playerId);
    return player ? player.volume : 1;
  }

  setVolume(playerId: string, volume: string | number | null): void {
    if (volume !== null) {
      const numVolume =
        typeof volume === 'string' ? parseFloat(volume) : volume;
      this.plyrService.setVolume(playerId, numVolume);
      // Also send command to cast room
      if (this.roomId) {
        this.castMediaControlService.sendVolumeCommand(
          this.roomId,
          playerId,
          numVolume
        );
      }
    }
  }

  pauseAll(): void {
    this.plyrService.pauseAll();
    // Also send command to cast room
    if (this.roomId) {
      this.castMediaControlService.sendPauseAllCommand(this.roomId);
    }
  }
}
