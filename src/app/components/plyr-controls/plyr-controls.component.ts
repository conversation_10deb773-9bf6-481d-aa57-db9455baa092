import { Component, <PERSON>Ini<PERSON>, On<PERSON><PERSON>roy, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSliderModule } from '@angular/material/slider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { PlyrService, PlyrInstance } from '../../services/plyr.service';
import { CastMediaControlService } from '../../services/cast-media-control.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-plyr-controls',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSliderModule,
    MatTooltipModule
  ],
  template: `
    <mat-card
      class="plyr-controls-card"
      [class.embedded]="embedded"
      *ngIf="players.length > 0"
    >
      <mat-card-header>
        <mat-card-title>
          <mat-icon>smart_display</mat-icon>
          Cast Room Controls
        </mat-card-title>
        <div class="header-actions">
          <button
            mat-icon-button
            (click)="pauseAll()"
            matTooltip="Pause All Media"
            class="pause-all-button"
          >
            <mat-icon>pause</mat-icon>
          </button>
        </div>
      </mat-card-header>

      <mat-card-content>
        <div class="players-list">
          <div
            class="player-item"
            *ngFor="let player of players"
            [class.playing]="isPlaying(player.id)"
          >
            <div class="player-info">
              <mat-icon class="player-type-icon">
                {{ getPlayerIcon(player.type) }}
              </mat-icon>
              <span class="player-title">{{ player.title }}</span>
            </div>

            <div class="player-controls">
              <button
                mat-icon-button
                (click)="togglePlayPause(player.id)"
                [matTooltip]="isPlaying(player.id) ? 'Pause' : 'Play'"
                class="play-pause-button"
              >
                <mat-icon>
                  {{ isPlaying(player.id) ? 'pause' : 'play_arrow' }}
                </mat-icon>
              </button>

              <button
                mat-icon-button
                (click)="seekToStart(player.id)"
                matTooltip="Restart"
                class="restart-button"
              >
                <mat-icon>replay</mat-icon>
              </button>

              <button
                mat-icon-button
                (click)="toggleMute(player.id)"
                [matTooltip]="isMuted(player.id) ? 'Unmute' : 'Mute'"
                class="mute-button"
                [class.muted]="isMuted(player.id)"
              >
                <mat-icon>
                  {{ isMuted(player.id) ? 'volume_off' : 'volume_up' }}
                </mat-icon>
              </button>
              <div class="volume-controls" *ngIf="player.type !== 'youtube'">
                <!-- Local Volume (GameMaster) -->
                <div class="volume-control local-volume">
                  <mat-icon class="volume-icon">volume_up</mat-icon>
                  <span class="volume-label">Local</span>
                  <input
                    type="range"
                    class="volume-slider"
                    [min]="0"
                    [max]="1"
                    [step]="0.1"
                    [value]="getVolume(player.id)"
                    (input)="
                      setLocalVolume(player.id, $any($event.target).value)
                    "
                    matTooltip="Local Volume (GameMaster)"
                  />
                </div>

                <!-- Cast Room Volume (if not embedded) -->
                <div class="volume-control cast-volume" *ngIf="!embedded">
                  <mat-icon class="volume-icon">cast</mat-icon>
                  <span class="volume-label">Cast</span>
                  <input
                    type="range"
                    class="volume-slider"
                    [min]="0"
                    [max]="1"
                    [step]="0.1"
                    [value]="getCastVolume(player.id)"
                    (input)="
                      setCastVolume(player.id, $any($event.target).value)
                    "
                    matTooltip="Cast Room Volume"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  `,
  styles: [
    `
      .plyr-controls-card {
        background: var(--mat-sys-surface-container);
        border: 1px solid var(--mat-sys-outline-variant);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        overflow: hidden;
      }

      .plyr-controls-card.embedded {
        background: var(--mat-sys-surface-container-low);
        border: 1px solid var(--mat-sys-outline-variant);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        border-radius: 8px;
        margin-top: 0.5rem;
      }

      mat-card-header {
        background: var(--mat-sys-surface-container-high);
        padding: 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      mat-card-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font: var(--mat-sys-title-medium);
        color: var(--mat-sys-on-surface);
        margin: 0;
      }

      .header-actions {
        display: flex;
        gap: 0.5rem;
      }

      .pause-all-button {
        background: var(--mat-sys-error-container);
        color: var(--mat-sys-on-error-container);
      }

      mat-card-content {
        padding: 0;
      }

      .players-list {
        display: flex;
        flex-direction: column;
        gap: 0;
      }

      .player-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--mat-sys-outline-variant);
        transition: background-color 0.2s ease;
        min-height: 60px;
      }

      .player-item:last-child {
        border-bottom: none;
      }

      .player-item.playing {
        background: var(--mat-sys-primary-container);
        color: var(--mat-sys-on-primary-container);
      }

      .player-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex: 1;
        min-width: 0;
      }

      .player-type-icon {
        color: var(--mat-sys-on-surface-variant);
        font-size: 20px;
        width: 20px;
        height: 20px;
      }

      .player-item.playing .player-type-icon {
        color: var(--mat-sys-on-primary-container);
      }

      .player-title {
        font: var(--mat-sys-body-medium);
        color: var(--mat-sys-on-surface);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .player-item.playing .player-title {
        color: var(--mat-sys-on-primary-container);
        font-weight: 500;
      }

      .player-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex-shrink: 0;
      }

      .play-pause-button {
        background: var(--mat-sys-primary);
        color: var(--mat-sys-on-primary);
      }

      .player-item.playing .play-pause-button {
        background: var(--mat-sys-secondary);
        color: var(--mat-sys-on-secondary);
      }

      .restart-button {
        color: var(--mat-sys-on-surface-variant);
      }

      .mute-button {
        color: var(--mat-sys-on-surface-variant);
      }

      .mute-button.muted {
        color: var(--mat-sys-error);
      }

      .volume-controls {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        min-width: 140px;
        max-width: 140px;
      }

      .volume-control {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        min-height: 24px;
      }

      .volume-icon {
        font-size: 14px;
        width: 14px;
        height: 14px;
        color: var(--mat-sys-on-surface-variant);
        flex-shrink: 0;
      }

      .volume-label {
        font-size: 9px;
        color: var(--mat-sys-on-surface-variant);
        min-width: 24px;
        text-align: left;
        flex-shrink: 0;
      }

      .volume-slider {
        flex: 1;
        min-width: 60px;
        height: 4px;
        margin: 0;
      }

      .local-volume .volume-icon {
        color: var(--mat-sys-primary);
      }

      .cast-volume .volume-icon {
        color: var(--mat-sys-secondary);
      }

      /* Mobile optimizations */
      @media (max-width: 600px) {
        .player-item {
          flex-direction: column;
          align-items: stretch;
          gap: 0.5rem;
          padding: 0.5rem;
        }

        .player-controls {
          justify-content: space-between;
          flex-wrap: wrap;
          gap: 0.25rem;
        }

        .volume-controls {
          min-width: auto;
          max-width: none;
          flex: 1;
          flex-direction: row;
          gap: 0.5rem;
        }

        .volume-control {
          min-width: auto;
          flex: 1;
        }

        .volume-label {
          font-size: 8px;
          min-width: 20px;
        }

        .volume-slider {
          min-width: 40px;
        }
      }
    `
  ]
})
export class PlyrControlsComponent implements OnInit, OnDestroy {
  @Input() roomId: string = '';
  @Input() embedded: boolean = false;
  players: PlyrInstance[] = [];
  private subscription?: Subscription;
  private castVolumes = new Map<string, number>(); // Track cast room volumes separately

  constructor(
    private plyrService: PlyrService,
    private castMediaControlService: CastMediaControlService
  ) {}

  ngOnInit(): void {
    this.updatePlayersList();

    // Subscribe to playing state changes
    this.subscription = this.plyrService.currentlyPlaying$.subscribe(() => {
      this.updatePlayersList();
    });
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  private updatePlayersList(): void {
    this.players = this.plyrService.getAllPlayers();
  }

  getPlayerIcon(type: string): string {
    switch (type) {
      case 'video':
        return 'videocam';
      case 'audio':
        return 'audiotrack';
      case 'youtube':
        return 'smart_display';
      default:
        return 'play_circle';
    }
  }

  isPlaying(playerId: string): boolean {
    return this.plyrService.isPlaying(playerId);
  }

  togglePlayPause(playerId: string): void {
    // Only send commands to cast room, don't affect local player
    if (this.roomId) {
      if (this.isPlaying(playerId)) {
        this.castMediaControlService.sendPauseCommand(this.roomId, playerId);
      } else {
        this.castMediaControlService.sendPlayCommand(this.roomId, playerId);
      }
    }
  }

  seekToStart(playerId: string): void {
    // Only send command to cast room, don't affect local player
    if (this.roomId) {
      const player = this.plyrService
        .getAllPlayers()
        .find(p => p.id === playerId);
      const startTime = player?.element.getAttribute('data-media-start');
      const seekTime = startTime ? parseFloat(startTime) : 0;
      this.castMediaControlService.sendSeekCommand(
        this.roomId,
        playerId,
        seekTime
      );
    }
  }

  getVolume(playerId: string): number {
    const player = this.plyrService.getPlayer(playerId);
    return player ? player.volume : 1;
  }

  setVolume(playerId: string, volume: string | number | null): void {
    if (volume !== null) {
      const numVolume =
        typeof volume === 'string' ? parseFloat(volume) : volume;
      this.plyrService.setVolume(playerId, numVolume);
      // Also send command to cast room
      if (this.roomId) {
        this.castMediaControlService.sendVolumeCommand(
          this.roomId,
          playerId,
          numVolume
        );
      }
    }
  }

  // Local volume control (GameMaster only)
  setLocalVolume(playerId: string, volume: string | number | null): void {
    if (volume !== null) {
      const numVolume =
        typeof volume === 'string' ? parseFloat(volume) : volume;
      this.plyrService.setVolume(playerId, numVolume);
      // Do NOT send to cast room - local only
    }
  }

  // Cast room volume control
  setCastVolume(playerId: string, volume: string | number | null): void {
    if (volume !== null) {
      const numVolume =
        typeof volume === 'string' ? parseFloat(volume) : volume;
      // Store cast volume locally
      this.castVolumes.set(playerId, numVolume);
      // Send ONLY to cast room
      if (this.roomId) {
        this.castMediaControlService.sendVolumeCommand(
          this.roomId,
          playerId,
          numVolume
        );
      }
    }
  }

  // Get cast room volume
  getCastVolume(playerId: string): number {
    return this.castVolumes.get(playerId) || 1;
  }

  toggleMute(playerId: string): void {
    // Only send command to cast room, don't affect local player
    const wasMuted = this.plyrService.isMuted(playerId);

    if (this.roomId) {
      if (wasMuted) {
        this.castMediaControlService.sendUnmuteCommand(this.roomId, playerId);
      } else {
        this.castMediaControlService.sendMuteCommand(this.roomId, playerId);
      }
    }
  }

  isMuted(playerId: string): boolean {
    return this.plyrService.isMuted(playerId);
  }

  pauseAll(): void {
    // Only send command to cast room, don't affect local player
    if (this.roomId) {
      this.castMediaControlService.sendPauseAllCommand(this.roomId);
    }
  }
}
