// This file was generated by running 'ng generate @angular/material:theme-color'.
// Proceed with caution if making changes to this file.

@use 'sass:map';
@use '@angular/material' as mat;

// Note: Color palettes are generated from primary: 005cbb
$_palettes: (
  primary: (
    0: #000000,
    10: #001b3f,
    20: #002f66,
    25: #003a7a,
    30: #00458f,
    35: #0050a5,
    40: #005cbb,
    50: #3376d6,
    60: #5290f2,
    70: #7cabff,
    80: #abc7ff,
    90: #d7e2ff,
    95: #ecf0ff,
    98: #f9f9ff,
    99: #fdfbff,
    100: #ffffff,
  ),
  secondary: (
    0: #000000,
    10: #021b3d,
    20: #1b3053,
    25: #273b5f,
    30: #33476b,
    35: #3e5378,
    40: #4b5f84,
    50: #63779e,
    60: #7d91ba,
    70: #97acd5,
    80: #b2c7f2,
    90: #d7e2ff,
    95: #ecf0ff,
    98: #f9f9ff,
    99: #fdfbff,
    100: #ffffff,
  ),
  tertiary: (
    0: #000000,
    10: #330045,
    20: #52046d,
    25: #5f1779,
    30: #6b2686,
    35: #793393,
    40: #8640a0,
    50: #a15abb,
    60: #bd73d7,
    70: #da8ef4,
    80: #edb1ff,
    90: #f9d8ff,
    95: #ffebff,
    98: #fff7fb,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #191c22,
    20: #2e3037,
    25: #393b42,
    30: #44474d,
    35: #505259,
    40: #5c5e65,
    50: #75777e,
    60: #8f9098,
    70: #a9abb3,
    80: #c5c6ce,
    90: #e1e2ea,
    95: #eff0f9,
    98: #f9f9ff,
    99: #fdfbff,
    100: #ffffff,
    4: #0b0e14,
    6: #111319,
    12: #1d2026,
    17: #272a30,
    22: #32353b,
    24: #373940,
    87: #d8d9e2,
    92: #e7e8f0,
    94: #ecedf6,
    96: #f2f3fc,
  ),
  neutral-variant: (
    0: #000000,
    10: #171c26,
    20: #2b303b,
    25: #363b47,
    30: #424752,
    35: #4d525e,
    40: #595e6a,
    50: #727784,
    60: #8c919e,
    70: #a6abb9,
    80: #c2c6d4,
    90: #dee2f1,
    95: #ecf0ff,
    98: #f9f9ff,
    99: #fdfbff,
    100: #ffffff,
  ),
  error: (
    0: #000000,
    10: #410002,
    20: #690005,
    25: #7e0007,
    30: #93000a,
    35: #a80710,
    40: #ba1a1a,
    50: #de3730,
    60: #ff5449,
    70: #ff897d,
    80: #ffb4ab,
    90: #ffdad6,
    95: #ffedea,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
);

$_rest: (
  secondary: map.get($_palettes, secondary),
  neutral: map.get($_palettes, neutral),
  neutral-variant: map.get($_palettes,  neutral-variant),
  error: map.get($_palettes, error),
);

$primary-palette: map.merge(map.get($_palettes, primary), $_rest);
$tertiary-palette: map.merge(map.get($_palettes, tertiary), $_rest);